package com.fzzixun.appstore.aliexpress.facade.controller;

import com.fzzixun.appstore.aliexpress.application.service.ExpressApiService;
import com.fzzixun.appstore.aliexpress.base.infrastructure.annotation.WebLog;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.global.iop.util.ApiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@WebLog
@RequestMapping("/ali-express-auth/rest/v1/auth")
@RestController
@Api(tags = "aliExpress授权")
@RequiredArgsConstructor
public class AuthController {

    private final ExpressApiService expressApiService;

    /**
     * @param state
     * @param channel
     * @return
     */
    @GetMapping("/url")
    @ApiOperation("获取授权地址")
    public ResponseVo<?> authUrl(@RequestParam String state,
                                 @RequestParam String channel) {
        return expressApiService.getAuthUrl(state, channel);
    }

    @GetMapping("/finish")
    @ApiOperation("接收code")
    public ResponseVo<?> receiveCode(@RequestParam String channel,
                                     @RequestParam String code,
                                     @RequestParam String state) throws ApiException {
        return expressApiService.createToken(channel, code, state);
    }

    @PostMapping("/safe-add-auth")
    @ApiOperation("safe-add-auth")
    public ResponseVo<?> addAuth(@RequestParam String channel,
                                 @RequestParam String sellerId,
                                 @RequestParam String callbackUrl) throws ApiException {
        return expressApiService.addAuth(channel, sellerId, callbackUrl);
    }
}
