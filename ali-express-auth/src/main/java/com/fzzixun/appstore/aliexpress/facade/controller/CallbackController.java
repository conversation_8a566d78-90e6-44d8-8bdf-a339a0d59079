package com.fzzixun.appstore.aliexpress.facade.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.aliexpress.base.infrastructure.annotation.WebLog;
import com.fzzixun.appstore.aliexpress.base.infrastructure.exception.ForbiddenException;
import com.fzzixun.appstore.aliexpress.base.infrastructure.util.MdcUtil;
import com.fzzixun.appstore.aliexpress.base.infrastructure.util.SignatureUtil;
import com.fzzixun.appstore.aliexpress.producer.KafkaOpt;
import com.fzzixun.appstore.aliexpress.base.infrastructure.config.ExpresssClientConfig;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@WebLog
@RequestMapping("/ali-express-auth/rest/v1/callback")
@RestController
@Api(tags = "事件推送")
@RequiredArgsConstructor
public class CallbackController {

    private final ExpresssClientConfig expressClientConfig;
    private final KafkaOpt kafkaOpt;

    @PostMapping("/receive")
    @ApiOperation("回调接收")
    public ResponseVo<?> forwardRequest(HttpServletRequest request, @RequestBody String body) {
        String authorization = request.getHeader("authorization");
        if (StringUtils.isEmpty(authorization)) {
            throw new ForbiddenException("authorization is null");
        }
        // 签名验证
        String base = expressClientConfig.getAppKey() + body;
        String signature = SignatureUtil.getSignature(base, expressClientConfig.getSecret());
        if (!authorization.equals(signature)) {
            log.error("签名验证失败, authorization:{}, body:{}", authorization, body);
            throw new ForbiddenException("签名验证失败");
        }

        JSONObject jsonBody = JSON.parseObject(body);
        String sellerId = jsonBody.getString("seller_id");
        // todo 回调给商家
        kafkaOpt.send(body);
        return ResponseVo.ofSuccessWithId(MdcUtil.getRequestId());
    }
}
