package com.fzzixun.appstore.aliexpress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @description MicroApplication
 * @date 2022/1/26 13:58
 */
@Slf4j
@EnableJpaAuditing
@EnableSwagger2
@EnableCaching
@SpringBootApplication(scanBasePackageClasses = {ExpressApplication.class, AliExpressConfig.class})
public class ExpressApplication {
    /**
     * 应用上下文
     */
    private static ConfigurableApplicationContext ctx;

    /**
     * 打印文档地址
     */
    private static String getDocAddress() {
        try {
            String host = InetAddress.getLocalHost().getHostAddress();
            TomcatServletWebServerFactory tomcatServletWebServerFactory = (TomcatServletWebServerFactory) ctx
                    .getBean("tomcatServletWebServerFactory");
            int port = tomcatServletWebServerFactory.getPort();
            String contextPath = tomcatServletWebServerFactory.getContextPath();
            return "http://" + host + ":" + port + contextPath + "/doc.html";
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return "文档地址获取失败";
        }
    }

    /**
     * 启动方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        ctx = SpringApplication.run(ExpressApplication.class, args);
        log.info("系统启动成功, 接口文档: " + getDocAddress());
    }
}
