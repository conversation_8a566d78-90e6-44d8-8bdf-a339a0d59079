package com.fzzixun.appstore.aliexpress.application.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.aliexpress.base.infrastructure.config.ExpresssClientConfig;
import com.fzzixun.appstore.aliexpress.base.infrastructure.util.MdcUtil;
import com.fzzixun.appstore.aliexpress.service.CallbackService;
import com.fzzixun.appstore.aliexpress.service.SellerInfoService;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import com.global.iop.util.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExpressApiService {

    @Value("${ali-express.api.response.log.enable:false}")
    private Boolean logEnable;


    private final IopClient iopClient;
    private final CallbackService callbackService;
    private final SellerInfoService sellerInfoService;
    private final ExpresssClientConfig expressClientConfig;

    public ResponseVo<?> createToken(String channel, String code, String callbackUrl) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("/auth/token/create");

        request.addApiParameter("code", code);
        //request.addApiParameter("uuid", MdcUtil.getRequestId());
        IopResponse response = iopClient.execute(request, Protocol.GOP);
        String responseBody = response.getBody();
        JSONObject tokenResponse = JSON.parseObject(responseBody);
        String accessToken = tokenResponse.getString("access_token");
        if (StringUtils.isEmpty(accessToken)) {
            log.info("token生成失败，response:{}", responseBody);
            return ResponseVo.ofBadRequestWithId("create token error：" + tokenResponse.getString("message"), MdcUtil.getRequestId());
        }
        String sellerId = tokenResponse.getString("seller_id");
        //绑定商家和sellerId
        sellerInfoService.bindMerchantSellerId(channel, sellerId, callbackUrl);
        //推送令牌
        callbackService.sendCallback("aliexpress_" + sellerId, callbackUrl, responseBody, Constant.CALLBACK_MESSAGE_TYPE_TOKEN);
        return ResponseVo.ofSuccessWithId(MdcUtil.getRequestId());
    }

    public ResponseVo<?> getAuthUrl(String state, String channel) {
        String authUrl;
        try {
            authUrl = String.format("%s?response_type=code&force_auth=true&redirect_uri=%s&client_id=%s&state=%s",
                    expressClientConfig.getAuthUrl(), URLEncoder.encode(expressClientConfig.getRedirectUri() + "?channel=" + channel, "UTF-8"),
                    expressClientConfig.getAppKey(), URLEncoder.encode(state, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            return ResponseVo.ofBadRequestWithId("get auth url error :" + e.getMessage(), MdcUtil.getRequestId());
        }
        ResponseVo<String> responseVo = ResponseVo.ofSuccess(authUrl);
        responseVo.setId(MdcUtil.getRequestId());
        return responseVo;
    }

    public ResponseVo<?> addAuth(String channel, String sellerId, String callbackUrl) {
        sellerInfoService.addAliExpressAuth(channel, sellerId, callbackUrl);
        return ResponseVo.ofSuccess();
    }
}
