# 调试配置
server.port=8082
management.endpoints.web.exposure.include=*
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.type.descriptor.sql.BasicBinder = trace
swagger.enable = true
# 数据库
spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM
# 数据库-hubstudio
#spring.datasource.url=****************************************************************************************************************************************************************************
#spring.datasource.username=appstore
#spring.datasource.password=wFaoLplMahfSI5xC
# redis
spring.redis.host = appstore-redis-main-test.fzzixun.com
spring.redis.port = 6379
spring.redis.timeout = 60000
spring.redis.database = 0
spring.redis.password = ZodEPYCZnNTjWySR
spring.redis.jedis.pool.min-idle = 1
spring.redis.jedis.pool.max-idle = 5
spring.redis.jedis.pool.max-wait = -1
spring.redis.jedis.pool.max-active = 8

# DEBUG
logging.level.org.springframework.data.redis = TRACE

# 速卖通
aliexpress.api.appkey = 501312
aliexpress.api.secret = KJo8xJpV0hiMU83aC4OHfKe2pu8DlXIp

ali-express.api.response.log.enable = true
param.log.debug = true
# kafka
aliexpress.groupid = openapi-aliexpress-callback
kafka.listener_topics = aliexpress-callback

#生产者配置
spring.kafka.producer.bootstrap-servers=b-1.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096,b-2.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096
spring.kafka.producer.properties.sasl.mechanism=SCRAM-SHA-512
spring.kafka.producer.properties.security.protocol=SASL_SSL
spring.kafka.producer.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="appstore" password="appstore123";
# 批量大小 默认16k
spring.kafka.producer.batch-size=325000
# 提交延时 默认0
spring.kafka.producer.properties.linger.ms=5000

#消费者配置
spring.kafka.consumer.bootstrap-servers = b-1.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096,b-2.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096
spring.kafka.consumer.properties.sasl.mechanism = SCRAM-SHA-512
spring.kafka.consumer.properties.security.protocol = SASL_SSL
spring.kafka.consumer.properties.sasl.jaas.config = org.apache.kafka.common.security.scram.ScramLoginModule required username="appstore" password="appstore123";
#spring.kafka.consumer.group-id=
spring.kafka.consumer.auto-offset-reset = earliest
spring.kafka.consumer.enable-auto-commit = false
spring.kafka.consumer.auto-commit-interval = 1000
spring.kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.max-poll-records = 500
#如果没有足够的数据立即满足“fetch.min.bytes”给出的要求，服务器在回答获取请求之前将阻塞的最长时间（以毫秒为单位）
#默认值为500
spring.kafka.consumer.fetch-max-wait = 5000
#服务器应以字节为单位返回获取请求的最小数据量，默认值为1，对应的kafka的参数为fetch.min.bytes。
spring.kafka.consumer.fetch-min-size = 65000
spring.kafka.listener.type = batch
spring.kafka.listener.concurrency = 3