server.port=80
apollo.bootstrap.enabled=true
# db
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=${APPSTORE_MYSQL_SOP_USERNAME}
spring.datasource.password=${APPSTORE_MYSQL_SOP_PASSWORD}
spring.datasource.url=jdbc:mysql://${APPSTORE_MYSQL_SOP_URL}:\
  ${APPSTORE_MYSQL_SOP_PORT}/\
  ${APPSTORE_MYSQL_SOP_DBNAME}?\
  useSSL=false&\
  useUnicode=true&\
  characterEncoding=utf-8&\
  zeroDateTimeBehavior=convertToNull&\
  serverTimezone=Asia/Shanghai
# redis
#spring.redis.host=${APPSTORE_REDIS_CACHE_URL}
#spring.redis.port=${APPSTORE_REDIS_CACHE_PORT}
#spring.redis.database=${APPSTORE_REDIS_CACHE_DBNAME}
#spring.redis.password=${APPSTORE_REDIS_CACHE_PASSWORD}
#spring.redis.jedis.pool.min-idle=1
#spring.redis.jedis.pool.max-idle=5
#spring.redis.jedis.pool.max-wait=-1
#spring.redis.jedis.pool.max-active=8
#spring.redis.timeout=60000
