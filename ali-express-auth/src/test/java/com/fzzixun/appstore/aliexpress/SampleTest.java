package com.fzzixun.appstore.aliexpress;

import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import com.global.iop.util.Constants;
import com.global.iop.util.FileItem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SampleTest {

  private IopClient iopClient;
  private String token;

	@BeforeEach
	public void init() {
		iopClient = new IopClientImpl("https://api-sg.aliexpress.com", "appkey", "secret");
		token = "token";
	}

	@Test
	public void testQuery() throws Exception {
		IopRequest iopRequest = new IopRequest("aliexpress.photobank.redefining.queryphotobankimagebypaths");
		iopRequest.setHttpMethod(Constants.METHOD_POST);
		iopRequest.addApiParameter("paths", "a.jpg");
        //tip：请注意simplify为请求公共参数，在设置该参数时请作为一级参数单独设置，不要作为任意子参数放到业务参数下（即单独使用下面一行代码）
        iopRequest.addApiParameter("simplify", "true");
		IopResponse rsp = iopClient.execute(iopRequest, token, Protocol.TOP);
		System.out.println(rsp.getBody());
	}

	@Test
	public void testUpload() throws Exception {
		IopRequest iopRequest = new IopRequest("aliexpress.image.redefining.uploadtempimage");
		iopRequest.setHttpMethod(Constants.METHOD_POST);
		iopRequest.addFileParameter("file_data", new FileItem("/Users/<USER>/a.png"));
		iopRequest.addApiParameter("src_file_name", "testFile");
        iopRequest.addApiParameter("simplify", "true");
		// 如果是TOP协议下的老api 则选择top协议，apiName以'.'为分隔符的是TOP协议 比如aliexpress.image.redefining.uploadtempimage
		// 如果是在新开放平台(GOP)配置的api，则选择GOP协议，apiName以'/'为分隔符
		// 具体某个API属于哪一种协议请参考每个API页面的sample code
		IopResponse rsp = iopClient.execute(iopRequest, token, Protocol.TOP);
		System.out.println(rsp.getBody());
	}
    
    
	@Test
	public void testGopTraditionalApi() throws Exception {
		IopRequest iopRequest = new IopRequest("/globalopen/api/get/a");
		iopRequest.setHttpMethod(Constants.METHOD_GET);
		iopRequest.addApiParameter("apiId", "1");
        iopRequest.addApiParameter("simplify", "true");
		IopResponse rsp = iopClient.execute(iopRequest, Protocol.GOP);
		System.out.println(rsp.getBody());
	}
    
	@Test
	public void testGopRestfulApi() throws Exception {
		// path参数
		IopRequest iopRequest = new IopRequest("/globalopen/api/getApiDsl/{region}/{env}/{apiId}");
		iopRequest.setHttpMethod(Constants.METHOD_PUT);
		iopRequest.addApiParameter("region", "global");
		iopRequest.addApiParameter("env", "PRE");
		iopRequest.addApiParameter("apiId", "1");
        iopRequest.addApiParameter("simplify", "true");
		IopResponse rsp = iopClient.execute(iopRequest, token, Protocol.REST_VND_2);
		System.out.println(rsp.getBody());
	}

}