package com.fzzixun.appstore.aliexpress;

import com.fzzixun.appstore.aliexpress.service.SellerInfoService;
import com.fzzixun.appstore.aliexpress.dao.entity.AliExpressAuth;
import com.fzzixun.appstore.aliexpress.dao.repository.AliExpressAuthRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-07
 */
@Slf4j
@SpringBootTest(classes = ExpressApplication.class)
@ActiveProfiles("local")
public class ExpressAuthTest {

    @Autowired
    private AliExpressAuthRepository aliExpressAuthRepository;

    @Autowired
    private SellerInfoService service;

    @Test
    void queryAuth() {
        List<AliExpressAuth> aliExpressAuthList = aliExpressAuthRepository.findBySellerId("9999");
        //System.out.println(aliExpressAuthList);

        Map<String, List<AliExpressAuth>> map = aliExpressAuthList.stream()
                .collect(Collectors.groupingBy(AliExpressAuth::getChannel));

        map.forEach((k, v) -> {
            System.out.println(k);
            System.out.println(v.get(0).getCallbackUrl());
        });
    }

    @Test
    void queryAuths() {
        service.getCallbackUrl("231384875");
    }
}
