<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.fzzixun.appstore.spapi</groupId>
        <artifactId>spapi-auth-proxy</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>ali-express-auth</artifactId>

    <properties>
        <java.version>1.8</java.version>
        <apollo.version>1.1.0</apollo.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- springboot 版本-->
        <spring-boot.version>2.5.1</spring-boot.version>
        <!-- spring cloud 版本 -->
        <spring-cloud.version>2020.0.6</spring-cloud.version>
        <logback.version>1.2.3</logback.version>
        <junit.version>4.11</junit.version>
        <fastjson.version>1.2.83</fastjson.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-fileupload.version>1.3.3</commons-fileupload.version>
        <commons-collection.version>3.2.2</commons-collection.version>
        <commons-lang3.version>3.8.1</commons-lang3.version>
        <commons-codec.version>1.11</commons-codec.version>
        <commons-logging.version>1.2</commons-logging.version>
        <hibernate-validator.version>6.0.13.Final</hibernate-validator.version>
        <fastmybatis.version>1.9.1</fastmybatis.version>
        <spring-data-redis.version>2.3.0.RELEASE</spring-data-redis.version>
        <guava.version>29.0-jre</guava.version>
        <swagger.version>1.5.21</swagger.version>
        <springfox-spring-web.version>2.9.2</springfox-spring-web.version>
        <springfox-swagger2.version>2.9.2</springfox-swagger2.version>
        <easyopen.version>1.16.9</easyopen.version>
        <asm.version>6.2</asm.version>
        <pagehelper.version>5.2.0</pagehelper.version>
        <!-- 公共模块-->
        <sop-common.version>4.2.19-SNAPSHOT</sop-common.version>
        <sop-java-sdk.version>4.2.1-SNAPSHOT</sop-java-sdk.version>
        <store-auth.starter.version>1.1.2</store-auth.starter.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- apollo配置 -->
        <dependency>
            <groupId>com.fzzixun.appstore.framework</groupId>
            <artifactId>config-springboot-starter</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 服务发现 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- 健康检查 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>


        <!-- 单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fzzixun.appstore.aliexpress</groupId>
            <artifactId>ali-express-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- 速卖通sdk -->
        <dependency>
            <groupId>com.ali.express</groupId>
            <artifactId>java-sdk</artifactId>
            <version>1.0.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/ali-express-sdk-1.0.4.jar</systemPath>
        </dependency>

        <!-- kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fzzixun.appstore.aliexpress</groupId>
            <artifactId>ali-express-service</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.5</version>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.fzzixun.appstore.aliexpress.ExpressApplication</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                    <!--          <skip>true</skip>-->
                    <!--                    <classifier>exec</classifier>-->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>