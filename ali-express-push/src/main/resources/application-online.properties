server.port=80
apollo.bootstrap.enabled=true

# db
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=${APPSTORE_MYSQL_SOP_USERNAME}
spring.datasource.password=${APPSTORE_MYSQL_SOP_PASSWORD}
spring.datasource.url=jdbc:mysql://${APPSTORE_MYSQL_SOP_URL}:\
  ${APPSTORE_MYSQL_SOP_PORT}/\
  ${APPSTORE_MYSQL_SOP_DBNAME}?\
  useSSL=false&\
  useUnicode=true&\
  characterEncoding=utf-8&\
  zeroDateTimeBehavior=convertToNull&\
  serverTimezone=Asia/Shanghai