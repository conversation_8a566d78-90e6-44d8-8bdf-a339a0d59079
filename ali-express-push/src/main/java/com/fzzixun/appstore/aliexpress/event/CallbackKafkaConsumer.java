package com.fzzixun.appstore.aliexpress.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.aliexpress.base.infrastructure.config.ParammConfig;
import com.fzzixun.appstore.aliexpress.service.CallbackService;
import com.fzzixun.appstore.aliexpress.service.SellerInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class CallbackKafkaConsumer {

    private final ParammConfig paramConfig;
    private final SellerInfoService sellerInfoService;
    private final CallbackService callbackService;

    @KafkaListener(topics = "#{'${kafka.listener_topics}'.split(',')}", groupId = "${aliexpress.groupid}")
    public void notifyListen(List<ConsumerRecord<?, String>> records, Consumer<?, ?> consumer) {
        if (Boolean.TRUE.equals(paramConfig.getLogDebug())) {
            log.info("record value: {}", records.size());
        }
        try {
            for (ConsumerRecord<?, String> record : records) {
                if (Boolean.TRUE.equals(paramConfig.getLogDebug())) {
                    log.info("record partition={} offset={}", record.partition(), record.offset());
                }
                JSONObject jsonBody = JSON.parseObject(record.value());
                String sellerId = jsonBody.getString("seller_id");
                List<Map<String, String>> channelUrlList = sellerInfoService.getCallbackUrl(sellerId);
                channelUrlList.forEach(channelUrl ->
                        channelUrl.forEach((channel, url) ->
                                callbackService.sendCallback("aliexpress_" + channel + "_" + sellerId, url, record.value(), Constant.CALLBACK_MESSAGE_TYPE_DATA)
                        )
                );
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            consumer.commitAsync();
        }
    }

}
