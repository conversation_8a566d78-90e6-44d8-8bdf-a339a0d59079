package com.fzzixun.appstore.aliexpress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableSwagger2
@EnableJpaAuditing
@SpringBootApplication(scanBasePackageClasses = {ExpressPushApplication.class, AliExpressConfig.class})
public class ExpressPushApplication {


    /**
     * 启动方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        SpringApplication.run(ExpressPushApplication.class, args);
        log.info("系统启动成功");
    }
}
