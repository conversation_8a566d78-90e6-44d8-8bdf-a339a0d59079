public class Test {
    public static void main(String[] args) {
        String uri = "/semi-managed/rest";
        String[] uriInfo = uri.split("/");
//        String apiUri = uri.substring(uri.indexOf(uriInfo[0]));
//        System.out.println(apiUri);
//        System.out.println(uri.substring(uri.indexOf("/", 1)));
        if (uriInfo.length < 3) {
            System.out.println("<3");
        }
        System.out.println(uri.substring(uri.indexOf(uriInfo[2])));
    }
}
