package com.fzzixun.shein.utils;

import com.fzzixun.proxy.common.infrastructure.exception.BadRequestException;
import com.fzzixun.shein.config.SheinConfig;
import com.fzzixun.shein.servlet.RequestBuilder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collection;

@Slf4j
@Component
public class ProxyUtil {

    @Autowired
    private SheinConfig sheinConfig;

    /**
     * 生成签名
     * @param openKeyId 店铺授权的openKeyId
     * @param secretKey 店铺授权的解密secretKey
     * @param path  请求接口路径
     * @param timestamp 请求时间戳
     * @param randomKey 5位随机字符串
     * @return
     * @throws Exception
     */
    public static String generateSheinSignature(String openKeyId, String secretKey, String path,
                                                String timestamp, String randomKey) throws Exception {
        // 步骤一：组装签名数据VALUE
        String value = openKeyId + "&" + timestamp + "&" + path;
//        System.out.println("步骤一 - 签名数据VALUE: " + value);

        // 步骤二：组装签名密钥KEY
        String key = secretKey + randomKey;
//        System.out.println("步骤二 - 签名密钥KEY: " + key);

        // 步骤三：HMAC-SHA256计算并转换为十六进制
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hmacResult = mac.doFinal(value.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexStringBuilder = new StringBuilder();
        for (byte b : hmacResult) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexStringBuilder.append('0');
            }
            hexStringBuilder.append(hex);
        }
        String hexSignature = hexStringBuilder.toString();
//        System.out.println("步骤三 - HMAC-SHA256结果(HEX): " + hexSignature);

        // 步骤四：Base64编码
        String base64Signature = Base64.getEncoder().encodeToString(
                hexSignature.getBytes(StandardCharsets.UTF_8));
//        System.out.println("步骤四 - Base64编码结果: " + base64Signature);

        // 步骤五：拼接RandomKey
        String finalSignature = randomKey + base64Signature;
//        System.out.println("步骤五 - 最终签名: " + finalSignature);

        return finalSignature;
    }

    public RequestBuilder createOkHttpRequest(HttpServletRequest servletRequest, String manageType, String apiUri) throws Exception {
        if (StringUtils.isEmpty(manageType)) {
            return new RequestBuilder(null, null, false, "请求地址错误，缺少店铺管理类型");
        }
        String apiUrl;
        String appId;
        String appSecret;
        if ("semi-managed".equalsIgnoreCase(manageType)) {
            apiUrl = sheinConfig.getSemiManagedApiUrl() + apiUri;
            appId = sheinConfig.getSemiManagedAppId();
            appSecret = sheinConfig.getSemiManagedAppSecret();
        } else if ("full-managed".equalsIgnoreCase(manageType)) {
            apiUrl = sheinConfig.getFullManagedApiUrl() + apiUri;
            appId = sheinConfig.getFullManagedAppId();
            appSecret = sheinConfig.getFullManagedAppSecret();
        }  else if ("self-operated".equalsIgnoreCase(manageType)) {
            apiUrl = sheinConfig.getSelfOperatedApiUrl() + apiUri;
            appId = sheinConfig.getSelfOperatedAppId();
            appSecret = sheinConfig.getSelfOperatedAppSecret();
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效店铺管理类型");
        }

        if (StringUtils.isEmpty(appId)) {
            return new RequestBuilder(null, null, false, "暂不支持此站点该店铺管理类型");
        }

        RequestBuilder.SheinInfo sheinInfo = new RequestBuilder.SheinInfo(appId, appSecret);

        String method = servletRequest.getMethod();
        if (StringUtils.isNotBlank(servletRequest.getQueryString())) {
            apiUrl = apiUrl + "?" + servletRequest.getQueryString();
        }
        String contentType = servletRequest.getHeader("content-type");
        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {
            if (contentType != null && contentType.contains("multipart/form-data")) {
                // 处理multipart/form-data请求
                return handleMultipartRequest(servletRequest, apiUrl, method, sheinInfo);
            }
            if (contentType != null &&
                    !contentType.contains("x-www-form-urlencoded")) {
                ServletInputStream servletInputStream = servletRequest.getInputStream();
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = servletInputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String requestParamsStr = result.toString(StandardCharsets.UTF_8.name());
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), requestParamsStr);
                RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, requestBody), requestParamsStr, true, null);
                requestBuilder.setSheinInfo(sheinInfo);
                return requestBuilder;
            }
            return null;
        }
        if (method.equalsIgnoreCase("POST") || method.equalsIgnoreCase("PUT")) {
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), new byte[0]);
            RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, requestBody), null, true, null);
            requestBuilder.setSheinInfo(sheinInfo);
            return requestBuilder;
        }
        RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, null), null, true, null);
        requestBuilder.setSheinInfo(sheinInfo);
        return requestBuilder;
    }

    public Request createReqHeader(HttpServletRequest httpRequest, Request.Builder requestBuilder,
                                   String reqPath, String openKeyId, String secretKey) {
        String language = httpRequest.getHeader("language");
        if (StringUtils.isNotBlank(language)) {
            requestBuilder.addHeader("language", language);
        }

        // 检查是否为multipart请求，如果是则不设置Content-Type，让OkHttp自动处理
        String contentType = httpRequest.getHeader("content-type");
        if (contentType == null || !contentType.contains("multipart/form-data")) {
            requestBuilder.addHeader("Content-Type", "application/json;charset=UTF-8");
        }

        requestBuilder.addHeader("x-lt-openKeyId", openKeyId);
        String timestamp = String.valueOf(System.currentTimeMillis());
        requestBuilder.addHeader("x-lt-timestamp", timestamp); // 时间戳（5分钟内有效）
        String sign;
        try {
            sign = generateSheinSignature(openKeyId, secretKey, reqPath, timestamp, RandomStringUtils.randomAlphanumeric(5));
        } catch (Exception e) {
            log.error("generateSheinSignature error", e);
            throw new BadRequestException("generateSheinSignature error");
        }
        requestBuilder.addHeader("x-lt-signature", sign);
        return requestBuilder.build();
    }

    /**
     * 处理multipart/form-data请求
     * @param servletRequest HTTP请求
     * @param apiUrl 目标API URL
     * @param method HTTP方法
     * @param sheinInfo Shein配置信息
     * @return RequestBuilder对象
     */
    private RequestBuilder handleMultipartRequest(HttpServletRequest servletRequest, String apiUrl,
                                                  String method, RequestBuilder.SheinInfo sheinInfo) {
        try {
            // 检查是否支持multipart
            if (!isMultipartSupported(servletRequest)) {
                log.warn("Servlet不支持multipart配置，尝试使用原始流处理");
                return handleMultipartAsStream(servletRequest, apiUrl, method, sheinInfo);
            }

            MultipartBody.Builder multipartBuilder = new MultipartBody.Builder();
            multipartBuilder.setType(MultipartBody.FORM);

            // 获取所有的multipart parts
            Collection<Part> parts = servletRequest.getParts();
            StringBuilder requestBodyLog = new StringBuilder("Multipart request with parts: ");
            for (Part part : parts) {
                String partName = part.getName();
                String fileName = part.getSubmittedFileName();

                if (fileName != null && !fileName.isEmpty()) {
                    // 这是一个文件部分
                    byte[] fileData = readPartData(part);
                    String partContentType = part.getContentType();
                    if (partContentType == null) {
                        partContentType = "application/octet-stream";
                    }

                    RequestBody fileBody = RequestBody.create(MediaType.parse(partContentType), fileData);
                    multipartBuilder.addFormDataPart(partName, fileName, fileBody);
                    requestBodyLog.append(String.format("[File: %s=%s (%d bytes)] ", partName, fileName, fileData.length));
                } else {
                    // 这是一个普通的表单字段
                    String fieldValue = readPartAsString(part);
                    multipartBuilder.addFormDataPart(partName, fieldValue);
                    requestBodyLog.append(String.format("[Field: %s=%s] ", partName, fieldValue));
                }
            }
            MultipartBody multipartBody = multipartBuilder.build();
            RequestBuilder requestBuilder = new RequestBuilder(
                (new Request.Builder()).url(apiUrl).method(method, multipartBody),
                requestBodyLog.toString(),
                true,
                null
            );
            requestBuilder.setFileReqSize(multipartBody.contentLength());
            requestBuilder.setSheinInfo(sheinInfo);
            return requestBuilder;

        } catch (Exception e) {
            log.error("处理multipart请求失败", e);
            // 如果multipart处理失败，尝试作为普通流处理
            return handleMultipartAsStream(servletRequest, apiUrl, method, sheinInfo);
        }
    }

    /**
     * 检查是否支持multipart配置
     * @param request HTTP请求
     * @return 是否支持
     */
    private boolean isMultipartSupported(HttpServletRequest request) {
        try {
            request.getParts();
            return true;
        } catch (Exception e) {
            log.debug("Multipart配置不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 当multipart配置不可用时，作为原始流处理
     * @param servletRequest HTTP请求
     * @param apiUrl 目标API URL
     * @param method HTTP方法
     * @param sheinInfo Shein配置信息
     * @return RequestBuilder对象
     */
    private RequestBuilder handleMultipartAsStream(HttpServletRequest servletRequest, String apiUrl,
                                                   String method, RequestBuilder.SheinInfo sheinInfo) {
        try {
            ServletInputStream servletInputStream = servletRequest.getInputStream();
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = servletInputStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }

            String contentType = servletRequest.getHeader("content-type");
            byte[] requestData = result.toByteArray();
            RequestBody requestBody = RequestBody.create(MediaType.parse(contentType), requestData);

            String requestBodyLog = String.format("Multipart stream request (%d bytes)", requestData.length);
            RequestBuilder requestBuilder = new RequestBuilder(
                (new Request.Builder()).url(apiUrl).method(method, requestBody),
                requestBodyLog,
                true,
                null
            );
            requestBuilder.setSheinInfo(sheinInfo);
            return requestBuilder;

        } catch (Exception e) {
            log.error("处理multipart流请求失败", e);
            return new RequestBuilder(null, null, false, "处理multipart请求失败: " + e.getMessage());
        }
    }

    /**
     * 读取Part中的文件数据
     * @param part multipart part
     * @return 文件字节数组
     * @throws IOException IO异常
     */
    private byte[] readPartData(Part part) throws IOException {
        try (InputStream inputStream = part.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        }
    }

    /**
     * 读取Part中的字符串数据
     * @param part multipart part
     * @return 字符串值
     * @throws IOException IO异常
     */
    private String readPartAsString(Part part) throws IOException {
        try (InputStream inputStream = part.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toString(StandardCharsets.UTF_8.name());
        }
    }

}
