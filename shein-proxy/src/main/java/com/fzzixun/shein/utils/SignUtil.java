package com.fzzixun.shein.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SignUtil {

    public static String getSignContent(Map<String, ?> params) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            String value = String.valueOf(params.get(key));
            if (StringUtils.isNoneEmpty(key, value)) {
                content.append(key).append(value);
            }
        }
        return content.toString();
    }
}
