package com.fzzixun.shein.servlet;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import okhttp3.Request;

/**
 * <AUTHOR>
 * @create 2022/6/7
 */
@Data
@NoArgsConstructor
public class RequestBuilder {
    private Request.Builder reqBuilder;
    private String requestBody;
    private boolean success = true;
    private String errorMsg;
    private SheinInfo sheinInfo;
    private Long fileReqSize;

    public RequestBuilder(Request.Builder reqBuilder, String requestBody, boolean success, String errorMsg) {
        this.reqBuilder = reqBuilder;
        this.requestBody = requestBody;
        this.success = success;
        this.errorMsg = errorMsg;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SheinInfo {
        private String appId;
        private String appSecret;
    }
}
