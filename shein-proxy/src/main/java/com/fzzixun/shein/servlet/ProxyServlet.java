package com.fzzixun.shein.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.proxy.common.infrastructure.exception.BadRequestException;
import com.fzzixun.shein.domain.entity.SheinSellerAuth;
import com.fzzixun.shein.domain.repository.SheinSellerAuthRepository;
import com.fzzixun.shein.dto.ProxyCompanyDTO;
import com.fzzixun.shein.dto.RequestRecord;
import com.fzzixun.shein.dto.SheinIpDTO;
import com.fzzixun.shein.manager.ProxyCompanyManager;
import com.fzzixun.shein.manager.SheinWhiteIpManager;
import com.fzzixun.shein.utils.AESTools;
import com.fzzixun.shein.utils.IpUtil;
import com.fzzixun.shein.utils.ProxyUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * SP-API代理转发服务
 */
@WebServlet(value = "/*", name = "ProxyServlet")
@MultipartConfig(
    maxFileSize = 200 * 1024 * 1024,      // 200MB
    maxRequestSize = 400 * 1024 * 1024,   // 400MB
    fileSizeThreshold = 5 * 1024 * 1024   // 5MB
)
@Slf4j
public class ProxyServlet extends HttpServlet {

    public static final String NUMBER_OF_CALLS_KEY = "numberOfCalls";
    public static final String REQUEST_SIZE_KEY = "requestSize";
    public static final String RESPONSE_SIZE_KEY = "responseSize";

    private final static String SHEIN_OPEN_KEY_PREFIX = "shein:openKey:";

    @Value("${limitStatusCode:429}")
    private Integer limitStatusCode;

    @Value("${limitTime:10}")
    private Integer limitTime;

    @Value("${limitReturnCode:403}")
    private Integer limitReturnCode;

    @Value("${printResponseLog:false}")
    private Boolean printResponseLog;

    @Value("${logStore.name:shein-record}")
    private String logStore;

    // 测试用，开启后所有转发请求的店铺保存
    @Value("${marketplaceRecord.test:false}")
    private Boolean marketplaceRecordTest;
    @Autowired
    private ProxyUtil proxyUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SheinSellerAuthRepository sheinSellerAuthRepository;

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 3036995738596829749L;



    @Override
    protected void service(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        try {
            String uri = servletRequest.getRequestURI();
            String[] uriInfo = uri.split("/");
            if (uriInfo.length < 2) {
                badRequest(servletResponse, "请求地址错误，缺少店铺托管类型");
                return;
            }
            if (uriInfo.length < 3) {
                badRequest(servletResponse, "请求地址错误，缺少API路径");
                return;
            }
            if ("/routing.json".equals(uri)) {
                writeResponse(servletResponse, 200, "{\"routes\":[]}");
                return;
            }
            String apiUri = "/" + uri.substring(uri.indexOf(uriInfo[2]));

            String ipAddress = IpUtil.getIP(servletRequest);
            SheinIpDTO spApiIpDTO = SheinWhiteIpManager.getWhiteIpMap().get(ipAddress);
            if (!"/".equals(uri)) {
                log.info("======>>>调用URI地址：{}, 调用IP地址：{}", uri, ipAddress);
                if (StringUtils.isNotEmpty(ipAddress) && spApiIpDTO == null) {
                    log.warn("该IP禁止访问: {}", ipAddress);
                    forbidden(servletResponse, "该IP禁止访问：" + ipAddress);
                    return;
                }
            }
            String openKeyId = servletRequest.getHeader("x-lt-openKeyId");
            if (StringUtils.isEmpty(openKeyId)) {
                forbidden(servletResponse, "请求头中缺少x-lt-openKeyId");
                return;
            }
            String manageType = uriInfo[1];
            RequestBuilder requestBuilder;
            requestBuilder = proxyUtil.createOkHttpRequest(servletRequest, manageType, apiUri);
            if (Boolean.TRUE.equals(printResponseLog)) {
                log.info("请求参数===> {}", requestBuilder.getRequestBody());
            }
            if (!requestBuilder.isSuccess()) {
                badRequest(servletResponse, requestBuilder.getErrorMsg());
                return;
            }
            String appId = requestBuilder.getSheinInfo().getAppId();
            String appSecret = requestBuilder.getSheinInfo().getAppSecret();
            String secretKey = this.getSecretKey(manageType, openKeyId, appId, appSecret);
            Request request = proxyUtil.createReqHeader(servletRequest, requestBuilder.getReqBuilder(),
                    apiUri, openKeyId, secretKey);

            if (Boolean.TRUE.equals(printResponseLog)) {
                log.info("======>>>[{}][{}]调用地址：{}", servletRequest.getMethod(), ipAddress, request.url());
            }
            Long fileReqSize = requestBuilder.getFileReqSize();
            long requestSize;
            if (fileReqSize != null) {
                requestSize = fileReqSize;
            } else {
                requestSize = requestBuilder.getRequestBody() == null ? 0 : requestBuilder.getRequestBody().length();
            }
            String statisticsKey = "shein_" + ipAddress + "_" + LocalDate.now().minusDays(1);
            long responseSize = 0;
            if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(statisticsKey, NUMBER_OF_CALLS_KEY))) {
                hashIncrBy(statisticsKey, NUMBER_OF_CALLS_KEY, 1);
                if (requestSize > 0) {
                    hashIncrBy(statisticsKey, REQUEST_SIZE_KEY, requestSize);
                }
            } else {
                Map<String, Object> maps = new HashMap<>();
                maps.put(NUMBER_OF_CALLS_KEY, 1);
                maps.put(REQUEST_SIZE_KEY, requestSize);
                maps.put(RESPONSE_SIZE_KEY, responseSize);
                hPutAll(statisticsKey, maps);
            }
            Call call = okHttpClient.newCall(request);
            ServletOutputStream servletOutputStream;
            byte[] responseByte;
            try (Response response = call.execute()) {
                if (Boolean.TRUE.equals(printResponseLog)) {
                    log.info("[{}],返回码[{}]response=>{}", ipAddress, response.code(), response);
                }
                ResponseBody body = response.body();
//            limitByStatusCode(key, response.code(), request.url());
                servletResponse.setStatus(response.code(), response.message());
                Headers headers = response.headers();
                for (String headerName : headers.names()) {
    //                if (Boolean.TRUE.equals(printResponseLog)) {
    //                    log.info("addHeader->> name:{}，value:{}", headerName, headers.get(headerName));
    //                }
                    if ("Content-Length".equals(headerName)) {
                        servletResponse.addHeader(headerName, body == null ? "0" : String.valueOf(body.contentLength()));
                        continue;
                    }
                    if (!"Transfer-Encoding".equals(headerName)) {
                        servletResponse.addHeader(headerName, headers.get(headerName));
                    } else {
    //                    if (Boolean.TRUE.equals(printResponseLog)) {
    //                        log.info("no addHeader:{}", headerName);
    //                    }
                    }
                }
                servletOutputStream = servletResponse.getOutputStream();
                responseByte = body == null ? new byte[0] : body.bytes().clone();
                if (Boolean.TRUE.equals(printResponseLog)) {
                    log.info("[{}]返回结果大小{} ===>>>{}", ipAddress, responseByte.length, new String(responseByte, StandardCharsets.UTF_8));
                }
                if (500 == response.code()) {
                    //响应500打印请求和响应
                    log.info("500内部错误req: [{}][{}]请求地址：{}, 请求体: {}", servletRequest.getMethod(), ipAddress, request.url(), requestBuilder.getRequestBody());
                    String result = new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", "");
                    log.info("500内部错误resp: [{}][{}],返回码[{}], response=>{}", servletRequest.getMethod(), ipAddress, response.code(), result);
                }
                hashIncrBy(statisticsKey, RESPONSE_SIZE_KEY, responseByte.length);
                // 请求记录 ip、公司名、店铺id、url、日期、请求大小、响应大小、
                try {
                    String companyName = Optional.ofNullable(ProxyCompanyManager.getCompanyMap().get(spApiIpDTO.getCompanyId()))
                            .map(ProxyCompanyDTO::getCompanyName).orElse("");
                    MDC.put("logStore", logStore);
                    RequestRecord requestRecord = new RequestRecord(ipAddress, companyName, null,
                            request.url().uri().getRawPath(), LocalDate.now(), requestSize, responseByte.length, response.code());
                    log.info(JSON.toJSONString(requestRecord));
                    MDC.remove("logStore");
                } catch (Exception e) {
                    log.error("记录请求记录失败", e);
                }
            }
            servletOutputStream.write(responseByte);
        } catch (Exception e) {
            log.error("代理请求异常！", e);
        }
    }

    private Long hashIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    private void forbidden(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 403, "{\"errors\": \"" + msg + "\"}");
    }

    private void badRequest(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 400, "{\"errors\": \"" + msg + "\"}");
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }

    private String getSecretKey(String type, String openKeyId, String appId, String appSecret) {
        String decryptSecretKey;
        String openKeyRedisKey = SHEIN_OPEN_KEY_PREFIX + type + ":" + openKeyId;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(openKeyRedisKey))) {
            SheinSellerAuth sheinSellerAuth = sheinSellerAuthRepository.findFirstByOpenKeyId(openKeyId)
                    .orElseThrow(() -> new BadRequestException("openKeyId not found"));
            if (sheinSellerAuth.getAppId() == null) {
                decryptSecretKey = sheinSellerAuth.getEncryptSecretKey();
            } else {
                decryptSecretKey = AESTools.decrypt(sheinSellerAuth.getEncryptSecretKey(), appSecret);
            }
            JSONObject params = new JSONObject();
            params.put("appId", appId);
            params.put("secretKey", decryptSecretKey);
            params.put("callbackUrl", sheinSellerAuth.getCallbackUrl());
            log.info("缓存openKeyId:[{}]解密后的secretKey", openKeyId);
            redisTemplate.opsForValue().set(openKeyRedisKey, params.toJSONString(), 60 * 60 * 24 * 7, TimeUnit.SECONDS);
            return decryptSecretKey;
        }
        String openKeyValue = (String) redisTemplate.opsForValue().get(openKeyRedisKey);
        JSONObject params = JSONObject.parseObject(openKeyValue);
        decryptSecretKey = params.getString("secretKey");
        return decryptSecretKey;
    }
}
