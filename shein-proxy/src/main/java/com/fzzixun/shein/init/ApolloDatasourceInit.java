package com.fzzixun.shein.init;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.BaseApolloDataSource;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.namespace.NamespaceApolloDataSource;
import com.fzzixun.shein.dto.ProxyCompanyDTO;
import com.fzzixun.shein.dto.SheinIpDTO;
import com.fzzixun.shein.manager.ProxyCompanyManager;
import com.fzzixun.shein.manager.SheinWhiteIpManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApolloDatasourceInit implements ApplicationRunner {

    @Value("${micro.framework.datasource.apollo.sp-api-company.namespace-name:a1-02-appstore.sp-api-company}")
    String spApiCompanyNamespaceName;

    @Value("${micro.framework.datasource.apollo.sp-api-white-ip.namespace-name:a1-02-appstore.sp-api-white-ip}")
    String spApiIpNamespaceName;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        BaseApolloDataSource<String, ProxyCompanyDTO> spApiCompanyApolloDataSource = new NamespaceApolloDataSource<>(spApiCompanyNamespaceName,
                source -> JSON.parseObject(source, ProxyCompanyDTO.class));
        ProxyCompanyManager.register2Property(spApiCompanyApolloDataSource.getProperty());
        log.info("Apollo-Shein-API-Company数据初始化");
        spApiCompanyApolloDataSource.initData();

        BaseApolloDataSource<String, SheinIpDTO> spApiIpApolloDataSource = new NamespaceApolloDataSource<>(spApiIpNamespaceName,
                source -> JSON.parseObject(source, SheinIpDTO.class));
        SheinWhiteIpManager.register2Property(spApiIpApolloDataSource.getProperty());
        log.info("Apollo-Shein-API-White-IP数据初始化");
        spApiIpApolloDataSource.initData();

    }
}
