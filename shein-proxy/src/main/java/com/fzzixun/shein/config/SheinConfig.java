package com.fzzixun.shein.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @create 2022/5/7
 */
@Data
@Configuration
public class SheinConfig {

    @Value("${shein.api.semi-managed.app_id:}")
    private String semiManagedAppId;
    @Value("${shein.api.semi-managed.app_secret:}")
    private String semiManagedAppSecret;

    @Value("${shein.api.full-managed.app_id:}")
    private String fullManagedAppId;
    @Value("${shein.api.full-managed.app_secret:}")
    private String fullManagedAppSecret;

    @Value("${shein.api.self-operated.app_id:}")
    private String selfOperatedAppId;
    @Value("${shein.api.self-operated.app_secret:}")
    private String selfOperatedAppSecret;

    @Value("${shein.auth.login.url:https://openapi-sem.sheincorp.com/#/empower}")
    private String loginUrl;

    // 半托管
    @Value("${shein.api.semi-managed.url:https://openapi.sheincorp.com}")
    private String semiManagedApiUrl;
    // 全托管
    @Value("${shein.api.full-managed.url:https://openapi.sheincorp.cn}")
    private String fullManagedApiUrl;
    // 自运营
    @Value("${shein.api.self-operated.url:https://openapi.sheincorp.com}")
    private String selfOperatedApiUrl;

    @Value("${shein.auth.index.url:https://sbappstoreapi.ziniao.com/shein-auth}")
    private String authIndex;

    @Value("${shein.auth.finish.url:https://sbappstoreapi.ziniao.com/shein-auth/rest/v1/auth/finish}")
    private String authFinishUrl;
}
