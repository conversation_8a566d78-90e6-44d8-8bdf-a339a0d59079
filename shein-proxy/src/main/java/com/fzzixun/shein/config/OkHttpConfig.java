package com.fzzixun.shein.config;

import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class OkHttpConfig {

    @Value("${param.okhttp.read-timeout:30}")
    private Integer okhttpReadTimeout;

    @Value("${param.okhttp.connect-timeout:10}")
    private Integer okhttpConnectTimeout;

    @Value("${param.okhttp.write-timeout:30}")
    private Integer okhttpWriteTimeout;

    @Value("${param.okhttp.connection-pool.max-idle-connections:20}")
    private Integer maxIdleConnections;

    @Value("${param.okhttp.connection-pool.keep-alive-duration:5}")
    private Integer keepAliveDuration;

    @Value("${param.okhttp.dispatcher.max-requests:64}")
    private Integer maxRequests;

    @Value("${param.okhttp.dispatcher.max-requests-per-host:32}")
    private Integer maxRequestsPerHost;

    @Bean
    public OkHttpClient okHttpClient() {
        // 创建连接池
        ConnectionPool connectionPool = new ConnectionPool(
                maxIdleConnections,
                keepAliveDuration,
                TimeUnit.MINUTES
        );

        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .readTimeout(okhttpReadTimeout, TimeUnit.SECONDS)
                .connectTimeout(okhttpConnectTimeout, TimeUnit.SECONDS)
                .writeTimeout(okhttpWriteTimeout, TimeUnit.SECONDS)
                .connectionPool(connectionPool);

        // 配置调度器的并发请求数
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(maxRequests);
        dispatcher.setMaxRequestsPerHost(maxRequestsPerHost);
        builder.dispatcher(dispatcher);

        return builder.build();
    }

}
