package com.fzzixun.shein.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AuthResponse implements Serializable {

    @ApiModelProperty(value = "响应码. 0:处理成功")
    private String code;
    private String msg;
    private AuthResultVo info;
    @ApiModelProperty(value = "请求的唯一标识；用于异常报错跟踪")
    private String traceId;

    public boolean isSuccess() {
        return "0".equals(this.getCode());
    }

    @Data
    public static class AuthResultVo {

        @ApiModelProperty(value = "卖家账号秘钥，需用开发者appSecretKey解密")
        private String secretKey;

        @ApiModelProperty(value = "开发者appId")
        private String appid;

        @ApiModelProperty(value = "卖家账号openKeyId")
        private String openKeyId;

        @ApiModelProperty(value = "原样返回的标识")
        private String state;

        @ApiModelProperty(value = "SHEIN商户ID")
        private String supplierId;

        private Integer supplierSource;


    }
}