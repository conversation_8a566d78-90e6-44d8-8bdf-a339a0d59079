server.port=10087
apollo.bootstrap.enabled=true
apollo.meta=http://localhost:8080
management.server.port=19001
logging.level.root=info
logging.level.com.fzzixun.appstore.temu=debug

ipWhiteList=127.0.0.1,*************,*************

testLimit=false


# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

limitStatusCode=429
printResponseLog = true

spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM

# Naming strategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.properties.hibernate.max_fetch_depth = 1
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true


#测试
shein.api.semi-managed.app_id=10ADAAA5CE0008CF3585A106B6AFF
shein.api.semi-managed.app_secret=D70A14A37467468AA4F5B96CE42A61F2
#shein.api.semi-managed.app_id = 131F55652C002A250174D359F9EC0
#shein.api.semi-managed.app_secret = EA56CA7C3BFB477698C30B09429860B5
shein.api.semi-managed.url=https://openapi-test01.sheincorp.cn

shein.api.full-managed.app_id=F0509DD53F86458F983020D9DBD220F8
shein.api.full-managed.app_secret=B7043FE37FEA4DA99824FBB1E7C84169
shein.api.full-managed.url=https://openapi-test01.sheincorp.cn

shein.api.self-operated.app_id=EED6AEEA6B4741EF94D29FED5A1CE76F
shein.api.self-operated.app_secret=35D01D988EBA46FB9D87CA066FFD1805
shein.api.self-operated.url=https://openapi-test01.sheincorp.cn

shein.auth.login.url=https://openapi-sem-test01.dotfashion.cn/sem/supplier/authorizateTempTest
shein.auth.index.url=http://127.0.0.1:10088
shein.auth.finish.url=http://127.0.0.1:10088/rest/v1/auth/finish