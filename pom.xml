<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fzzixun.appstore.spapi</groupId>
    <artifactId>spapi-auth-proxy</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>spapi-proxy</module>
        <module>spapi-auth</module>
        <!--        <module>ali-express-auth</module>-->
        <module>ali-express-common</module>
        <!--        <module>ali-express-forward</module>-->
        <!--        <module>ali-express-push</module>-->
        <!--        <module>ali-express-dao</module>-->
        <!--        <module>ali-express-service</module>-->
        <module>aws-ad-auth</module>
        <module>aws-ad-proxy</module>
        <module>common-service</module>
        <module>temu-proxy</module>
        <module>sif-proxy</module>
        <module>shein-auth</module>
        <module>shein-proxy</module>
        <module>proxy-common</module>
    </modules>


    <properties>
        <skipTests>true</skipTests>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 指定maven.compiler.plugin 配置版本，解决编译问题 -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot.version>2.5.15</spring-boot.version>
        <spring.cloud-version>2020.0.3</spring.cloud-version>
        <lombok.version>1.18.22</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.7.20</hutool.version>
        <netty.version>4.1.73.Final</netty.version>
        <http-client.version>4.5.13</http-client.version>
        <okhttp-version>2.7.5</okhttp-version>
        <commons-lang.version>3.12.0</commons-lang.version>
        <logback.version>1.2.3</logback.version>
        <commons-logging.version>1.2</commons-logging.version>
        <knife4j.version>3.0.2</knife4j.version>
        <apollo-client.version>1.3.0</apollo-client.version>
        <openapi.version>1.0.3</openapi.version>
        <browser-data.version>2.1.2</browser-data.version>
        <xxl-job.version>2.3.0</xxl-job.version>
        <jackson.version>2.10.1</jackson.version>
        <commons-codec.version>1.11</commons-codec.version>
        <guava.version>29.0-jre</guava.version>
        <swagger.version>1.5.21</swagger.version>
        <springfox-spring-web.version>2.9.2</springfox-spring-web.version>
        <springfox-swagger2.version>2.9.2</springfox-swagger2.version>
        <commons-fileupload.version>1.3.3</commons-fileupload.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-collection.version>3.2.2</commons-collection.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${http-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo-client.version}</version>
            </dependency>
            <!-- 开放平台对RPA服务 -->
            <dependency>
                <groupId>com.fzzixun.appstore.tool</groupId>
                <artifactId>openapi-rpa-starter</artifactId>
                <version>${openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fzzixun.appstore.tool</groupId>
                <artifactId>browser-data-starter</artifactId>
                <version>${browser-data.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <!-- 加了这个就不需要加版本号了 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign-core</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-spring-web</artifactId>
                <version>${springfox-spring-web.version}</version>
            </dependency>
            <!-- Swagger文档 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger2.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>1.9.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- commons -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collection.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>

            <!-- kafka -->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>2.7.2</version>
            </dependency>


            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>9.0.99</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tomcat-annotations-api</artifactId>
                        <groupId>org.apache.tomcat</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>9.0.99</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>9.0.99</version>
                <exclusions>
                    <exclusion>
                        <artifactId>tomcat-embed-core</artifactId>
                        <groupId>org.apache.tomcat.embed</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>9.0.99</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>