package com.fzzixun.appstore.aliexpress.valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @create 2021/11/9
 */

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValue.Validator.class)
public @interface EnumValue {

    String message() default "枚举值不存在";

    // 作用参考@Validated和@Valid的区别
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 目标枚举类
     */
    Class<?> target() default Class.class;

    /**
     * 是否忽略空值
     */
    boolean ignoreEmpty() default true;

    /**
     * 判断参数，默认name
     */
    String checkMethodName() default "name";

    @Slf4j
    class Validator implements ConstraintValidator<EnumValue, Object> {
        // 枚举校验注解
        private EnumValue annotation;

        @Override
        public void initialize(EnumValue constraintAnnotation) {
            annotation = constraintAnnotation;
        }

        @Override
        public boolean isValid(Object value, ConstraintValidatorContext constraintValidatorContext) {
            boolean result = false;

            Class<?> cls = annotation.target();
            boolean ignoreEmpty = annotation.ignoreEmpty();

            // target为枚举，并且value有值，或者不忽视空值，才进行校验
            if (cls.isEnum() && (!StringUtils.isEmpty(value) || !ignoreEmpty)) {

                Object[] objects = cls.getEnumConstants();
                try {
                    Method method = cls.getMethod(annotation.checkMethodName());
                    for (Object obj : objects) {
                        Object code = method.invoke(obj);
                        if (value.toString().equals(code.toString())) {
                            result = true;
                            break;
                        }
                    }
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    log.warn("EnumValidator call isValid() method exception.");
                    result = false;
                }
            } else {
                result = true;
            }
            return result;
        }
    }
}
