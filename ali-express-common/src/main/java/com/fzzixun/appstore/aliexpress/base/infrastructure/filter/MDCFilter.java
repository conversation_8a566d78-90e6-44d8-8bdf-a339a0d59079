package com.fzzixun.appstore.aliexpress.base.infrastructure.filter;

import com.fzzixun.appstore.aliexpress.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

@Slf4j
@WebFilter
@Component
@Order(-10)
public class MDCFilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        insertIntoMDC(httpServletRequest);
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            clearMDC();
        }
    }

    void insertIntoMDC(HttpServletRequest request) {
//        String uri = httpServletRequest.getRequestURI();
//        String method = httpServletRequest.getMethod();
        String requestId = request.getHeader(Constant.X_GATEWAY_REQUEST_ID);
        if (requestId == null) {
            UUID uuid = UUID.randomUUID();
            requestId = uuid.toString();
        }
        MDC.put(Constant.MDC_REQUEST_TRACE, requestId);
//        MDC.put(ClassicConstants.REQUEST_METHOD, method);
//        MDC.put(ClassicConstants.REQUEST_REQUEST_URI, uri);
    }

    void clearMDC() {
//        MDC.remove(ClassicConstants.REQUEST_METHOD);
//        MDC.remove(ClassicConstants.REQUEST_REQUEST_URI);
        MDC.remove(Constant.MDC_REQUEST_TRACE);
    }
}
