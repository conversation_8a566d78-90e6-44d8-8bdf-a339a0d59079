package com.fzzixun.appstore.aliexpress.vo;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.http.HttpStatus;

public class ResponseVo<T> {

    @ApiModelProperty("错误ID,用于日志查询")
    private String id;

    private int code;
    private String message;
    private T data;

    public ResponseVo(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseVo() {
        this.code = Status.SUCCESS.getCode();
        this.message = Status.SUCCESS.getStandardMessage();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getCode() {
        return code;
    }

    public boolean isSuccess() {
        return getCode() == Status.SUCCESS.getCode();
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> ResponseVo<T> ofMessage(int code, String message) {
        return new ResponseVo<>(code, message, null);
    }

    public static <T> ResponseVo<T> ofError(String message) {
        return new ResponseVo<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message, null);
    }

    public static <T> ResponseVo<T> ofError() {
        return new ResponseVo<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "未知异常，请联系管理员", null);
    }

    public static <T> ResponseVo<T> ofBadRequestWithId(String message, String id) {
        ResponseVo<T> responseVo = ofBadRequest(message);
        responseVo.setId(id);
        return responseVo;
    }

    public static <T> ResponseVo<T> ofBadRequest(String message) {
        return new ResponseVo<>(HttpStatus.BAD_REQUEST.value(), message, null);
    }

    public static <T> ResponseVo<T> ofSuccessWithId(String id) {
        ResponseVo<T> responseVo = new ResponseVo<>();
        responseVo.setId(id);
        return responseVo;
    }


    public static <T> ResponseVo<T> ofSuccess() {
        return new ResponseVo<>();
    }

    public static <T> ResponseVo<T> ofSuccess(T data) {
        return new ResponseVo<>(Status.SUCCESS.getCode(), Status.SUCCESS.getStandardMessage(), data);
    }

    public static <T> ResponseVo<T> ofSuccessMessage(String message)
    {
        return new ResponseVo<>(Status.SUCCESS.getCode(), message, null);
    }

    public static <T> ResponseVo<T> ofStatus(Status status) {
        return new ResponseVo<>(status.getCode(), status.getStandardMessage(), null);
    }

    public enum Status {
        SUCCESS(200, "Success"),
        BAD_REQUEST(400, "Bad Request"),
        NOT_FOUND(404, "Not Found"),
        INTERNAL_SERVER_ERROR(500, "Unknown Internal Error"),
        NOT_VALID_PARAM(40005, "Not valid Params"),
        NOT_SUPPORTED_OPERATION(40006, "Operation not supported"),
        NOT_LOGIN(50000, "Not Login");

        private int code;
        private String standardMessage;

        Status(int code, String standardMessage) {
            this.code = code;
            this.standardMessage = standardMessage;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getStandardMessage() {
            return standardMessage;
        }

        public void setStandardMessage(String standardMessage) {
            this.standardMessage = standardMessage;
        }
    }
}
