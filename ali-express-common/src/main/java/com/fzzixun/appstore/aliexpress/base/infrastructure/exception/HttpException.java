package com.fzzixun.appstore.aliexpress.base.infrastructure.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;


@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HttpException extends RuntimeException {
    private static final long serialVersionUID = 100769337459532804L;
    /**
     * 响应状态码
     */
    private HttpStatus httpStatus;

    /**
     * 错误状态码
     *
     * @param status
     * @param message
     */
    public HttpException(HttpStatus status, String message) {
        super(message);
        httpStatus = status;
    }
}