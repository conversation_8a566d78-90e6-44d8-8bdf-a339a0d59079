package com.fzzixun.appstore.aliexpress.base.infrastructure.config;

import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class ExpresssClientConfig {

    @Value("${aliexpress.api.url:https://api-sg.aliexpress.com}")
    public String serverUrl;

    @Value("${aliexpress.auth.url:https://api-sg.aliexpress.com/oauth/authorize}")
    public String authUrl;

    @Value("${aliexpress.redirect.uri:https://sbappstoreapi.ziniao.com/ali-express-auth/rest/v1/auth/finish}")
    public String redirectUri;

    @Value("${aliexpress.api.appkey:501312}")
    public String appKey;

    @Value("${aliexpress.api.secret:}")
    public String secret;

    @Bean
    @ConditionalOnProperty(name = "express.client.enable", havingValue = "true")
    public IopClient createExpressClient() {
        return new IopClientImpl(serverUrl, appKey, secret);
    }
}
