package com.fzzixun.appstore.aliexpress.base.infrastructure.exception;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.UUID;


@Slf4j
@ControllerAdvice
public class ExpressExceptionHandler {

    private static final String INNER_EXCEPTION = "自定义异常:";

    private String getLogError(ResponseVo<Object> responseDto) {
        return responseDto.getMessage() + ", 异常id:" + responseDto.getId();
    }

    private String getErrorId() {
        String requestId = MDC.get(Constant.MDC_REQUEST_TRACE);
        if (requestId == null) {
            requestId = UUID.randomUUID().toString();
        }
        return requestId;
    }

    @ResponseBody
    @ExceptionHandler(value = ApiException.class)
    public ResponseEntity<ResponseVo<Object>> handlerApiException(ApiException e) {
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest("aliExpress请求异常");
        responseVo.setId(getErrorId());
        log.error("aliExpress请求异常:" + getLogError(responseVo));
        return new ResponseEntity<>(responseVo, HttpStatus.BAD_REQUEST);
    }

    /**
     * HTTP异常处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = HttpException.class)
    public ResponseEntity<ResponseVo<Object>> handlerHttpException(HttpException e) {
        ResponseVo<Object> responseVo = ResponseVo.ofMessage(e.getHttpStatus().value(), e.getMessage());
        responseVo.setId(getErrorId());
        log.error(INNER_EXCEPTION + getLogError(responseVo));
        return new ResponseEntity<>(responseVo, e.getHttpStatus());
    }

    /**
     * 方法参数类型不匹配异常
     *
     * @param e 异常
     * @return 结果
     */
    @ResponseBody
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ResponseVo<Object>> handlerBindException(BindException e) {
        FieldError fieldError = e.getBindingResult().getFieldErrors().get(0);
        String message = "请求参数[" + fieldError.getField() + "]类型不匹配";
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest(message);
        responseVo.setId(getErrorId());
        log.error("参数异常:" + getLogError(responseVo));
        return new ResponseEntity<>(responseVo, HttpStatus.BAD_REQUEST);
    }

    @ResponseBody
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ResponseVo<Object>> handlerMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest("参数异常，请校验参数格式");
        responseVo.setId(getErrorId());
        log.error("参数异常:" + getLogError(responseVo));
        return new ResponseEntity<>(responseVo, HttpStatus.BAD_REQUEST);
    }

    @ResponseBody
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public ResponseEntity<ResponseVo<Object>> handlerMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest("参数异常，请校验参数格式");
        responseVo.setId(getErrorId());
        log.error("参数异常:" + getLogError(responseVo));
        return new ResponseEntity<>(responseVo, HttpStatus.BAD_REQUEST);
    }

//    @ResponseBody
//    @ExceptionHandler(value = HttpClientErrorException.class)
//    public ResponseEntity<ErrorResponseDto> handlerHttpClientErrorException(HttpClientErrorException e) {
//        ErrorResponseDto dto = new ErrorResponseDto(e.getResponseBodyAsString());
//        dto.setId(getErrorId());
//        log.error("请求异常:{}, message:{}", getLogError(dto), e.getMessage());
//        return new ResponseEntity<>(dto, e.getStatusCode());
//    }
//
//    @ResponseBody
//    @ExceptionHandler(value = HttpServerErrorException.class)
//    public ResponseEntity<ErrorResponseDto> handlerHttpServerErrorException(HttpServerErrorException e) {
//        ErrorResponseDto dto = new ErrorResponseDto(e.getResponseBodyAsString());
//        dto.setId(getErrorId());
//        log.error("请求异常:{}, message:{}", getLogError(dto), e.getMessage());
//        return new ResponseEntity<>(dto, e.getStatusCode());
//    }
//
//    @ResponseBody
//    @ExceptionHandler(value = ResourceAccessException.class)
//    public ResponseEntity<ErrorResponseDto> handlerResourceAccessException(ResourceAccessException e) {
//        ErrorResponseDto dto = new ErrorResponseDto("请求超时");
//        dto.setId(getErrorId());
//        log.error("RestTemplate请求异常:{}, message:{}", getLogError(dto), e.getMessage());
//        return new ResponseEntity<>(dto, HttpStatus.REQUEST_TIMEOUT);
//    }
//
//    @ResponseBody
//    @ExceptionHandler(value = SocketTimeoutException.class)
//    public ResponseEntity<ErrorResponseDto> handlerSocketTimeoutException(SocketTimeoutException e) {
//        ErrorResponseDto dto = new ErrorResponseDto("请求超时");
//        dto.setId(getErrorId());
//        log.error("okhttp请求异常:" + getLogError(dto));
//        return new ResponseEntity<>(dto, HttpStatus.REQUEST_TIMEOUT);
//    }
//
//    @ResponseBody
//    @ExceptionHandler(value = ClientAbortException.class)
//    public ResponseEntity<ErrorResponseDto> handlerClientAbortException(ClientAbortException e) {
//        ErrorResponseDto dto = new ErrorResponseDto("客户端异常退出");
//        dto.setId(getErrorId());
//        log.error("请求异常:" + getLogError(dto));
//        return new ResponseEntity<>(dto, HttpStatus.BAD_REQUEST);
//    }
//
//    @ResponseBody
//    @ExceptionHandler(value = FeignException.class)
//    public ResponseEntity<ErrorResponseDto> handlerParamException(FeignException e) {
//        ErrorResponseDto dto = new ErrorResponseDto();
//        String uuid = getErrorId();
//        dto.setId(uuid);
//        if (e instanceof FeignException.BadRequest || e instanceof FeignException.Forbidden) {
//            String message = JSONObject.parseObject(e.contentUTF8()).getString("message");
//            dto.setMessage(message);
//            log.error("Feign请求异常:" + message + ", 异常id:" + uuid);
//            return new ResponseEntity<>(dto, HttpStatus.BAD_REQUEST);
//        } else {
//            dto.setMessage("未知异常,请联系管理员处理");
//            log.error("Feign请求异常:" + e.getMessage() + ", 异常id:" + uuid, e);
//            return new ResponseEntity<>(dto, HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    /**
     * 参数类型异常处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResponseVo<Object> handlerMediaTypeException(HttpMessageNotReadableException e) {
        ResponseVo<Object> responseVo = new ResponseVo<>(HttpStatus.BAD_REQUEST.value(), null, null);
        responseVo.setId(getErrorId());
        if (e.getCause() instanceof JsonMappingException) {
            StringBuilder stringBuilder = new StringBuilder();
            JsonMappingException exception = (JsonMappingException) e.getCause();
            for (JsonMappingException.Reference path : exception.getPath()) {
                stringBuilder.append("请求参数[").append(path.getFieldName()).append("]不正确; ");
            }
            responseVo.setMessage(stringBuilder.toString());
            log.error("参数异常:" + getLogError(responseVo));
            return responseVo;
        }
        responseVo.setMessage(e.getMessage());
        log.error("参数异常:" + getLogError(responseVo));
        return responseVo;
    }

    /**
     * 校验器异常处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseVo<Object> handlerValidatorException(MethodArgumentNotValidException e) {
        FieldError fieldError = e.getBindingResult().getFieldErrors().get(0);
        String message = "请求参数[" + fieldError.getField() + "]不正确："
                + fieldError.getDefaultMessage();
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest(message);
        responseVo.setId(getErrorId());
        log.error("请求参数异常:" + message);
        return responseVo;
    }

    /**
     * 协议异常处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ExceptionHandler(value = HttpMediaTypeException.class)
    public ResponseVo<Object> handlerMediaTypeException(HttpMediaTypeException e) {
        ResponseVo<Object> responseVo = ResponseVo.ofBadRequest(e.getMessage());
        responseVo.setId(getErrorId());
        return responseVo;
    }

    /**
     * 未捕获异常默认处理
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = Exception.class)
    public ResponseVo<Object> handlerDefaultException(Exception e) {
        ResponseVo<Object> responseVo = ResponseVo.ofMessage(HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "未知异常,请联系管理员处理");
        String uuid = getErrorId();
        responseVo.setId(uuid);
        log.error("未捕获异常:" + e.getClass().getName() + ":" + e.getMessage() + ", 异常id:" + uuid, e);
        return responseVo;
    }
}
