/**  
 * Copyright © 2019ZXun tec. All rights reserved.
 */
package com.zixun.kuniao.comm.pojo;


import com.zixun.kuniao.utils.MessageUtil;

import java.io.Serializable;

/**
 * 
 * 返回接口定义
 * <AUTHOR>
 * @date 2019年8月5日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月5日      zx             create
 *
 */
public class ResponsePoJo<T> implements Serializable {

    /**
     * 序列ID
     */
    private static final long serialVersionUID = -4313344925015997500L;

    /**
     * 状态CODE
     */
    private String ret;
    
    /**
     * 状态内容
     */
    private String msg;
    
    /**
     * 请求时间
     */
    private long restime;
    
    /**
     * 返回对象
     */
    private T data;
    
    /**
     * TOKEN
     */
    private String token;
    
    /**
     * 返回无实际内容对象
     */
    public ResponsePoJo() {
        this.restime = System.currentTimeMillis();
    }
    /**
     * 返回有内容对象
     * @param stateCode
     */
    public ResponsePoJo(String stateCode) {
        this.ret = stateCode;
        this.msg = MessageUtil.getMessage(stateCode);
        this.restime = System.currentTimeMillis();
    }
    
    /**
     * 返回成功对象
     * @param responseData
     */
    public ResponsePoJo(T responseData) {
        this.ret = "";
        this.data = responseData;
        this.restime = System.currentTimeMillis();
    }

    /**
     * 返回有内容对象
     * @param stateCode
     * @param responseData
     */
    public ResponsePoJo(String stateCode, T responseData) {
        this.ret = stateCode;
        this.msg = MessageUtil.getMessage(stateCode);
        this.data = responseData;
        this.restime = System.currentTimeMillis();
    }

    public static <T> ResponsePoJo<T> success(T data) {
        ResponsePoJo<T> responsePoJo = new ResponsePoJo<>();
        responsePoJo.ret = "0";
        responsePoJo.msg = "success";
        responsePoJo.data = data;

        return responsePoJo;
    }

    public static <T> ResponsePoJo<T> fail() {
        ResponsePoJo<T> responsePoJo = new ResponsePoJo<>();
        responsePoJo.ret = "-1";
        responsePoJo.msg = "fail";
        responsePoJo.data = null;

        return responsePoJo;
    }

    /**
     * @return the ret
     */
    public String getRet() {
        return ret;
    }
    /**
     * @param ret the ret to set
     */
    public void setRet(String ret) {
        this.ret = ret;
    }
    /**
     * @return the msg
     */
    public String getMsg() {
        return msg;
    }
    /**
     * @param msg the msg to set
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }
    /**
     * @return the restime
     */
    public long getRestime() {
        return restime;
    }
    /**
     * @param restime the restime to set
     */
    public void setRestime(long restime) {
        this.restime = restime;
    }
    /**
     * @return the data
     */
    public T getData() {
        return data;
    }
    /**
     * @param data the data to set
     */
    public void setData(T data) {
        this.data = data;
    }
    /**
     * @return the token
     */
    public String getToken() {
        return token;
    }
    /**
     * @param token the token to set
     */
    public void setToken(String token) {
        this.token = token;
    }
    
}
