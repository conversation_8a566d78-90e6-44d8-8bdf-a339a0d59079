package com.zixun.kuniao.comm.aop;

import com.zixun.kuniao.comm.exception.ExceptionUtil;
import com.zixun.kuniao.comm.exception.JwtException;
import com.zixun.kuniao.comm.exception.ValidateException;
import com.zixun.kuniao.comm.pojo.ResponsePoJo;
import com.zixun.kuniao.comm.pojo.UserToken;
import com.zixun.kuniao.comm.util.JwtUtil;
import com.zixun.kuniao.utils.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

@Aspect
@Component
@Slf4j
public class ControllerAop {


    /**
     * 用户Token
     */
    @Autowired
    private UserToken userToken;

    /**
     * 定义一个切入点
     */
    @Pointcut("execution(public * com.zixun.kuniao.*.controller.*.*(..)) &&"
            + " !execution(public * com.zixun.kuniao.notcheck.controller.*.*(..))")
    public void needCheckMethod() {
    }

    /**
     * 定义一个切入点
     */
    @Pointcut("execution(public * com.zixun.kuniao.notcheck.controller.*.*(..))")
    public void notNeedCheckMethod() {
    }

    /**
     * 需要验证的方法入口
     *
     * @param pjp
     * @return
     * @throws Throwable
     * @date: 2019年8月16日
     * @author: zx
     */
    @Around("needCheckMethod()")
    public Object doNeedCheckProfiling(ProceedingJoinPoint pjp) throws Throwable {
        // 针对token进行验证
        return callMethod(pjp, true);
    }

    /**
     * 无需验证的方法入口
     *
     * @param pjp
     * @return
     * @throws Throwable
     * @date: 2019年8月16日
     * @author: zx
     */
    @Around("notNeedCheckMethod()")
    public Object doNotNeedCheckProfiling(ProceedingJoinPoint pjp) throws Throwable {
        return callMethod(pjp, false);
    }

    /**
     * 调用方法主逻辑
     *
     * @param pjp
     * @return
     * @throws Throwable
     * @date: 2019年8月16日
     * @author: zx
     */
    @SuppressWarnings("unchecked")
    private Object callMethod(ProceedingJoinPoint pjp, boolean needCheckToken) throws Throwable {

        Object returnObject = null;
        try {
            String startInfo = String.format("开始方法----> %s", pjp.toString());
            // 打印日志所有参数
            log.info(startInfo);
            // 针对token进行验证
            if (needCheckToken) {
                renewToken();
            }
            // 执行该方法
            returnObject = pjp.proceed();
            // 封装结果
        } catch (Exception e) {
            // 异常处理
            String excptionInfo = String.format("异常方法----> %s", pjp.toString());
            String stateCode = "";
            if (ExceptionUtil.isDefinedException(e)) {
                if (e instanceof ValidateException) {
                    log.debug(excptionInfo);
                    log.debug(e.getMessage(), e);
                } else {
                    log.error(excptionInfo);
                    log.error(e.getMessage(), e);
                }
                stateCode = e.getMessage();
            } else {
                log.error(excptionInfo);
                log.error(e.getMessage(), e);
            }
            // token验证异常
            if (e instanceof JwtException) {
                stateCode = "E000002";
            }
            returnObject = new ResponsePoJo<>();
            if (StringUtils.isBlank(stateCode)) {
                stateCode = "E000001";
            }
            if (!Pattern.matches("(E|W|I)\\d{6}", stateCode) && stateCode.contains("-")) {
                stateCode = stateCode.split("-")[0];
            }
            ((ResponsePoJo<Object>) returnObject).setRet(stateCode);
            ((ResponsePoJo<Object>) returnObject).setMsg(MessageUtil.getMessage(stateCode));
            ((ResponsePoJo<Object>) returnObject).setRestime(System.currentTimeMillis());
            ((ResponsePoJo<Object>) returnObject).setData(null);
        } finally {
            String endInfo = String.format("结束方法----> %s", pjp.toString());
            if (needCheckToken && returnObject instanceof ResponsePoJo
                    && StringUtils.isNotBlank(userToken.getCustomerId())) {
                ((ResponsePoJo<Object>) returnObject).setToken(JwtUtil.createToken(userToken));
            }
            log.info(endInfo);
        }
        return returnObject;
    }

    /**
     * 从头部获取token
     *
     * @return
     * @date: 2019年8月6日
     * @author: zx
     */
    private void renewToken() throws Exception {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        String token = request.getHeader("Authorization");
        try {
            UserToken userTokenNew = JwtUtil.verifiesToken(token);
            userToken.setCustomerId(userTokenNew.getCustomerId());
            userToken.setEmail(userTokenNew.getEmail());
        } catch (Exception e) {
            throw new JwtException(e);
        }

    }

    public static void main(String[] args) throws Exception {
//        UserToken userToken = new UserToken();
//        userToken.setCustomerId("8");
//        userToken.setEmail("<EMAIL>");
//        System.out.println(JwtUtil.createToken(userToken));
        UserToken userToken = new UserToken();
        userToken.setCustomerId("2");
        userToken.setEmail("<EMAIL>");
        System.out.println(JwtUtil.createToken(userToken));
    }
}
