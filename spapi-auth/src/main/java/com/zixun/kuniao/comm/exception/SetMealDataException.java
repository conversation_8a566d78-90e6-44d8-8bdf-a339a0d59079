/**
 * Copyright (C) ZX All Rights Reserved.
 */
package com.zixun.kuniao.comm.exception;

/**
 * 
 * 套餐相关异常。<br />
 * <AUTHOR>
 * @version 1.0
 * 修改记录
 * 日期       操作人员      内容
 * 2015年8月13日   ZX     新作
 */
public class SetMealDataException extends ZxException {


    /**
     * 序列ID
     */
    private static final long serialVersionUID = -6297467677223665448L;
    
    /**
     * 参数列表
     */
    protected Object[] params;


    /**
     * 构造函数
     */
    public SetMealDataException() {
        super();
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     */
    public SetMealDataException(String code) {
        super(code);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     */
    public SetMealDataException(String code, Throwable cause) {
        super(code, cause);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param params 参数列表
     */
    public SetMealDataException(String code, Object...params) {
        super(code);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     * @param params 参数列表
     */
    public SetMealDataException(String code, Throwable cause, Object...params) {
        super(code, cause);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param cause 异常
     */
    public SetMealDataException(Throwable cause) {
        super(cause);
    }

    /**
     * @return the params
     */
    public Object[] getParams() {
        return params;
    }

    /**
     * @param params the params to set
     */
    public void setParams(Object[] params) {
        this.params = params;
    }

}
