/**
 * Copyright (C) ZX All Rights Reserved.
 */
package com.zixun.kuniao.comm.exception;

import com.zixun.kuniao.utils.MessageUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * 
 * 通常异常。<br />
 * <AUTHOR>
 * @version 1.0
 * 修改记录
 * 日期       操作人员      内容
 * 2015年8月13日   ZX     新作
 */
public class ZxException extends RuntimeException {


    /**
     * 序列ID
     */
    private static final long serialVersionUID = -6297467677223665448L;
    
    /**
     * 参数列表
     */
    protected Object[] params;


    /**
     * 构造函数
     */
    public ZxException() {
        super();
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     */
    public ZxException(String code) {
        super(code);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     */
    public ZxException(String code, Throwable cause) {
        super(code, cause);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param params 参数列表
     */
    public ZxException(String code, Object...params) {
        super(code);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     * @param params 参数列表
     */
    public ZxException(String code, Throwable cause, Object...params) {
        super(code, cause);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param cause 异常
     */
    public ZxException(Throwable cause) {
        super(cause);
    }

    /**
     * @return the params
     */
    public Object[] getParams() {
        return params;
    }

    /**
     * @param params the params to set
     */
    public void setParams(Object[] params) {
        this.params = params;
    }
    
    /*
     * (non-Javadoc)
     * @see java.lang.Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        StringBuilder stateCode = new StringBuilder(super.getMessage());
        String strMessage = MessageUtil.getMessage(stateCode.toString(), this.params);
        if (StringUtils.isNotBlank(strMessage)) {
            StringBuilder detailMessage = new StringBuilder(MessageUtil.getMessage(stateCode.toString(), this.params));
            return stateCode.append("-").append(detailMessage).toString();
        } else {
            return stateCode.toString();
        }
        
        
    }
}
