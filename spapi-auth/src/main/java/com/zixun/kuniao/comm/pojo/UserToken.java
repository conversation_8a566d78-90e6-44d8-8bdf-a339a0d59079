/**  
 * Copyright © 2019zx tec. All rights reserved.
 */
package com.zixun.kuniao.comm.pojo;

import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;

/**
 * 用户Token信息
 * <AUTHOR>
 * @date 2019年8月6日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月6日      zx             create
 * 
 */
@Component
@Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class UserToken {

    /**
     * 客户ID
     */
    private String customerId;
    
    /**
     * 邮件地址
     */
    private String email;
    
    /**
     * 可活日期
     */
    private Date verifyDate;

    /**
     * @return the customerId
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * @param customerId the customerId to set
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the verifyDate
     */
    public Date getVerifyDate() {
        return verifyDate;
    }

    /**
     * @param verifyDate the verifyDate to set
     */
    public void setVerifyDate(Date verifyDate) {
        this.verifyDate = verifyDate;
    }

}
