/**  
 * Copyright © 2017zx tec. All rights reserved.
 */

package com.zixun.kuniao.comm.exception;

/**
 * 异常操作工具
 * <AUTHOR>
 * @date May 3, 2017
 * @version 1.0
 *
 * operation
 * date         operator            content
 * May 3, 2017      zx             create
 * 
 */
public class ExceptionUtil {

    /**
     * 空构造函数，禁止实例化
     */
    private ExceptionUtil() {
        
    }
    
    /**
     * 判断是否是已经定义过的异常
     * @param e 异常
     * @return 判断结果
     */
    public static boolean isDefinedException(Exception e) {
        
        boolean checkResult = false;
        if (e instanceof ValidateException) {
            checkResult = true;
        } else if (e instanceof ZxException) {
            checkResult = true;
        } else if (e instanceof AuthException) {
            checkResult = true;
        } else if (e instanceof MismatchingSetMealException) {
            checkResult = true;
        } else if (e instanceof SetMealDataException) {
            checkResult = true;
        } else if (e instanceof SetMealNoEnoughException) {
            checkResult = true;
        } else if (e instanceof SetMealTypeException) {
            checkResult = true;
        } else if (e instanceof ZxPgException) {
            checkResult = true;
        }
        return checkResult;
    }
    
    /**
     * 
     * 取异常参数
     * @date:  May 3, 2017
     * @author: ZXun
     * @param e
     * @return
     */
    public static Object[] getExceptionParams(Exception e) {
        
        ZxException je = (ZxException)e;
        return je.getParams();
    }
}
