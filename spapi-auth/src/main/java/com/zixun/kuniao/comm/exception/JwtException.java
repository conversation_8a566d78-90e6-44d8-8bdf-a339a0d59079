/**
 * Copyright (C) ZX All Rights Reserved.
 */
package com.zixun.kuniao.comm.exception;

/**
 * 
 * token异常
 * <AUTHOR>
 * @date 2019年8月20日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月20日      zx             create
 *
 */
public class JwtException extends RuntimeException {


    /**
     * 序列ID
     */
    private static final long serialVersionUID = 817696115794504986L;
    
    /**
     * 参数列表
     */
    protected Object[] params;


    /**
     * 构造函数
     */
    public JwtException() {
        super();
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     */
    public JwtException(String code) {
        super(code);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     */
    public JwtException(String code, Throwable cause) {
        super(code, cause);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param params 参数列表
     */
    public JwtException(String code, Object...params) {
        super(code);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     * @param params 参数列表
     */
    public JwtException(String code, Throwable cause, Object...params) {
        super(code, cause);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param cause 异常
     */
    public JwtException(Throwable cause) {
        super(cause);
    }

    /**
     * @return the params
     */
    public Object[] getParams() {
        return params;
    }

    /**
     * @param params the params to set
     */
    public void setParams(Object[] params) {
        this.params = params;
    }

}
