/**  
 * Copyright © 2019zx tec. All rights reserved.
 */
package com.zixun.kuniao.comm.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.zixun.kuniao.comm.pojo.UserToken;
import com.zixun.kuniao.utils.DateUtil;

import java.util.Date;

/**
 * 秘钥token工具类
 * <AUTHOR>
 * @date 2019年8月6日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月6日      zx             create
 * 
 */
public class JwtUtil {
    
    public static void main(String[] args) throws Exception {
        UserToken userToken = new UserToken();
        userToken.setCustomerId("ABC");
        userToken.setEmail("<EMAIL>");
        userToken.setVerifyDate(DateUtil.addHour(new Date(), 2));
        
        System.out.println(JwtUtil.createToken(userToken));
    }
    
    /**
     * 安全验证码
     */
    private static String SECRET_KEY = "thisnewdiy2019";

    /**
     * 
     * 创建JWT
     * @date:  2019年8月6日
     * @author: zx
     * @param userToken
     * @return
     * @throws Exception
     */
    public static String createToken(UserToken userToken) throws Exception {
        Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
        String token = JWT.create()
                .withIssuer(userToken.getCustomerId())
                .withClaim("email", userToken.getEmail())
                .withExpiresAt(DateUtil.addHour(new Date(), 2))
                .sign(algorithm);
        return token;
    }
    
    /**
     * 
     * 验证JWT
     * @date:  2018年1月2日
     * @author: zx
     * @param token TOKEN KEY
     * @return 用户ID
     * @throws Exception
     */
    public static UserToken verifiesToken(String token) throws Exception {
        Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
        DecodedJWT jwt = JWT.decode(token);
        UserToken userToken = new UserToken();
        userToken.setCustomerId(jwt.getIssuer());
        userToken.setEmail(jwt.getClaim("email").asString());
        userToken.setVerifyDate(jwt.getExpiresAt());
        
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(userToken.getCustomerId())
                .withClaim("email", userToken.getEmail())
                .build(); //Reusable verifier instance
        verifier.verify(token);
        return userToken;
    }
}
