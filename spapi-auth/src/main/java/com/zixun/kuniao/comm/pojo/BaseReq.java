/**  
 * Copyright © 2019zx tec. All rights reserved.
 */
package com.zixun.kuniao.comm.pojo;

/**
 * 
 * REQ基本类
 * <AUTHOR>
 * @date 2019年8月6日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月6日      zx             create
 *
 */
public class BaseReq {
    
    /**
     * 当前页
     */
    private int page = 0;

    /**
     * 一页件数
     */
    private int limit = 50;
    
    /**
     * 
     * 获得记录起始序列
     * @date:  2019年8月6日
     * @author: zx
     * @return
     */
    public int getBegin() {
        if (page > 0) {
            return (page - 1) * limit;
        } else {
            return 0;
        }
        
    }

    /**
     * @return the page
     */
    public int getPage() {
        return page;
    }

    /**
     * @param page the page to set
     */
    public void setPage(int page) {
        this.page = page;
    }

    /**
     * @return the limit
     */
    public int getLimit() {
        return limit;
    }

    /**
     * @param limit the limit to set
     */
    public void setLimit(int limit) {
        this.limit = limit;
    }

}
