/**
 * Copyright (C) ZX All Rights Reserved.
 */
package com.zixun.kuniao.comm.exception;

/**
 * 
 * 验证异常。<br />
 * <AUTHOR>
 * @version 1.0
 * 修改记录
 * 日期       操作人员      内容
 * 2015年8月13日   ZX     新作
 */
public class ValidateException extends ZxException {


    /**
     * 序列ID
     */
    private static final long serialVersionUID = -6297467677223665448L;
    
    /**
     * 错误项目名
     */
    private String faildItemName;
    

    /**
     * 构造函数
     */
    public ValidateException() {
        super();
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     */
    public ValidateException(String code) {
        super(code);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param faildItemName 错误项目名
     */
    public ValidateException(String code, String faildItemName) {
        super(code);
        this.faildItemName = faildItemName;
    }
    
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param params 参数列表
     */
    public ValidateException(String code, Object...params) {
        super(code);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param cause 异常
     * @param params 参数列表
     */
    public ValidateException(String code, Throwable cause, Object...params) {
        super(code, cause);
        this.setParams(params);
    }
    
    /**
     * 构造函数
     * @param code 错误ID
     * @param faildItemName 错误项目名
     * @param cause 异常
     */
    public ValidateException(String code, String faildItemName, Throwable cause) {
        super(code, cause);
        this.faildItemName = faildItemName;
    }
    
    /**
     * 获取错误项目名
     * @return 错误项目名
     */
    public String getFaildItemName() {
        return this.faildItemName;
    }
}
