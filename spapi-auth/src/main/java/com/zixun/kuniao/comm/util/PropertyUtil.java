/**  
 * Copyright © 2019zx tec. All rights reserved.
 */
package com.zixun.kuniao.comm.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;

/**
 * 参数工具类
 * <AUTHOR>
 * @date 2019年8月6日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2019年8月6日      zx             create
 * 
 */
@Slf4j
public class PropertyUtil {


    /**
     * 系统配置信息
     */
    private static Properties propertiesConfig;
    
    
    /**
     * 系统配置文件名
     */
    private static String PROPERTIES_COMM_FILE = "system_config.properties";


    /**
     * 禁止工具类实例化
     */
    private PropertyUtil() {

    }

    // 初始化加载消息
    static {
        try {
            propertiesConfig = PropertiesLoaderUtils.loadAllProperties(PROPERTIES_COMM_FILE);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 
     * 获取指定消息
     * TODO
     * @date:  2019年8月6日
     * @author: zx
     * @param key
     * @return
     */
    public static String getConfig(String key) {
        return propertiesConfig.getProperty(key);
    }

}
