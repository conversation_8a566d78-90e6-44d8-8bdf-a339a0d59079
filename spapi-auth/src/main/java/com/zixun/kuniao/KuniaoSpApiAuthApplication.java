package com.zixun.kuniao;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableAsync
@EnableApolloConfig
@SpringBootApplication(scanBasePackageClasses = {KuniaoSpApiAuthApplication.class}
        , exclude = DataSourceAutoConfiguration.class)
public class KuniaoSpApiAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(KuniaoSpApiAuthApplication.class, args);
    }
}


