package com.zixun.kuniao.notcheck.controller;


import com.zixun.kuniao.comm.exception.ValidateException;
import com.zixun.kuniao.comm.pojo.ResponsePoJo;
import com.zixun.kuniao.notcheck.pojo.*;
import com.zixun.kuniao.notcheck.service.AuthorizeV1Service;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.zixun.kuniao.utils.Contants.CLIENT_IP;

/**
 * 酷鸟授权服务
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/authorize")
public class AuthorizeV2Controller {

    @Autowired
    private AuthorizeV1Service authorizeV1Service;


    /**
     * 亚马逊店铺授权地址
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "亚马逊店铺授权地址")
    @PostMapping("/v2/authorize-url")
    public ResponsePoJo<String> authorizeUrl(AuthorizeUrlV2Req req, HttpServletRequest request) throws Exception {
        String nowIp = (String) request.getAttribute(CLIENT_IP);
        if (StringUtils.isBlank(nowIp)) {
            throw new ValidateException("E000005", "用户来源缺失");
        }
        // 验证参数
        if (StringUtils.isBlank(req.getRegion())) {
            throw new ValidateException("E000004", "缺少必需的参数region");
        }
        if (StringUtils.isBlank(req.getAppId())) {
            throw new ValidateException("E000006", "缺少必需的参数appId");
        }

        ResponsePoJo<String> res = new ResponsePoJo<>();
        log.info("授权请求：{}", req);
        res.setData(authorizeV1Service.authorizeUrlV2(req.getState(), req.getRegion(), req.getAppId(), nowIp));
        return res;
    }

}
