package com.zixun.kuniao.notcheck.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/4/15
 */
@Configuration
@ConfigurationProperties(prefix = "aws.authorize")
public class AuthConfig {
    private Map<String, String> url;

    private Map<String, String> vendorUrl;

    public Map<String, String> getUrl() {
        return url;
    }

    public void setUrl(Map<String, String> url) {
        this.url = url;
    }

    public Map<String, String> getVendorUrl() {
        return vendorUrl;
    }

    public void setVendorUrl(Map<String, String> vendorUrl) {
        this.vendorUrl = vendorUrl;
    }
}
