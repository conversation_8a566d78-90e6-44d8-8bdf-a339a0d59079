package com.zixun.kuniao.notcheck.pojo;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AuthorizeUrlReq {

    private String state;

    private String region;

    private String marketplace;

    private String central;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMarketplace() {
        return marketplace;
    }

    public void setMarketplace(String marketplace) {
        this.marketplace = marketplace;
    }

    public String getCentral() {
        return central;
    }

    public void setCentral(String central) {
        this.central = central;
    }
}
