package com.zixun.kuniao.notcheck.service;

import com.fzzixun.appstore.isvNotify.common.po.NotifyPo;
import com.fzzixun.appstore.isvNotify.common.service.NotifyMicroService;
import com.zixun.kuniao.notcheck.enums.AuthType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CallbackService {

    private final NotifyMicroService notifyMicroService;

    /**
     * 广告token 推送
     */
    public static final Integer CALLBACK_MESSAGE_TYPE_ADS_TOKEN = 221;

    /**
     * spapi token 推送
     */
    public static final Integer CALLBACK_MESSAGE_TYPE_SPAPI_TOKEN = 222;

    /**
     * @param appKey
     * @param data
     * @param authType
     * @return
     */
    public String sendCallback(String appKey, String data, AuthType authType) {
        log.info("推送通知！" + appKey + "," + authType + "," + data);
        try {
            NotifyPo notifyPo = new NotifyPo();
            notifyPo.setAppKey(appKey);
            notifyPo.setCompanyId(0L);
            //222:亚马逊spapi授权，221:亚马逊广告api授权
            notifyPo.setMessageType(AuthType.SPAPI.equals(authType) ? CALLBACK_MESSAGE_TYPE_SPAPI_TOKEN : CALLBACK_MESSAGE_TYPE_ADS_TOKEN);
            notifyPo.setData(data);
            String result = notifyMicroService.sentNotify(notifyPo);
            if (!"success".equals(result)) {
                log.error("推送通知失败！result:{} " + appKey + "," + authType + "," + data, result);
            }
            return result;
        } catch (Exception e) {
            log.error("推送通知异常error：" + appKey + "," + authType + "," + data, e);
        }
        return null;
    }
}
