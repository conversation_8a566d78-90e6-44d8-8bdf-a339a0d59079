package com.zixun.kuniao.notcheck.config;

import com.zixun.kuniao.notcheck.interceptor.RequestIpInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @create 2022/5/9
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private RequestIpInterceptor requestIpInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(requestIpInterceptor)
                .addPathPatterns("/authorize/authorize-url")
                .addPathPatterns("/authorize/v2/authorize-url");
    }
}
