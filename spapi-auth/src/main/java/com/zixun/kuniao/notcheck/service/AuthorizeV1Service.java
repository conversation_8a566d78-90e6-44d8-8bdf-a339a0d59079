package com.zixun.kuniao.notcheck.service;


import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.zixun.kuniao.comm.exception.ValidateException;
import com.zixun.kuniao.notcheck.config.AuthConfig;
import com.zixun.kuniao.notcheck.enums.AuthType;
import com.zixun.kuniao.notcheck.pojo.AuthSellerCallbackReq;
import com.zixun.kuniao.notcheck.pojo.AuthSellerCallbackRes;
import com.zixun.kuniao.notcheck.pojo.AuthorizeUrlRes;
import com.zixun.kuniao.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 授权服务
 */
@Slf4j
@Service("authorizeV1Service")
public class AuthorizeV1Service {

    @Value("${proxy.index}")
    private String proxyIndex;

    @Value("${auth.index}")
    private String authIndex;

    @Value("${application_id}")
    private String applicationId;

    @Value("${application_id_test}")
    private String applicationIdTest;

    @Autowired
    private AuthConfig authConfig;

    @Value("${testIpList}")
    private String testIpList;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${stateExpiredHours:1}")
    private Integer stateExpiredHours;

    @Value("${enabledStateEncrypt:false}")
    private Boolean enabledStateEncrypt;

    @Value("${illegal.state.alert.token:c49856ae7e115277ff9d434fa0c267425d2eb8f2621b7f18846e6d47b3f2c146}")
    private String illegalStateAlertToken;

    // ================================飞书机器人==================================================
    // 告警机器人
    @Value("${param.feishu.warning-notify.token:9f461fa1-1f69-48e4-b060-d9e7f3b7c9d4}")
    private String feishuWarningNotifyToken;

    @Value("${param.feishu.warning-notify.secret:xItIkoPRND0wQdZigTtrF}")
    private String feishuWarningNotifySecret;
    /**
     * 亚马逊店铺授权地址
     *
     * @return
     */
    public AuthorizeUrlRes authorizeUrl(String reqState, String region,
                                        String nowIp, String central, String marketplace) {
        AuthorizeUrlRes res = new AuthorizeUrlRes();
        String state;
        if (StringUtils.isNotBlank(reqState)) {
            state = URLEncoder.encode(reqState);
        } else {
            state = UUID.randomUUID().toString().replaceAll("-", "");
        }

        String stateKey = state;
        if (enabledStateEncrypt) {
            SecureUtil.disableBouncyCastle();
            stateKey = IdUtil.fastSimpleUUID();
            String encryptResult = AesUtil.encryptBase64(state, AesUtil.defaultKey);
            log.info("加密state:{} ，加密结果（Base64）: {}， key：{}", state, encryptResult, stateKey);
            if (StringUtils.isBlank(encryptResult)) {
                throw new ValidateException("E000001", "state处理失败");
            }

            //stateKey存入redis,encryptBase64为值，默认1小时过期
            try {
                redisTemplate.opsForValue().set(stateKey, encryptResult, stateExpiredHours, TimeUnit.HOURS);
            } catch (Exception e) {
                throw new ValidateException("E000001", "state处理失败");
            }
        }


        // 生成授权所需要的参数
        String params = "application_id=" + applicationId + "&state=" + stateKey;
        if (StringUtils.isNotBlank(testIpList)) {
            // 如果是测试IP需要用测试应用授权
            List<String> ipLists = Arrays.asList(testIpList.split(","));
            if (ipLists.contains(nowIp)) {
                params = "application_id=" + applicationIdTest + "&state=" + stateKey;
            }
        }

        String url;
        if ("seller".equals(central)) {
            log.info("aws.authorize.url." + region.toLowerCase());
            url = authConfig.getUrl().get(region.toLowerCase()) + "/apps/authorize/consent?" + params;
        } else {
            //如果marketplace包含空格，将空格替换为-
            marketplace = marketplace.replaceAll(" ", "-");
            //如果marketplace等于U.A.E.，将marketplace转换为uae
            if ("U.A.E.".equals(marketplace)) {
                marketplace = "uae";
            }
            log.info("aws.authorize.vendor." + marketplace.toLowerCase());
            String domain = authConfig.getVendorUrl().get(marketplace.toLowerCase());
            if (domain == null) {
                //抛异常
                throw new ValidateException("E060009", "授权失败，未定义该站点");
            }
            url = domain + "/apps/authorize/consent?" + params;
        }
        log.info(url);
        res.setUrl(url);
        return res;
    }

    public String authorizeUrlV2(String reqState, String region, String appId, String nowIp) throws UnsupportedEncodingException, URISyntaxException {
        //V2版本新增自定义参数
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("appId", appId);
        if (StringUtils.isNotBlank(reqState)) {
            queryParams.put("extInfo", reqState);
        }
        String stateParam = addQueryParamsToURL(authIndex + "/authorize/finish", queryParams);
        // 生成授权所需要的参数
        String params = "application_id=" + applicationId + "&state=" + URLEncoder.encode(stateParam, "UTF-8");
        if (StringUtils.isNotBlank(testIpList)) {
            // 如果是测试IP需要用测试应用授权
            List<String> ipLists = Arrays.asList(testIpList.split(","));
            if (ipLists.contains(nowIp)) {
                params = "application_id=" + applicationIdTest + "&state=" + URLEncoder.encode(stateParam, "UTF-8");
            }
        }
        log.info("aws.authorize.url." + region.toLowerCase());
        String url = authConfig.getUrl().get(region.toLowerCase()) + "/apps/authorize/consent?" + params;
        log.info(url);
        return url;
    }

    public static String addQueryParamsToURL(String url, Map<String, String> queryParams) throws URISyntaxException, UnsupportedEncodingException {
        if (queryParams != null && !queryParams.isEmpty()) {
            URI uri = new URI(url);
            String query = uri.getQuery();
            StringBuilder sb = new StringBuilder(query != null ? query : "");
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                String param = entry.getKey();
                String value = URLEncoder.encode(entry.getValue(), "UTF-8");
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(param).append("=").append(value);
            }
            query = sb.toString();
            return new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), query, uri.getFragment()).toString();
        }
        return url;
    }

    /**
     * 亚马逊物流授权回调接口
     *
     * @param req
     * @throws Exception
     * @returnAuthorizeV1Service
     */
    public AuthSellerCallbackRes shopAuthSellerCallback(AuthSellerCallbackReq req, HttpServletResponse response)
            throws Exception {
        AuthSellerCallbackRes res = new AuthSellerCallbackRes();
        log.info(String.format("参数===========Spapi_oauth_code:%s", req.getSpapi_oauth_code()));
        log.info(String.format("参数===========Mws_auth_token:%s", req.getMws_auth_token()));
        log.info(String.format("参数===========State:%s", req.getState()));
        log.info(String.format("参数===========Selling_partner_id:%s", req.getSelling_partner_id()));

        // 验证参数
        if (StringUtils.isBlank(req.getSelling_partner_id())) {
            throw new ValidateException("E060002", "缺少必需的参数sellingPartnerId");
        }
        if (StringUtils.isBlank(req.getSpapi_oauth_code())) {
            throw new ValidateException("E060004", "缺少必需的参数spapiOauthCode");
        }
        if (StringUtils.isBlank(req.getState())) {
            throw new ValidateException("E060005", "缺少必需的参数state，验证失败，停止授权");
        }

        String tokenUrl = proxyIndex + "/cn/auth/o2/token";
        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", req.getSpapi_oauth_code());
        params.put("redirect_uri", authIndex + "/authorize/callback");

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");

        // 获取店铺token
        String result = HttpUtil.post(headMap, tokenUrl, JsonUtil.mapToJson(params));
        log.info(result);

        if (StringUtils.isNotBlank(result)) {
            Map<String, Object> resMap = JsonUtil.jsonToMap(result);
            if (resMap.get("access_token") == null) {
                throw new ValidateException("E060001", result);
            }

            res.setAccessToken(resMap.get("access_token").toString());
            res.setTokenType(resMap.get("token_type").toString());
            res.setExpiresIn(resMap.get("expires_in").toString());
            res.setRefreshToken(resMap.get("refresh_token").toString());
            if (req.getState().contains("http")) {
                res.setSellingPartnerId(req.getSelling_partner_id());
                res.setMwsAuthToken(req.getMws_auth_token());
                // 发送参数到外部接口
                try {
                    String redirectUrl = URLDecoder.decode(req.getState(), "utf-8");
                    String urlParams = "accessToken=" + URLEncoder.encode(res.getAccessToken(), "utf-8")
                            + "&tokenType=" + res.getTokenType()
                            + "&expiresIn=" + res.getExpiresIn()
                            + "&refreshToken=" + URLEncoder.encode(res.getRefreshToken(), "utf-8")
                            + "&sellingPartnerId=" + res.getSellingPartnerId()
                            + "&mwsAuthToken=" + res.getMwsAuthToken();
                    if (redirectUrl.contains("?")) {
                        redirectUrl += "&" + urlParams;
                    } else {
                        redirectUrl += "?" + urlParams;
                    }
                    response.sendRedirect(redirectUrl);
                    res.setIsRedirect(1);
                } catch (Exception ex) {
                    log.error(String.format("调用外部接口失败， 接口地址： %s， 参数：%s", req.getState(), JsonUtil.objectToJson(res)));
                }
            } else {
                res.setIsRedirect(0);
            }
        }
        return res;
    }


    /**
     * 亚马逊店铺授权回调接口
     *
     * @param req
     * @return
     * @throws Exception
     */
    public AuthSellerCallbackRes authSellerCallback(AuthSellerCallbackReq req, HttpServletResponse response) throws Exception {
        AuthSellerCallbackRes res = new AuthSellerCallbackRes();

        log.info(String.format("参数===========Spapi_oauth_code:%s", req.getSpapi_oauth_code()));
        log.info(String.format("参数===========Mws_auth_token:%s", req.getMws_auth_token()));
        log.info(String.format("参数===========State:%s", req.getState()));
        log.info(String.format("参数===========Selling_partner_id:%s", req.getSelling_partner_id()));

        // 验证参数
        if (StringUtils.isBlank(req.getSelling_partner_id())) {
            //授权失败
            response.sendRedirect(authIndex + "/spapi/auth/failure.html");
            res.setIsRedirect(1);
            return res;
//            throw new ValidateException("E060002", "缺少必需的参数sellingPartnerId");
        }
        if (StringUtils.isBlank(req.getSpapi_oauth_code())) {
            //授权失败
            response.sendRedirect(authIndex + "/spapi/auth/failure.html");
            res.setIsRedirect(1);
            return res;
//            throw new ValidateException("E060004", "缺少必需的参数spapiOauthCode");
        }
        if (StringUtils.isBlank(req.getState())) {
            log.info("state is null, redirect to login page.");
            res.setIsRedirect(2);
            return res;
//            throw new ValidateException("E060005", "缺少必需的参数state，验证失败，停止授权");
        }

//        String appId = getQueryParamValue(req.getState(), "appId");
//        log.info("获取appId:{}", appId);
        String decryptState = req.getState();
        if (enabledStateEncrypt) {
            // 获取state
            String stateValue;
            try {
                //判断redis中是否有某个key
                if (Boolean.FALSE.equals(redisTemplate.hasKey(req.getState()))) {
                    log.error("找不到state!{} ", req.getState());
                    FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【SPAPI】找不到state， 授权失败！" + req.getState(), false);
                    //授权失败
                    response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                    res.setIsRedirect(1);
                    return res;
                }
                stateValue = (String) redisTemplate.opsForValue().get(req.getState());
            } catch (IOException e) {
                log.error("获取state异常! state：{}", req.getState(), e);
                FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【SPAPI】获取state异常， 授权失败！ " + req.getState(), false);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                res.setIsRedirect(1);
                return res;
            }
            // 执行解密
            SecureUtil.disableBouncyCastle();
            decryptState = AesUtil.decryptStr(stateValue, AesUtil.defaultKey);
            log.error("解密decryptState:{}，key:{}，value:{}", decryptState, req.getState(), stateValue);
            if (StringUtils.isBlank(decryptState)) {
                log.error("解密decryptState失败:{}，key:{}，value:{}", decryptState, req.getState(), stateValue);
                FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【SPAPI】解密decryptState失败， 授权失败! " + req.getState(), false);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                res.setIsRedirect(1);
                return res;
            }
        }

        String appId = null;

        //获取token
        String result;
        try {
            result = getTokenByCode(req.getSpapi_oauth_code());
        } catch (Exception e) {
            log.error("根据code获取token异常! {}", req.getSpapi_oauth_code(), e);
            //授权失败
            response.sendRedirect(authIndex + "/spapi/auth/failure.html");
            res.setIsRedirect(1);
            return res;
        }
        if (StringUtils.isNotBlank(appId)) {
            //V2授权
            if (StringUtils.isBlank(result)) {
                log.error("token result blank! {}", appId);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                res.setIsRedirect(1);
                return res;
            }

            Map<String, Object> resMap = JsonUtil.jsonToMap(result);
            if (resMap.get("access_token") == null) {
                log.error("token is null! {}", appId);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                res.setIsRedirect(1);
                return res;
            }

            res.setAccessToken(resMap.get("access_token").toString());
            res.setTokenType(resMap.get("token_type").toString());
            res.setExpiresIn(resMap.get("expires_in").toString());
            res.setRefreshToken(resMap.get("refresh_token").toString());
            res.setSellingPartnerId(req.getSelling_partner_id());
            res.setMwsAuthToken(req.getMws_auth_token());
            String extInfo = getQueryParamValue(req.getState(), "extInfo");
            log.info("获取extInfo:{}", extInfo);
            if (StringUtils.isNotBlank(extInfo)) {
                //推送数据添加extInfo
                res.setState(extInfo);
            }
            //推送令牌
            String sendResult = callbackService.sendCallback(appId, JSONObject.toJSONString(res), AuthType.SPAPI);
            if (StringUtils.equals(sendResult, "success")) {
                log.info("授权成功! {}, {}", appId, sendResult);
                //授权成功
                response.sendRedirect(authIndex + "/spapi/auth/success.html");
                res.setIsRedirect(1);
                return res;
            } else {
                log.error("令牌推送失败! {}, {}", appId, sendResult);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                res.setIsRedirect(1);
                return res;
            }

        } else {
            //V1授权
            if (StringUtils.isNotBlank(result)) {
                Map<String, Object> resMap = JsonUtil.jsonToMap(result);
                if (resMap.get("access_token") == null) {
                    log.error("token获取失败! {}", result);
                    //授权失败
                    response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                    res.setIsRedirect(1);
                    return res;
//                    throw new ValidateException("E060001", result);
                }

                res.setAccessToken(resMap.get("access_token").toString());
                res.setTokenType(resMap.get("token_type").toString());
                res.setExpiresIn(resMap.get("expires_in").toString());
                res.setRefreshToken(resMap.get("refresh_token").toString());

                if (decryptState.contains("http")) {
                    res.setSellingPartnerId(req.getSelling_partner_id());
                    res.setMwsAuthToken(req.getMws_auth_token());
                    // 发送参数到外部接口
                    try {
                        String redirectUrl = URLDecoder.decode(decryptState, "utf-8");
                        String urlParams = "accessToken=" + URLEncoder.encode(res.getAccessToken(), "utf-8")
                                + "&tokenType=" + res.getTokenType()
                                + "&expiresIn=" + res.getExpiresIn()
                                + "&refreshToken=" + URLEncoder.encode(res.getRefreshToken(), "utf-8")
                                + "&sellingPartnerId=" + res.getSellingPartnerId()
                                + "&mwsAuthToken=" + res.getMwsAuthToken();
                        if (redirectUrl.contains("?")) {
                            redirectUrl += "&" + urlParams;
                        } else {
                            redirectUrl += "?" + urlParams;
                        }
                        response.sendRedirect(redirectUrl);
                        res.setIsRedirect(1);
                    } catch (Exception ex) {
                        log.error(String.format("调用外部接口失败， 接口地址： %s， 参数：%s", decryptState, JsonUtil.objectToJson(res)), ex);
                    }
                } else {
                    res.setIsRedirect(0);
                }
            }
            return res;
        }
    }

    private String getTokenByCode(String code) throws Exception {
        String tokenUrl = proxyIndex + "/auth/o2/token";

        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        params.put("redirect_uri", authIndex + "/authorize/callback");

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");

        // 获取店铺token
        String result = HttpUtil.post(headMap, tokenUrl, JsonUtil.mapToJson(params));
//        String result = "{\"access_token\":\"Atza|IwEBINxHNlgjaFbwbQ6fkdYeqxEEHToR4TLCOy4niWYvbIWR3v56qgz2fJ6TN1vhfweUBsGkoLOUR5u12cr08YTVKzPXwBqdY6xqYwmRdA9UDb-zTbDhIfCc4vWk2bIWfrD243lKfqlKNiT6yqzV2MpotNz1I9NX8WZCTJCS0qKQLdPYg0mHNOFKzERa6RKrn1koS01Vdft7qGx85sIzs0EamAtnZ47PpY76OQQ7N3jNVHygVCa8ueL459YUG6__JLj2HdN_bx0KV4ENg_NI4UgPwr6QQjBcyaZOsdJ_2OZic3N-YVEVgV2EPXwxmKQ-4o0QF-xIO7kptt9xKcNteOPtzNjA\",\"refresh_token\":\"Atzr|IwEBICmPWMh7X10njJMvPg8Lk3hjT-8VCQ--xPxwc3cW1HguvtrhUNHk7l2kfss2Zb4biyHDUxxtlIa45KErVFGTZ7t95ItyvkLU891M1fLCirYr-hh8KvHDK2ahi8M2CwjxtjwHytGxM9XMgy0MqxMnFu46oZNnvUW4_bfSyBYYBRVHTDTwBIykmbkAvwtBTkMOGm1KoibeCtp8Qk8f4bn6CaJUTNYazjxvBHBHDzz98QgOrRhIfnGEwwrLqpYojhWThdCNbASRqqq5BpbjcSkunrvMDZVQu0hhIZWQYhas7u73ejhI6oz6UzFSXEfNQsnkGCI\",\"token_type\":\"bearer\",\"expires_in\":3600}";
        log.info(result);
        return result;
    }

    private String getQueryParamValue(String url, String param) {
        try {
            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                String[] queryParams = query.split("&");
                for (String queryParam : queryParams) {
                    String[] paramParts = queryParam.split("=");
                    if (paramParts.length == 2 && paramParts[0].equals(param)) {
                        return URLDecoder.decode(paramParts[1], "UTF-8");
                    }
                }
            }
        } catch (URISyntaxException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

}

