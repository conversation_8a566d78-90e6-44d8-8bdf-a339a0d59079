package com.zixun.kuniao.notcheck.service;

import org.slf4j.LoggerFactory;
import org.slf4j.impl.StaticLoggerBinder;

public class LogImplementationDetector {
    public static void main(String[] args) {
        try {
            StaticLoggerBinder binder = StaticLoggerBinder.getSingleton();
            System.out.println("Logging implementation: " + binder.getLoggerFactoryClassStr());
            System.out.println("Location: " + StaticLoggerBinder.class.getProtectionDomain().getCodeSource().getLocation());
        } catch (Exception e) {
            System.out.println("SLF4J is not bound to a logging implementation.");
        }
    }
}
