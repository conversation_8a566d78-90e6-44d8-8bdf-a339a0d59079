package com.zixun.kuniao.notcheck.controller;


import com.zixun.kuniao.comm.exception.ValidateException;
import com.zixun.kuniao.comm.pojo.BaseRes;
import com.zixun.kuniao.comm.pojo.ResponsePoJo;
import com.zixun.kuniao.notcheck.pojo.*;
import com.zixun.kuniao.notcheck.service.AuthorizeV1Service;
import com.zixun.kuniao.utils.JsonUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Enumeration;

import static com.zixun.kuniao.utils.Contants.CLIENT_IP;

/**
 * 酷鸟授权服务
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/authorize")
public class AuthorizeV1Controller {


    @Value("${auth.index}")
    private String authIndex;

    /**
     * 授权重定向地址
     */
    @Value("${auth.redirect.url:https://www.koniao.com}")
    private String authRedirectUrl;

    @Value("${auth.finish.url:https://authorize.koniao.com/authorize/finish}")
    private String authFinishUrl;


    @Autowired
    private AuthorizeV1Service authorizeV1Service;


    /**
     * 亚马逊店铺授权地址
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "亚马逊店铺授权地址")
    @GetMapping("authorize-url")
    public ResponsePoJo<AuthorizeUrlRes> authorizeUrl(AuthorizeUrlReq req, HttpServletRequest request) throws Exception {
        String nowIp = (String) request.getAttribute(CLIENT_IP);
        if (StringUtils.isBlank(nowIp)) {
            throw new ValidateException("E000005", "用户来源缺失");
        }
        // 验证参数
        if (StringUtils.isBlank(req.getState())) {
            throw new ValidateException("E000003", "缺少必需的参数state");
        }
        if (StringUtils.isBlank(req.getRegion())) {
            throw new ValidateException("E000004", "缺少必需的参数region");
        }

        //central只能填vendor、seller，否则抛异常。默认为seller
        if (StringUtils.isNotBlank(req.getCentral()) && !"seller".equals(req.getCentral()) && !"vendor".equals(req.getCentral())) {
            throw new ValidateException("E060008", "central只能填vendor｜seller");
        }

        //如果central不为空则marketplace必填
        if (StringUtils.isNotBlank(req.getCentral())) {
            if (StringUtils.isBlank(req.getMarketplace())) {
                throw new ValidateException("E060007", "central不为空则marketplace必填");
            }
        }

        //central默认为seller
        if (StringUtils.isBlank(req.getCentral())) {
            req.setCentral("seller");
        }


        ResponsePoJo<AuthorizeUrlRes> res = new ResponsePoJo<>();
        log.info("授权请求：{}", req);
        res.setData(authorizeV1Service.authorizeUrl(req.getState(), req.getRegion(), nowIp, req.getCentral(), req.getMarketplace()));
        res.setRet("0");
        res.setMsg("success");
        return res;
    }


    /**
     * 亚马逊店铺授权回调接口
     *
     * @param req
     * @param response
     * @throws Exception
     */
    @ApiOperation(value = "亚马逊店铺授权回调接口")
    @GetMapping("finish")
    public void authSellerCallback(AuthSellerCallbackReq req,
                                   HttpServletResponse response) throws Exception {
        AuthSellerCallbackRes result = authorizeV1Service.authSellerCallback(req, response);
        if (result.getIsRedirect() == 1) {
            return;
        }
        if (result.getIsRedirect() == 2) {
            response.sendRedirect(authRedirectUrl);
            return;
        }
        response.sendRedirect(authIndex + "/spapi/callback/index.html?accessToken=" +
                URLEncoder.encode(result.getAccessToken(), "UTF-8") +
                "&tokenType=" + result.getTokenType() +
                "&expiresIn=" + result.getExpiresIn() +
                "&refreshToken=" + URLEncoder.encode(result.getRefreshToken(), "UTF-8")
        );

    }

    /**
     * 亚马逊店铺授权重定向接口
     *
     * @param req
     * @param response
     * @throws Exception
     */
    @ApiOperation(value = "亚马逊店铺授权重定向接口")
    @GetMapping("generate")
    public void authSellerGenerate(AuthSellerGenerateReq req,
                                   HttpServletResponse response) throws Exception {
        log.info("亚马逊店铺授权重定向接口，req:{}", req);
        response.sendRedirect(req.getAmazon_callback_uri() + "?redirect_uri=" + authFinishUrl + "&amazon_state=" + req.getAmazon_state());
    }

    @ApiOperation(value = "重定向测试")
    @GetMapping("redirect")
    public void redirect(HttpServletResponse response) throws Exception {
        //response.sendRedirect(authIndex + "/spapi/callback/index.html?accessToken=" + "xxx" + "&tokenType=" + "bereave" + "&expiresIn=" + "666" + "&refreshToken=" + "rttt");
        String at = "Atza|IwEBIF1dVKO4Z_-xsKWdYlA7UR-crmEaVtNcRc5vBGUjVczFK9Myws2VRM1CkuomZsjsgi6FHXLReSwJ_eTKUiD8V3CNwOptcdtswxiD4nfI0aiW7pXfP81FcCxztE59668ZAiyF2VPLij39AyGqRI_zo50MlW0RejUtgDrmrVKQ-43OKC45gECxLYW6fcBa4jbVuzQIlkmu3tD1LWBW1i7_db1PKuSgsGj0yns4ZpouVfqXiDmZFK-1jwunW3lvay6f90EreVMCBR_H9kHpUeIZI5NlfpOcHwbfYJgEPEQpSpgwzrFvEUWIL8KD9i_rBvBpqks";
        String rt = "Atzr|IwEBINpAXSYvhPZxnRQC4JxYFbIbBcnGMwkCn2NhOhyrsD4CvkiPOTwLcpD2iD7LSVRozEADgWJRyjOdCaGOcZsh_lkGTzMRQ7Io3-AG6vWFNhy3HCwFeKYez3V8C1Ro-OzgSRFuxrVVIT-X8SjK_xO5ldp7x83yqCkHOGDr40eUdHwFXSnsPGI9s0tYOclepUI3HMfftBzZIkr4me4Vx4B3kdREPJS46dDhhqB7fZ-_gHocfSDMcZB6EI-SkuSVLnApS88uAHE0FLUCDt5OvqCea7Bdn6D8lT4jO9CmQY4nRWen6_pfL6570HevSTjtqVG8Udw";
        response.sendRedirect(authIndex + "/spapi/callback/index.html?accessToken=" + URLEncoder.encode(at, "UTF-8") + "&tokenType=bearer&expiresIn=3600&refreshToken=" + URLEncoder.encode(rt, "UTF-8"));

    }

    /**
     * 亚马逊店铺授权回调接口
     *
     * @param req
     * @param response
     * @throws Exception
     */
    @ApiOperation(value = "亚马逊物流授权回调接口")
    @GetMapping("ship/finish")
    public void shipAuthSellerCallback(AuthSellerCallbackReq req,
                                       HttpServletResponse response) throws Exception {
        AuthSellerCallbackRes result = authorizeV1Service.shopAuthSellerCallback(req, response);
        if (result.getIsRedirect() == 1) {
            return;
        }
        response.sendRedirect(authIndex + "/spapi/callback/index.html?accessToken=" + URLEncoder.encode(result.getAccessToken(), "UTF-8") + "&tokenType=" + result.getTokenType() + "&expiresIn=" + result.getExpiresIn() + "&refreshToken=" + URLEncoder.encode(result.getRefreshToken(), "UTF-8"));

    }


    /**
     * 亚马逊店铺授权回调接口（暂时没用）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "授权重定向接口", notes = "授权重定向接口")
    @GetMapping("callback")
    public ResponsePoJo<BaseRes> callback(HttpServletRequest req) throws Exception {
        Enumeration<String> a = req.getParameterNames();
        Enumeration<String> b = req.getAttributeNames();
        while (a.hasMoreElements()) {
            log.info(String.format("a参数======》》》%s", a.nextElement()));
        }
        while (b.hasMoreElements()) {
            log.info(String.format("b参数======》》》%s", b.nextElement()));
        }
        return new ResponsePoJo<>("success", null);
    }

    /**
     * 参数接收
     *
     * @param req
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "参数接收")
    @PostMapping("parameter-receiving")
    public ResponsePoJo<BaseRes> parameterReceiving(@RequestBody ParameterReceivingReq req) throws Exception {
        ResponsePoJo<BaseRes> res = new ResponsePoJo<>();
        log.info(String.format("======接收到参数：%s", JsonUtil.objectToJson(req)));
        res.setRet("0");
        res.setMsg("success");
        return res;
    }

}
