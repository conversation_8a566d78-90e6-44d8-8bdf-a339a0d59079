package com.zixun.kuniao.notcheck.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.zixun.kuniao.comm.dto.SpApiIpDTO;
import com.zixun.kuniao.manager.SpApiWhiteIpManager;
import com.zixun.kuniao.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.zixun.kuniao.utils.Contants.CLIENT_IP;

/**
 * <AUTHOR>
 * @create 2022/5/9
 */
@Component
@Slf4j
public class RequestIpInterceptor implements HandlerInterceptor {

    @Value("${ipWhiteList}")
    private String ipWhiteList;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String ipAddress = IpUtil.getIP(request);
        log.info("获取到的客户端IP：{}", ipAddress);
        SpApiIpDTO spApiIpDTO = SpApiWhiteIpManager.getWhiteIpMap().get(ipAddress);
        if (StringUtils.isNotEmpty(ipAddress) && spApiIpDTO == null) {
            log.warn("该IP禁止访问: {}", ipAddress);
            forbidden(response, "该IP禁止访问：" + ipAddress);
            return false;
        }
        request.setAttribute(CLIENT_IP, ipAddress);
        return true;
    }

    /**
     * 拒绝请求
     *
     * @param res 响应
     */
    private void forbidden(HttpServletResponse res, String message) {
        try {
            res.setStatus(200);
            res.setHeader(
                    "Content-Type",
                    "application/json;charset=utf-8"
            );
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ret", "E000000");
            jsonObject.put("msg", message);
            jsonObject.put("restime", System.currentTimeMillis());
            jsonObject.put("data", null);
            log.info("forbidden restime:{}", jsonObject.get("restime"));
            res.getWriter().print(jsonObject.toJSONString());
        } catch (IOException err) {
            log.error(err.getMessage());
        }
    }
}
