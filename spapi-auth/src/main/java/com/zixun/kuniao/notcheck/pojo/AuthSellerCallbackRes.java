package com.zixun.kuniao.notcheck.pojo;

public class AuthSellerCallbackRes {

    private String accessToken;

    private String tokenType;

    private String expiresIn;

    private String refreshToken;

    private String sellingPartnerId;

    private String mwsAuthToken;

    private String errCode;

    private int isRedirect;

    private String state;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getIsRedirect() {
        return isRedirect;
    }

    public void setIsRedirect(int isRedirect) {
        this.isRedirect = isRedirect;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getSellingPartnerId() {
        return sellingPartnerId;
    }

    public void setSellingPartnerId(String sellingPartnerId) {
        this.sellingPartnerId = sellingPartnerId;
    }

    public String getMwsAuthToken() {
        return mwsAuthToken;
    }

    public void setMwsAuthToken(String mwsAuthToken) {
        this.mwsAuthToken = mwsAuthToken;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(String expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
}
