package com.zixun.kuniao.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description DingDingNotifyUtil
 * @date 2021/5/13 20:18
 */
@Slf4j
public class FeishuNotifyUtil {
    public static String SIGN_WEBHOOK = "https://open.feishu.cn/open-apis/bot/v2/hook/%s";


    /**
     * 创建签名
     *
     * @param timestamp 时间戳/秒
     * @param secret
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    private static String createSign(long timestamp, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64Utils.encode(signData));
    }

//
//    public static void sendMsg(String msg, String token) {
//        sendMsgToGroup(msg, false, token);
//    }

    public static void sendMsgToGroup(String token, String secret, String msg, boolean isAtAll) {
        Map<String, String> textMap = new HashMap<>();
        if (isAtAll) {
            msg = "<at user_id=\"all\">所有人</at> " + msg;
        }
        textMap.put("text", msg);

        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("msg_type", "text");
        bodyMap.put("content", JSON.toJSONString(textMap));
        String ret;
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String sign = createSign(timestamp, secret);
            String url = String.format(SIGN_WEBHOOK, token);
            bodyMap.put("timestamp", String.valueOf(timestamp));
            bodyMap.put("sign", sign);
            ret = HttpUtil.post(url, JSON.toJSONString(bodyMap));
            JSONObject jsonObject = JSON.parseObject(ret);
            System.out.println(jsonObject.toJSONString());

            Long code = jsonObject.getLong("code");
            if (code != 0) {
                String bodyStr = JSONObject.toJSONString(bodyMap);
                log.error("send feishu msg error:{}, message:{}, body:{}", ret, jsonObject.getString("msg"), bodyStr);
            } else {
                log.info("send feishu msg success:{}", ret);
            }
        } catch (Exception e) {
            log.error("send feishu msg error:{}", e.getMessage());
        }
    }

    public static void sendRichTextToGroup(String token, String secret, String title, List<List<Map<String, String>>> contentList) {
        Map<String, Object> postContentMap = new HashMap<>();
        postContentMap.put("zh_cn", new HashMap<String, Object>() {{
            put("title", title);
            put("content", contentList);
        }}
        );
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("post", postContentMap);

        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("msg_type", "post");
        bodyMap.put("content", contentMap);
        String ret;
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String sign = createSign(timestamp, secret);
            String url = String.format(SIGN_WEBHOOK, token);
            bodyMap.put("timestamp", String.valueOf(timestamp));
            bodyMap.put("sign", sign);
            ret = HttpUtil.post(url, JSON.toJSONString(bodyMap));
            JSONObject jsonObject = JSON.parseObject(ret);
            System.out.println(jsonObject.toJSONString());

            Long code = jsonObject.getLong("code");
            if (code != 0) {
                String bodyStr = JSONObject.toJSONString(bodyMap);
                log.error("send feishu post msg error:{}, message:{}, body:{}", ret, jsonObject.getString("msg"), bodyStr);
            } else {
                log.info("send feishu post msg success:{}", ret);
            }
        } catch (Exception e) {
            log.error("send feishu post msg error:{}", e.getMessage());
        }
    }
    private static String getIsvAuditFeishuPostNotify(String appName, String developName, String updateRemark) {
        return  "[" +
                "  [{" +
                "    \"tag\": \"at\"," +
                "    \"user_id\": \"all\"," +
                "    \"user_name\": \"所有人\"" +
                "  }, {" +
                "    \"tag\": \"text\"," +
                "    \"text\": \"你有一个新的应用待审核，请进入BSS后台查看: \"" +
                "  }, {" +
                "    \"tag\": \"a\"," +
                "    \"text\": \"点击查看\"," +
                "    \"href\": \"" + "https://bss.ziniao.com/pro/marketAppAudit" + "\"" +
                "  }]," +
                "  [{" +
                "    \"tag\": \"text\"," +
                "    \"text\": \"应用名: " + appName + "\"" +
                "  }]," +
                "  [{" +
                "    \"tag\": \"text\"," +
                "    \"text\": \"开发商: " + developName + "\"" +
                "  }]," +
                "  [{" +
                "    \"tag\": \"text\"," +
                "    \"text\": \"提交时间: " + LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\"" +
                "  }]," +
                "  [{" +
                "    \"tag\": \"text\"," +
                "    \"text\": \"更新说明: " + updateRemark + "\"" +
                "  }]" +
                "]";
    }
    public static void main(String[] args) {
        List<List<Map<String, String>>> contentList = new ArrayList<>();
        // 段落1
        List<Map<String, String>> content1 = new ArrayList<>();
        content1.add(new HashMap<String, String>() {{put("tag", "text");put("text", "测试");}});
        content1.add(new HashMap<String, String>() {{put("tag", "a");put("text", "请查看");put("href", "https://www.baidu.com");}});
        contentList.add(content1);

        String isvAuditFeishuPostNotify = getIsvAuditFeishuPostNotify("测试应用", "测试开发商", "测试更新说明");
        // 转成List<List<Map<String, String>>>
        List<List<Map<String, String>>> contentList1 = JSONObject.parseObject(isvAuditFeishuPostNotify, new TypeReference<List<List<Map<String, String>>>>() {});
        FeishuNotifyUtil.sendRichTextToGroup("11ae1d4d-7574-42ae-90a2-7b412adb14bb", "KB5VMpjdPXZhDfjsCMoBnd", "ISV应用审核提醒", contentList1);
    }
}
