/**  
 * Copyright © 2016zg. All rights reserved.
 */
package com.zixun.kuniao.utils;

import java.text.DateFormat;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zixun.kuniao.comm.exception.ZxException;

/**
 * 
 * JSON数据解析类
 * <AUTHOR>
 * @date 2016年11月17日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2016年11月17日      ZX                新建
 *
 */
public class JsonUtil {
    
    /**
     * 禁止实例化
     */
    private JsonUtil() {
        
    }
  
    /**
     * 
     * @Description: 将对象转换成json格式 
     * <AUTHOR>
     * @param ts 需要转换的对象
     * @return 转换好的JSON串
     * @throws Exception
     */
    public static String objectToJson(Object ts) throws Exception {
        
        return objectToJson(ts, DateUtil.FORMART_YYYYMMDD_HHMMSS_DEFAULT);
    }
    

  
    /**
     * 
     * @Description: 将对象转换成json格式(并自定义日期格式)
     * <AUTHOR>
     * @param ts 需要转换的对象
     * @param dateformat 日期格式
     * @return 转换好的JSON字符串
     * @throws Exception
     */
    public static String objectToJson(Object ts, final String dateformat) throws Exception {
        
        ObjectMapper mapper = new ObjectMapper();
        final DateFormat df = new SimpleDateFormat(dateformat);
        mapper.setDateFormat(df);
        String jsonStr = mapper.writeValueAsString(ts);
        return jsonStr;
    }
    
    /**
     * 
     * @Description: Map转JSON字符串
     * @date:  2015年9月17日 下午4:07:25 
     * <AUTHOR>
     * @param map Map对象
     * @return JSON字符串
     * @throws Exception
     */
    public static String mapToJson(Map<String, Object> map) throws Exception {
        
        return mapToJson(map, DateUtil.FORMART_YYYYMMDD_HHMMSS_DEFAULT);
    }
    
    /**
     * 
     * @Description: Map转JSON字符串
     * <AUTHOR>
     * @param map Map对象
     * @param dateFormat 日期格式
     * @return JSON字符串
     * @throws Exception 
     */
    public static String mapToJson(Map<String, Object> map, final String dateFormat) throws Exception {
        
        ObjectMapper mapper = new ObjectMapper();
        final DateFormat df = new SimpleDateFormat(dateFormat);
        mapper.setDateFormat(df);
        String jsonStr = mapper.writeValueAsString(map);
        return jsonStr;
    }
  
    
    /**
     * 
     * @Description: 将json字符串转换成list对象
     * <AUTHOR>
     * @param jsonStr JSON字符串
     * @param t 列表对象
     * @param dateformat 日期格式化字符串
     * @return 转换后的列表
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> jsonToList(String jsonStr, Class<T> t, final String dateformat) throws Exception {
        
        ObjectMapper mapper = new ObjectMapper();
        final DateFormat df = new SimpleDateFormat(dateformat);
        mapper.setDateFormat(df);
        
        JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class, t);
        
        List<T> resultList = (List<T>) mapper.readValue(jsonStr, javaType);
        return resultList;
    }
    
    /**
     * 
     * @Description: 将json字符串转换成list对象
     * <AUTHOR>
     * @param jsonStr JSON字符串
     * @param t 列表对象
     * @return 转换后的列表
     * @throws Exception
     */
    public static <T> List<T> jsonToList(String jsonStr, Class<T> t) throws Exception {
        return jsonToList(jsonStr, t, DateUtil.FORMART_YYYYMMDD_HHMMSS_DEFAULT);
    }
    

    /**
     * 
     * @Description: 将JSON字符串转换成MAP
     * <AUTHOR>
     * @param jsonStr JSON字符串
     * @return 转换后的MAP
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> jsonToMap(String jsonStr) throws Exception {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> resultMap = (Map<String, Object>) mapper.readValue(jsonStr, Map.class);
            return resultMap;
        } catch (Exception e) {
            throw new ZxException("change Json To Map Error", e);
        }
    }
  
    /**
     * 
     * @Description: 将json转换成bean对象 
     * @date:  2015年9月17日 下午2:51:48 
     * <AUTHOR>
     * @param jsonStr JSON字符串
     * @param t 要转换成的对象
     * @return 转换后的对象
     * @throws Exception
     */
    public static <T> T jsonToBean(String jsonStr, Class<T> t) throws Exception {
        
        return jsonToBean(jsonStr, t, DateUtil.FORMART_YYYYMMDD_HHMMSS_DEFAULT);
    }  

    /**
     * 
     * @Description: 将json转换成bean对象（指定日期格式）
     * @date:  2015年9月17日 下午2:56:01 
     * <AUTHOR>
     * @param jsonStr JSON字符串
     * @param t 要转换成的对象
     * @param dateFormat 日期格式
     * @return 转换后的对象
     */
    public static <T> T jsonToBean(String jsonStr, Class<T> t, final String dateFormat) throws Exception {
        
        ObjectMapper mapper = new ObjectMapper();
        final DateFormat df = new SimpleDateFormat(dateFormat);
        mapper.setDateFormat(df);
        
        T result = (T) mapper.readValue(jsonStr, t);
        return result;
    }

}
