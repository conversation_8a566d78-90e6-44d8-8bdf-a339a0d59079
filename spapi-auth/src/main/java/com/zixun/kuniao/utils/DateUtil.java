/**  
 * Copyright © 2016zg. All rights reserved.
 */
package com.zixun.kuniao.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 
 * @Description: 日期操作工具类
 * <AUTHOR>
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2016年11月17日      ZX                新建
 *
 */
public class DateUtil {

    /**
     * 日期时间格式yyyy-MM-dd HH:mm:ss
     */
    public static final String FORMART_YYYYMMDD_HHMMSS_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式yyyy-MM-dd
     */
    public static final String FORMART_YYYYMMDD_DEFAULT = "yyyy-MM-dd";

    /**
     * 时间格式HH:mm
     */
    public static final String FORMART_HHMM_DEFAULT = "HH:mm";

    /**
     * 
     * @Description: 将日期字符串转换成指定日期型
     * @date:  2016年1月13日 下午4:48:03 
     * <AUTHOR>
     * @param value 日期字符串
     * @param targetType 指定日期类型
     * @param formats 格式
     * @return 日期
     */
    public static Date parseDate(String value, Class<?> targetType, String... formats) {
        for (String format : formats) {
            try {
                long v = new SimpleDateFormat(format).parse(value).getTime();
                return (Date) targetType.getConstructor(long.class).newInstance(v);
            } catch (ParseException e) {
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            try {
                return (Date) targetType.getConstructor(String.class).newInstance(value);
            } catch (Exception e) {
            }
        }
        throw new IllegalArgumentException("cannot parse:" + value + " for date by formats:" + Arrays.asList(formats));
    }

    /**
     * 
     * @Description: 判断是否是日期类型
     * @date:  2016年1月13日 下午4:48:06 
     * <AUTHOR>
     * @param targetType 判断类
     * @return 判断结果
     */
    public static boolean isDateType(Class<?> targetType) {
        if (targetType == null) {
            return false;
        } else {
            return targetType == Date.class || targetType == java.sql.Timestamp.class
                    || targetType == java.sql.Date.class || targetType == java.sql.Time.class;
        }
    }

    /**
     * @Description: 转换日期到字符串
     * @date:  2015年8月27日 上午11:22:54 
     * @param date 日期
     * @param dateFormat 格式
     * @return 格式化后的字符串
     */
    public static String parseString(Date date, String dateFormat) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        String returnDateStr = "";
        try {
            returnDateStr = simpleDateFormat.format(date);
        } catch (Exception e) {
            // nothing
        }
        return returnDateStr;
    }

    /**
     * @Description: 判断是否是日期型
     * @date:  2015年8月27日 上午11:22:59 
     * @param date 日期
     * @param dateFormat 格式
     * @return 判断结果
     */
    public static boolean isDate(String date, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        sdf.setLenient(false);
        try {
            sdf.parse(date);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 
     * @Description: 日期加减，单位分钏
     * @date:  2015年9月28日 下午8:46:29 
     * <AUTHOR>
     * @param date 基础日期
     * @param increment 加减值（单位分钟）
     * @return 计算后的日期
     */
    public static Date addDate(Date date, Integer increment) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, increment);
        return calendar.getTime();
    }
    
    /**
     * 
     * 
     * @date:  2017年8月31日
     * @author: zx
     * @param date
     * @param increment
     * @return
     */
    public static Date addHour(Date date, Integer increment) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, increment);
        return calendar.getTime();
    }

    /**
     * 
     * @Description: 日期加减，单位天
     * @date:  2015年9月28日 下午8:46:29 
     * <AUTHOR>
     * @param date 基础日期
     * @param increment 加减值（天）
     * @return 计算后的日期
     */
    public static Date addDates(Date date, Integer increment) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, increment);
        return calendar.getTime();
    }

    /**
     * 
     * @Description: 返回系统时间
     * @date:  2015年9月8日 上午11:54:53 
     * @param dateFormat 格式
     * @return 指定格式系统日期字符串
     */
    public static String getSystemDateTime(String dateFormat) {
        return parseString(new Date(), dateFormat);
    }

    /**
     * 
     * @Description: 判断是否是同一天
     * @date:  2015年11月6日 上午9:47:07 
     * <AUTHOR>
     * @param oneDate 判断对象的一个日期
     * @param otherDate 判断对象的另一个日期
     * @return 判断结果
     */
    public static boolean isSameDay(Calendar oneDate, Calendar otherDate) {

        boolean result = true;

        if (oneDate.get(Calendar.YEAR) != otherDate.get(Calendar.YEAR)) {
            result = false;
        } else if (oneDate.get(Calendar.MONTH) != otherDate.get(Calendar.MONTH)) {
            result = false;
        } else if (oneDate.get(Calendar.DATE) != otherDate.get(Calendar.DATE)) {
            result = false;
        }
        return result;
    }

    /**
     * 
     * @Description: 取得当前日期是多少周 
     * @date:  2015年11月5日 上午10:22:36 
     * <AUTHOR>
     * @param Date date
     * @return int
     * @throws
     */
    public static int getWeekOfYear(Date date) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        /**设置一年中第一个星期所需的最少天数，例如，如果定义第一个星期包含一年第一个月的第一天，则使用值 1 调用此方法。 
         * 如果最少天数必须是一整个星期，则使用值 7 调用此方法。 **/
        c.setMinimalDaysInFirstWeek(1);
        c.setTime(date);
        return c.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     *  
     * @Description: 得到某一年周的总数 
     * @date:  2015年11月5日 上午10:23:24 
     * <AUTHOR>
     * @param int year
     * @return int
     * @throws
     */
    public static int getMaxWeekNumOfYear(int year) {
        Calendar c = Calendar.getInstance();
        c.set(year, Calendar.DECEMBER, 31, 23, 59, 59);
        return getWeekOfYear(c.getTime());
    }

    /**
     *   
     * @Description: 得到某年某周的第一天  
     * @date:  2015年11月5日 上午10:23:46 
     * <AUTHOR>
     * @param int year, int week
     * @return Date
     * @throws
     */
    public static Date getFirstDayOfWeek(int year, int week) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.WEEK_OF_YEAR, week);
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);// 设置周一
        c.setFirstDayOfWeek(Calendar.MONDAY);
        return c.getTime();
    }

    /**
     *   
     * @Description: 得到某年某周的最后一天  
     * @date:  2015年11月5日 上午10:24:02 
     * <AUTHOR>
     * @param int year, int week
     * @return Date
     * @throws 
     */
    public static Date getLastDayOfWeek(int year, int week) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.WEEK_OF_YEAR, week);
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6); // Sunday
        return c.getTime();
    }

    /**
     * 
     * @Description: 得到某年某月的第一天
     * @date:  2015年11月5日 下午5:28:20 
     * <AUTHOR>
     * @param int year, int month
     * @return Date
     * @throws 
     */
    public static Date getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getMinimum(Calendar.DATE));
        return cal.getTime();
    }

    /**
     * 
     * @Description: 得到某年某月的最后一天
     * @date:  2015年11月5日 下午5:27:58 
     * <AUTHOR>
     * @param int year, int month
     * @return Date
     * @throws
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        int value = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, value);
        return cal.getTime();
    }

    /**
     * 
     * @Description: 取得当前日期所在周的第一天
     * @date:  2015年11月5日 上午10:30:42 
     * <AUTHOR>
     * @param Date date
     * @return Date
     * @throws
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek()); // Sunday
        return calendar.getTime();
    }

    /**
     * 
     * @Description: 取得当前日期所在周的最后一天
     * @date:  2015年11月5日 上午10:30:59 
     * <AUTHOR>
     * @param Date date
     * @return Date
     * @throws
     */
    public static Date getLastDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek() + 6); // Saturday
        return calendar.getTime();
    }

    /**
     *   
     * @Description: 在日期上增加数个整月
     * @date:  2015年11月5日 上午11:48:50 
     * <AUTHOR>
     * @param Date date, int n
     * @return Date
     * @throws
     */
    public static Date addMonth(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, n);
        return cal.getTime();
    }

    /**
     *  
     * @Description: 获取当年的第一天 
     * @date:  2015年11月5日 上午11:50:53 
     * <AUTHOR>
     * @param Date
     * @return 
     * @throws
     */
    public static Date getCurrYearFirst() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     *   
     * @Description: 获取当年的最后一天 
     * @date:  2015年11月5日 上午11:51:10 
     * <AUTHOR>
     * @param Date
     * @return 
     * @throws
     */
    public static Date getCurrYearLast() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearLast(currentYear);
    }

    /**
     *  
     * @Description: 获取某年第一天日期 
     * @date:  2015年11月5日 上午11:51:19 
     * <AUTHOR>
     * @param int year
     * @return Date
     * @throws
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     *  
     * @Description: 获取某年最后一天日期 
     * @date:  2015年11月5日 上午11:51:32 
     * <AUTHOR>
     * @param int year
     * @return Date
     * @throws
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();
        return currYearLast;
    }
    
    /**
     * 
     * @Description: 格式化时间段
     * @date:  2016年3月21日 下午7:55:14 
     * <AUTHOR>
     * @param times 时间长度(秒)
     * @return 格式化后时间段
     */
    public static String formatTime(Long times) {
        
        Integer secPerDay = 86400;
        Integer secPerHour = 3600;
        Integer secPerMin = 60;
        
        StringBuilder formatStr = new StringBuilder();
        if (times < 0) {
            formatStr.append("-");
        }
        
        Integer days = (int) (times / secPerDay);
        times -= days * secPerDay;

        Integer hours = (int) (times / secPerHour);
        times -= hours * secPerHour;

        Integer mins = (int) (times / secPerMin);
        times -= mins * secPerMin;

        formatStr.append(days);
        formatStr.append("天");
        formatStr.append(hours);
        formatStr.append("时");
        formatStr.append(mins);
        formatStr.append("分");
        formatStr.append(times);
        formatStr.append("秒");

        return formatStr.toString();
    }
    
    
    /**
     * 
     * 转换当前时区时间
     * @date:  2017年2月20日
     * @author: ZX
     * @param date
     * @return
     */
    public static String getGMT8String(Long date){
        Date dateTime = new Date(date);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_HHMMSS_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(dateTime);
    }
    
    /**
     * 
     * 转换当前时区时间
     * @date:  2017年2月20日
     * @author: ZX
     * @param date
     * @return
     */
    public static String getGMT8String(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_HHMMSS_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(date);
    }
    
    /**
     * 
     * 转换当前时区时间
     * @date:  2017年2月20日
     * @author: ZX
     * @param date
     * @return
     */
    public static String getGMT8StringForDate(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(date);
    }
    
    
    /**
     * 
     * 转换成北京时区时间
     * @date:  2017年2月20日
     * @author: ZX
     * @param date
     * @return
     */
    public static String getGMT8StringForDate(Long date){
        Date dateObject = new Date(date);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(dateObject);
    }
    
    /**
     * 
     * 转换成北京时区时间
     * @date:  2017年2月20日
     * @author: ZX
     * @param date
     * @param format 日期格式
     * @return
     */
    public static String getGMT8StringForDate(Long date, String format){
        Date dateObject = new Date(date);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(dateObject);
    }
    
    /**
     * 返回东八区格式时间串
     * @date:  2017年8月3日
     * @author: yesb
     * @param date
     * @param format
     * @return
     */
    public static String getGMT8StringForDate(Date date, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(date);
    }
    
    /**
     * 返回东八区时间
     * @date:  2017年8月6日
     * @author: yesb
     * @param dateStr
     * @param format
     * @return
     * @throws Exception
     */
    public static Date parseGMT8Date(String dateStr, String format) throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.parse(dateStr);
    }

    
    /**
     * 
     * 取当日北京时间0点
     * @date:  2017年7月4日
     * @author: zx
     * @param value
     * @param format
     * @return
     * @throws Excetpion
     */
    public static Date getGMT8With0clock() throws Exception {
        String strDate = getGMT8StringForDate(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.parse(strDate);
    }
    
    /**
     * 
     * 取指定时间戳北京时间0点
     * @date:  2017年12月1日
     * @author: zx
     * @param date
     * @return
     * @throws Exception
     */
    public static Date getGMT8With0clock(Long date) throws Exception {
        String strDate = getGMT8StringForDate(new Date(date));
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART_YYYYMMDD_DEFAULT);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.parse(strDate);
    }
    
    /**
     * 
     * 取北京时间当前月的第一天0时日期
     * @date:  2017年8月1日
     * @author: zx
     * @return
     * @throws Exception
     */
    public static Date getGMT8FirstDayOfMonthWith0clock() throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    

    public static void main(String[] args) throws Exception {
//        System.out.println(DateUtil.getGMT8FirstDayOfMonthWith0clock().getTime());
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
//        return cal.getTime();
        System.out.println(cal.get(Calendar.YEAR));
        System.out.println(cal.get(Calendar.MONTH) + 1);
        System.out.println(cal.get(Calendar.DATE));
        System.out.println(cal.get(Calendar.HOUR_OF_DAY));
        
        System.out.println(cal.getTimeInMillis());
    }
    
    /**
     * 转换字符串时间为零时区
     * @date:  2017年8月14日
     * @author: zcy
     * @param date
     * @param dateFormat
     * @return
     * @throws Exception
     */
    public static Date getGMT8Wx0clock(String date,String dateFormat) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.parse(date);
    }
    
    /**
     * 格式化前端页面时间查询条件
     * @date:  2017年12月13日
     * @author: yesb
     * @param date
     * @return
     * @throws Exception 
     */
    public static Date parseWebSearchDate(String date) throws Exception{
        SimpleDateFormat sf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        return sf.parse(date);
    }
    
    /**
     * 格式化为零时区时间
     * @date:  2018年1月8日
     * @author: yesb
     * @param date
     * @param dateFormat
     * @return
     * @throws Exception
     */
    public static Date parseGMTDate(String date, String dateFormat) throws Exception{
        SimpleDateFormat gmtFormat = new SimpleDateFormat(dateFormat);
        gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        
        return gmtFormat.parse(date);
    }

    /**
     * @date:  2018年4月18日
     * @author: yesb
     * @param time
     * @param string
     * @return
     */
    public static String getGMTStringForDate(Long date,String dateFormat) throws Exception {
        Date dateTime = new Date(date);
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(dateTime);
    }
}
