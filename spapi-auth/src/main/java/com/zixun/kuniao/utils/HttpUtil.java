package com.zixun.kuniao.utils;

import com.zixun.kuniao.comm.exception.ZxException;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;


/**
 * Http工具类
 *
 * <AUTHOR>
 * @version 1.0
 * <p>
 * operation
 * date         operator            content
 * 2018年1月10日      zx             create
 * @date 2018年1月10日
 */
public class HttpUtil {

    /**
     * 返回请求本体
     *
     * @param in
     * @return
     * @date: 2017年7月24日
     * @author: zx
     */
    private static String inputReader(InputStream in) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(in));
        StringBuffer sb = new StringBuffer();
        String line = "";
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                throw new ZxException("关闭资源失败", e);
            }
        }
        String resultStr = sb.toString();
        return resultStr;
    }

    /**
     * POST请求
     *
     * @param headMap
     * @param url
     * @param formparams
     * @return
     * @throws Exception
     * @date: 2018年1月10日
     * @author: zx
     */
    public static String post(Map<String, String> headMap, String url, List<NameValuePair> formparams) throws Exception {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        UrlEncodedFormEntity uefEntity;
        CloseableHttpResponse response = null;
        try {
            if (headMap != null) {
                for (Entry<String, String> headEntry : headMap.entrySet()) {
                    httpPost.addHeader(headEntry.getKey(), headEntry.getValue());
                }
            }

            uefEntity = new UrlEncodedFormEntity(formparams, "UTF-8");
            httpPost.setEntity(uefEntity);
            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return inputReader(entity.getContent());
        } finally {
            try {
                httpclient.close();
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ZxException("关闭资源失败", e);
            }
        }
    }

    /**
     * PUT请求
     *
     * @param headMap
     * @param url
     * @param jsonStr
     * @return
     * @throws Exception
     * @date: 2018年1月10日
     * @author: zx
     */
    public static String put(Map<String, String> headMap, String url, String jsonStr) throws Exception {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        CloseableHttpResponse response = null;
        try {
            if (headMap != null) {
                for (Entry<String, String> headEntry : headMap.entrySet()) {
                    httpPut.addHeader(headEntry.getKey(), headEntry.getValue());
                }
            }
            StringEntity se = new StringEntity(jsonStr, "UTF-8");
            httpPut.setEntity(se);
            response = httpclient.execute(httpPut);
            HttpEntity entity = response.getEntity();
            return inputReader(entity.getContent());
        } finally {
            try {
                httpclient.close();
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ZxException("关闭资源失败", e);
            }
        }
    }

    /**
     * 不用认证信息POST请求
     *
     * @param headMap
     * @param url
     * @param jsonStr
     * @return
     * @throws Exception
     * @date: 2018年1月10日
     * @author: zx
     */
    public static String post(Map<String, String> headMap, String url, String jsonStr) throws Exception {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        try {
            if (headMap != null) {
                for (Entry<String, String> headEntry : headMap.entrySet()) {
                    httpPost.addHeader(headEntry.getKey(), headEntry.getValue());
                }
            }
            StringEntity se = new StringEntity(jsonStr, "UTF-8");
            httpPost.setEntity(se);
            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return inputReader(entity.getContent());
        } finally {
            try {
                httpclient.close();
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ZxException("关闭资源失败", e);
            }
        }
    }

    /**
     * GET请求
     *
     * @param headMap
     * @param url
     * @param valueMap
     * @param timeout
     * @return
     * @throws Exception
     * @date: 2018年4月25日
     * @author: zx
     */
    public static String get(Map<String, String> headMap, String url, Map<String, String> valueMap, int timeout)
            throws Exception {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        if (valueMap != null) {
            StringBuilder strUrl = new StringBuilder();
            strUrl.append("?");
            for (Entry<String, String> valueEntry : valueMap.entrySet()) {
                strUrl.append(valueEntry.getKey());
                strUrl.append("=");
                strUrl.append(valueEntry.getValue());
                strUrl.append("&");
            }
            strUrl.deleteCharAt(strUrl.length() - 1);
            url += strUrl.toString();
        }
        HttpGet httpGet = new HttpGet(url);
        //设置请求和传输超时时间
        RequestConfig requestConfig =
                RequestConfig.custom().setSocketTimeout(timeout).setConnectTimeout(1800000).build();
        httpGet.setConfig(requestConfig);

        CloseableHttpResponse response = null;
        try {
            if (headMap != null) {
                for (Entry<String, String> headEntry : headMap.entrySet()) {
                    httpGet.addHeader(headEntry.getKey(), headEntry.getValue());
                }
            }

            response = httpclient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity, "utf-8");
//            return inputReader(entity.getContent());
        } finally {
            try {
                httpclient.close();
                if (null != response) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ZxException("关闭资源失败", e);
            }
        }
    }

    /**
     * GET请求
     *
     * @param headMap
     * @param url
     * @param valueMap
     * @return
     * @throws Exception
     * @date: 2018年1月10日
     * @author: zx
     */
    public static String get(Map<String, String> headMap, String url, Map<String, String> valueMap)
            throws Exception {
        return get(headMap, url, valueMap, 20000);
    }

}