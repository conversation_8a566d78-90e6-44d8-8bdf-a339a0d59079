/**
 * 
 */
package com.zixun.kuniao.utils;

import java.io.IOException;
import java.util.Properties;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.support.PropertiesLoaderUtils;

/**
 * 
 * 系统属性工具
 * 
 * <AUTHOR>
 * @date 2017年1月6日
 * @version 1.0
 *
 *          operation date operator content 2017年1月6日 ZX create
 *
 */
@Slf4j
public class MessageUtil {


    /**
     * 系统配置信息
     */
    private static Properties messageConfig;
    
    /**
     * 项目私有消息
     */
    private static Properties projectMessageProperties;
    
    /**
     * 共通消息文件名
     */
    private static String MESSAGE_COMM_FILE = "message_comm.properties";
    
    /**
     * 项目私有消息文件名
     */
    private static String MESSAGE_FILE = "message.properties";


    /**
     * 禁止工具类实例化
     */
    private MessageUtil() {

    }

    // 初始化加载消息
    static {
        try {
            messageConfig = PropertiesLoaderUtils.loadAllProperties(MESSAGE_COMM_FILE);
            projectMessageProperties = PropertiesLoaderUtils.loadAllProperties(MESSAGE_FILE);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 
     * 获取指定共通系统消息
     * @date: 2015年8月26日 下午7:55:19
     * @param key
     *            键
     * @return 值
     */
    public static String getCommMessage(String key) {
        return messageConfig.getProperty(key);
    }
    
    /**
     * 
     * 获取指定项目系统消息
     * @date: 2015年8月26日 下午7:55:19
     * @param key
     *            键
     * @return 值
     */
    public static String getProjectMessage(String key) {
        return projectMessageProperties.getProperty(key);
    }
    
    /**
     * 
     * 获取指定消息
     * @date:  2018年4月21日
     * @author: zx
     * @param key
     * @return
     */
    public static String getMessage(String key) {
        String messageInfo = messageConfig.getProperty(key);
        if (StringUtils.isBlank(messageInfo)) {
            messageInfo = projectMessageProperties.getProperty(key);
        }
        return messageInfo;
    }
    
    /**
     * 放入参数
     * @date:  2020年2月10日
     * @author: zx
     * @param key
     * @param params
     * @return
     */
    public static String getMessage(String key, Object... params) {
        String messgeInfo = getMessage(key);
        if (StringUtils.isNotBlank(messgeInfo) && params != null) {
            messgeInfo = String.format(messgeInfo, params);
        }
        return messgeInfo;
    }
}
