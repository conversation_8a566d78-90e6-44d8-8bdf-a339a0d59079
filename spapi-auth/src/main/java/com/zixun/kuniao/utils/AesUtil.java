package com.zixun.kuniao.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AesUtil {
    //    长度只能是16位，24位，32位
    public static final String defaultKey = "i9sh0CyNwtYbgKjF";

    public static String encryptBase64(String content, String key) {
        try {
            byte[] byteKey = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue(),
                    key.getBytes()).getEncoded();
            SymmetricCrypto aes = SecureUtil.aes(byteKey);
            // 加密
            return aes.encryptBase64(content);
        } catch (Exception e) {
            log.error(" 加密异常:{}", e.getMessage());
        }
        return null;
    }

    public static String decryptStr(String encryptString, String key) {
        try {
            byte[] byteKey = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue(),
                    key.getBytes()).getEncoded();
            SymmetricCrypto aes = SecureUtil.aes(byteKey);
            //解密
            return aes.decryptStr(encryptString);
        } catch (Exception e) {
            log.error(" 解密异常:{}", e.getMessage());
        }
        return null;
    }


    public static void main(String[] args) {
        // Disable Bouncy Castle
        SecureUtil.disableBouncyCastle();
        String content = "http://www.baidu.com?a=x";
        String encryptStr = encryptBase64(content, defaultKey);
        System.out.println(encryptStr);

    }
}
