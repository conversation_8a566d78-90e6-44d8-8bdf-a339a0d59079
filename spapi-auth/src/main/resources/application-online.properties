# 调试配置
server.port=80
# 配置中心
apollo.bootstrap.enabled=true
logging.level.root=info
logging.level.com.zixun.kuniao=info

application_id=amzn1.sellerapps.app.da886ba0-0916-4f98-bc7c-29ec6c03f8d7

proxy.index=https://sp-test.koniao.com
auth.index=https://authorize.koniao.com
aws.authorize.url.na=https://sellercentral.amazon.com
aws.authorize.url.eu=https://sellercentral-europe.amazon.com
aws.authorize.url.fe=https://sellercentral-japan.amazon.com

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=${APPSTORE_REDIS_PROXY_URL}
# Redis服务器连接端口
spring.redis.port=${APPSTORE_REDIS_PROXY_PORT}
# Redis服务器连接密码（默认为空）
spring.redis.password=${APPSTORE_REDIS_PROXY_PASSWORD}
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000