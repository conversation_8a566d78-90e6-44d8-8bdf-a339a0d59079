<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>亚马逊授权</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
          integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
    <style>
        .container {
            margin-top: 50px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>亚马逊SPAPI授权</h1>
    <form id="authForm">
        <div class="form-group">
            <label for="siteSelect">授权站点</label>
            <select class="form-control" id="siteSelect">
                <option value="NA">北美</option>
                <option value="EU">欧洲</option>
                <option value="FE">远东</option>
            </select>
        </div>
        <div class="form-group">
            <label for="stateInput">拓展参数（授权成功后原样返回）</label>
            <input type="text" class="form-control" id="stateInput" name="state">
        </div>
        <button type="submit" class="btn btn-primary">确定</button>
    </form>
</div>
<!-- Add this HTML code within the <body> tag -->
<div class="modal" id="failureModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">授权结果</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>授权失败，请检查授权参数！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script>
    function getQueryVariable(variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] === variable) {
                return pair[1];
            }
        }
        return false;
    }

    $(document).ready(function () {
        $('#authForm').submit(function (e) {
            e.preventDefault(); // 阻止表单默认提交行为

            var appId = getQueryVariable('appId');
            if (!appId) {
                //授权失败
                var mymodal = $('#failureModal');
                mymodal.find('.modal-body').text('appId不能为空');
                mymodal.modal('show');
                return;
            }
            console.log(appId);

            var selectedSite = $('#siteSelect').val();
            var stateValue = $('#stateInput').val();

            var url = '/spapi-auth/authorize/v2/authorize-url';

            // 发送POST请求到Java服务端URL
            $.post(url, {region: selectedSite, state: stateValue, appId: appId}, function (response) {
                // 处理服务器响应
                console.log(response);
                if (response['data']) {
                    // alert(response['data'])
                    // 通过 window.location 实现重定向
                    window.location.href = response['data'];
                } else {
                    //授权失败
                    var mymodal = $('#failureModal');
                    mymodal.find('.modal-body').text(response['msg']);
                    mymodal.modal('show');
                    // $('#failureModal').modal('show');
                }
            });
        });
    });
</script>
</body>
</html>