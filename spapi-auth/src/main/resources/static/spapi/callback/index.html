<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>亚马逊授权发送</title>
  <style>
    html {
      background-color: #F7F8FA;
    }

    .content {
      padding: 100px 100px;
    }
    .row {
      display: flex;
      word-break: break-all;
      margin-bottom: 35px;
      color: #545557;
    }
    .lbl {
      width: 200px;
      text-align: right;
    }
    .val {
      flex: 1;
    }
    .opts {
      margin-top: 5px;
    }
    .copy-btn {
      padding: 8px 12px;
      text-align: center;
      color: #fff;
      border: none;
      outline-style: none;
      background-color: #0080FF;
      border-radius: 2px;
      cursor: pointer;
    }
    .copy-btn:active {
      opacity: 0.8;
    }
  </style>
</head>

<body>
  <div class="content">
    <div class="row">
      <div class="lbl">Access Token：</div>
      <div class="val">
        <span id="accessToken"></span>
        <div class="opts">
          <button class="copy-btn" id="copyAccessToken">复制</button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="lbl">Token Type：</div>
      <div class="val">
        <span id="tokenType"></span>
      </div>
    </div>
    <div class="row">
      <div class="lbl">Expires In：</div>
      <div class="val">
        <span id="expiresIn"></span>
      </div>
    </div>
    <div class="row">
      <div class="lbl">Refresh Token：</div>
      <div class="val">
        <span id="refreshToken"></span>
        <div class="opts">
          <button class="copy-btn" id="copyRefreshToken">复制</button>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.10/dist/clipboard.min.js"></script>
  <script>
    var querys = new URLSearchParams(window.location.search);
    document.getElementById('accessToken').innerText = querys.get('accessToken') || '';
    document.getElementById('copyAccessToken').setAttribute('data-clipboard-text', querys.get('accessToken') || '');
    document.getElementById('tokenType').innerText = querys.get('tokenType') || '';
    document.getElementById('expiresIn').innerText = querys.get('expiresIn') || '';
    document.getElementById('refreshToken').innerText = querys.get('refreshToken') || '';
    document.getElementById('copyRefreshToken').setAttribute('data-clipboard-text', querys.get('refreshToken') || '');

    new ClipboardJS('.copy-btn');
  </script>
</body>

</html>