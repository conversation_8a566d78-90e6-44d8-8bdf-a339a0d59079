server.port=10088
apollo.bootstrap.enabled=false
management.server.port=19006
logging.level.root=info
logging.level.com.zixun.kuniao=debug

application_id=amzn1.sellerapps.app.da886ba0-0916-4f98-bc7c-29ec6c03f8d7
application_id_test = amzn1.sp.solution.7c78a95d-30f2-4e3a-9b28-a67f19c16732

#proxy.index=https://sp-test.koniao.com
proxy.index=http://localhost:10086
auth.index=http://localhost:10088
aws.authorize.url.na=https://sellercentral.amazon.com
aws.authorize.url.eu=https://sellercentral-europe.amazon.com
aws.authorize.url.fe=https://sellercentral-japan.amazon.com
aws.authorize.vendor-url.canada=https://vendorcentral.amazon.ca
aws.authorize.vendor-url.us=https://vendorcentral.amazon.com
aws.authorize.vendor-url.mexico=https://vendorcentral.amazon.com.mx
aws.authorize.vendor-url.brazil=https://vendorcentral.amazon.com.br
aws.authorize.vendor-url.uk=https://vendorcentral.amazon.co.uk
aws.authorize.vendor-url.spain=https://vendorcentral.amazon.es
#France	https://vendorcentral.amazon.fr
aws.authorize.vendor-url.france=https://vendorcentral.amazon.fr
#Belgium	https://vendorcentral.amazon.com.be
aws.authorize.vendor-url.belgium=https://vendorcentral.amazon.com.be
#Netherlands	https://vendorcentral.amazon.nl
aws.authorize.vendor-url.netherlands=https://vendorcentral.amazon.nl
#Germany	https://vendorcentral.amazon.de
aws.authorize.vendor-url.germany=https://vendorcentral.amazon.de
#Italy	https://vendorcentral.amazon.it
aws.authorize.vendor-url.italy=https://vendorcentral.amazon.it
#Sweden	https://vendorcentral.amazon.se
aws.authorize.vendor-url.sweden=https://vendorcentral.amazon.se
#South Africa	https://vendorcentral.amazon.co.za
aws.authorize.vendor-url.south-africa=https://vendorcentral.amazon.co.za
#Poland	https://vendorcentral.amazon.pl
aws.authorize.vendor-url.poland=https://vendorcentral.amazon.pl
#Egypt	https://vendorcentral.amazon.me
aws.authorize.vendor-url.egypt=https://vendorcentral.amazon.me
#Saudi Arabia	https://vendorcentral.amazon.me
aws.authorize.vendor-url.saudi-arabia=https://vendorcentral.amazon.me
#Turkey	https://vendorcentral.amazon.com.tr
aws.authorize.vendor-url.turkey=https://vendorcentral.amazon.com.tr
#U.A.E.	https://vendorcentral.amazon.me
aws.authorize.vendor-url.uae=https://vendorcentral.amazon.me
#India	https://www.vendorcentral.in
aws.authorize.vendor-url.india=https://www.vendorcentral.in
#Singapore	https://vendorcentral.amazon.com.sg
aws.authorize.vendor-url.singapore=https://vendorcentral.amazon.com.sg
#Australia	https://vendorcentral.amazon.com.au
aws.authorize.vendor-url.australia=https://vendorcentral.amazon.com.au
#Japan	https://vendorcentral.amazon.co.jp
aws.authorize.vendor-url.japan=https://vendorcentral.amazon.co.jp

ipWhiteList=127.0.0.1,*************

testIpList=127.0.0.1,*************

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000