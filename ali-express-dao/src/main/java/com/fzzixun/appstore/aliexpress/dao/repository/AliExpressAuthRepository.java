package com.fzzixun.appstore.aliexpress.dao.repository;

import com.fzzixun.appstore.aliexpress.dao.entity.AliExpressAuth;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-06
 */
public interface AliExpressAuthRepository extends JpaRepository<AliExpressAuth, Long> {

    List<AliExpressAuth> findBySellerId(String sellerId);

    AliExpressAuth findBySellerIdAndChannel(String sellerId, String channel);
}
