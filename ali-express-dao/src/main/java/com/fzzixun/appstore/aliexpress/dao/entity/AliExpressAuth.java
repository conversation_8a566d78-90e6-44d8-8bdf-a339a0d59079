package com.fzzixun.appstore.aliexpress.dao.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-06-06
 */
@Data
@Entity
@Table(name = "ali_express_auth")
public class AliExpressAuth {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column
    private String channel;

    @Column(name = "seller_id")
    private String sellerId;

    @Column(name = "callback_url")
    private String callbackUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 授权次数
     */
    @Column(name = "auth_cnt")
    private Integer authCnt;

    @PrePersist
    protected void prePersist() {
        if (this.createTime == null) {
            createTime = new Date();
        }
        if (this.updateTime == null) {
            updateTime = new Date();
        }
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = new Date();
    }

    @PreRemove
    protected void preRemove() {
        this.updateTime = new Date();
    }
}
