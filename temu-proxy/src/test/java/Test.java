import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fzzixun.appstore.temu.utils.SignUtil;
import org.apache.commons.codec.digest.DigestUtils;

import java.time.Instant;

public class Test {
    public static JSONObject createRequestParams(String appKey, String appSecret, String apiType,
                                                 String accessToken, Integer timestamp, JSONObject params) {
        params.remove("sign");
        // 公共请求参数
        params.put("type", apiType);
        params.put("app_key", appKey);
        params.put("timestamp", timestamp);
        params.put("data_type", "JSON");
        params.put("access_token", accessToken);
        String signContent = SignUtil.getSignContent(params);
        String sign = DigestUtils.md5Hex(appSecret + signContent + appSecret).toUpperCase();
        params.put("sign", sign);
        return params;
    }
    public static void main(String[] args) {
        String appKey = "3a64c3d2cf7594dde99a498d9d6dc550";
        String appSecret = "ef6d1ed0c406678c6ff3960fb6a3ced5cda0e56d";
        String apiType = "bg.goods.image.upload.global";
        String accessToken = "z6fogdjdmqlft1lnbg1mwhywrdqqqlk8vzs2lomtcqnpx1l9fnftxxbd";
        String params = "{\"access_token\":\"z6fogdjdmqlft1lnbg1mwhywrdqqqlk8vzs2lomtcqnpx1l9fnftxxbd\",\"app_key\":\"3a64c3d2cf7594dde99a498d9d6dc550\",\"imageBizType\":0,\"options\":{\"cateId\":30469,\"doIntelligenceCrop\":false,\"boost\":false,\"sizeMode\":2},\"type\":\"bg.goods.image.upload.global\"}";
        Integer timestamp = 1756698723;
        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject resultObject = createRequestParams(appKey, appSecret, apiType, accessToken, timestamp, jsonObject);
        System.out.println(resultObject.toString(SerializerFeature.PrettyFormat));
    }
}




//        String fileUrl = "https://openapi-b-us.temu.com/pfse/pkg-label/235acd72/fbb278ae-6156-489c-8672-fd8f238b453f.pdf?signOM=q-sign-algorithm%3Dsha1%26q-ak%3D9dxqhKoxrDedVkQVjgtbe6TmRGr2UEwY%26q-sign-time%3D1741932170%3B1741932770%26q-key-time%3D1741932170%3B1741932770%26q-header-list%3D%26q-url-param-list%3D%26q-signature%3Dc03309371d12b07b2478d18d74099cf7ed131dcc";
//        String apiUrl = "https://znappstore-proxy.ziniao.com/cn/https/openapi.kuajingmaihuo.com/openapi/router";
//        String[] fileUrlSplit = fileUrl.split("com/");
//        String[] apiUrlSplit = apiUrl.split("/openapi/router");
//        String reqUrl = apiUrlSplit[0] + "/" + fileUrlSplit[1];
//        System.out.println(reqUrl);
