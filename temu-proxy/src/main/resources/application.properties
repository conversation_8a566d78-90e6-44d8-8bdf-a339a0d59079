spring.profiles.active=local
app.id=temu-proxy
server.port=10086
spring.application.name=temu-proxy
# Apollo
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=application
apollo.bootstrap.eagerLoad.enable=true
apollo.autoUpdateInjectedSpringProperties=true

management.endpoints.web.base-path=/status
management.endpoints.web.exposure.include=health
management.server.port=19002
# 数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# jpa
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

spring.task.execution.pool.coreSize=30