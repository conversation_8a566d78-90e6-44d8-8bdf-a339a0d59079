server.port=10086
apollo.bootstrap.enabled=true
apollo.meta=http://localhost:8080
management.server.port=19002
logging.level.root=info
logging.level.com.fzzixun.appstore.temu=debug

temu.setting.map.url_cn = https://openapi.kuajingmaihuo.com/openapi/router
temu.setting.map.url_cn_test = https://kj-openapi.temudemo.com/openapi/router
temu.setting.map.url_us = https://openapi-b-us.temu.com/openapi/router
temu.setting.map.url_us_test = http://openapi-b-us.temudemo.com/openapi/router
temu.setting.map.url_eu = https://openapi-b-eu.temu.com/openapi/router
temu.setting.map.url_eu_test = http://openapi-b-eu.temudemo.com/openapi/router
temu.setting.map.app_key_full_managed_cn = 72bc9e4143e960b2134e1cdf22fec651
temu.setting.map.app_secret_full_managed_cn = c54100b5b15d69d5cf0db9e8a653333a60f73c23
temu.setting.map.app_key_semi_managed_cn = 47bb4bb7769e12d9f7aa93cf029fe529
temu.setting.map.app_secret_semi_managed_cn = ac0a3e952eaaa5b19c0e615c2ef497f50afa6e49
temu.setting.map.app_key_semi_managed_us = 4ebbc9190ae410443d65b4c2faca981f
temu.setting.map.app_secret_semi_managed_us = 4782d2d827276688bf4758bed55dbdd4bbe79a79
temu.setting.map.app_key_semi_managed_eu = f860e759073f9d1e5c8bbeb7baac1dbf
temu.setting.map.app_secret_semi_managed_eu = 121eac72693c6e587f7e15ce4721b42da5df2def

ipWhiteList=127.0.0.1,*************,*************

testLimit=false


# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

limitStatusCode=429

#测试
spapi.setting.map.access_key_test = ********************
spapi.setting.map.secret_key_test = +3VaPZJC6LxqUovVSrwJOwEvR6bX3SZkV08BLh+k
spapi.setting.map.client_id_test = amzn1.application-oa2-client.48a90d245897402cac66bc1072d0f811
spapi.setting.map.client_secret_test = a59ec70693f3be110cb0de90abd95f8389359940937ed47b1fce40f23b922c8d

spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM