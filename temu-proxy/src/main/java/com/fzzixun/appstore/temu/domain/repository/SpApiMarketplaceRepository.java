package com.fzzixun.appstore.temu.domain.repository;

import com.fzzixun.appstore.temu.domain.entity.SpApiMarketplace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


public interface SpApiMarketplaceRepository extends JpaRepository<SpApiMarketplace, Long>,
        JpaSpecificationExecutor<SpApiMarketplace> {
    Optional<SpApiMarketplace> findFirstByCompanyIdAndMarketplaceId(Long companyId, String marketplaceId);

    boolean existsByCompanyIdAndMarketplaceId(Long companyId, String marketplaceId);

}