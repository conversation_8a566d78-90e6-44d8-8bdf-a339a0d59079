//package com.fzzixun.appstore.temu.component;
//
//import com.fzzixun.appstore.temu.dto.ProxyDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class StoreProxyComponent {
//
//    @Value("${temu.proxy.enable:false}")
//    private boolean proxyEnable;
//    @Value("${temu.proxy.username:}")
//    private String proxyUsername;
//    @Value("${temu.proxy.password:}")
//    private String proxyPassword;
//    @Value("${temu.proxy.ip:}")
//    private String proxyIp;
//    @Value("${temu.proxy.port:80}")
//    private int proxyPort;
//
//    public ProxyDTO getProxyInfo() {
//        if (!proxyEnable) {
//            return null;
//        }
//        return new ProxyDTO(proxyUsername, proxyPassword, proxyIp, proxyPort);
//    }
//}
