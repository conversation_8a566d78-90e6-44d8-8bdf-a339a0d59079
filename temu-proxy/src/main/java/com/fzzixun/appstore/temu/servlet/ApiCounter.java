package com.fzzixun.appstore.temu.servlet;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ApiCounter {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public void increaseCount(String companyId, String ip, String url) {
        String key = getRedisKey(companyId, ip);
        // 在增加计数之前，先检查key是否已经存在
        Boolean hasKey = redisTemplate.hasKey(key);
        redisTemplate.opsForZSet().incrementScore(key, url, 1);
        // 如果key之前不存在，那么设置过期时间
        if (hasKey == null || !hasKey) {
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        }
    }

    public Double getCount(String companyId, String ip, String url) {
        String key = getRedisKey(companyId, ip);
        Double score = redisTemplate.opsForZSet().score(key, url);
        return score == null ? 0 : score;
    }

    public String getRedisKey(String companyId, String ip) {
        String date = LocalDate.now().format(FORMATTER);
        return "api:counter:" + date + ":" + companyId + ":" + ip;
    }

    public void printSortedSetData(String key) {
        // 获取Sorted Set中的数据，按照分数降序排列
        Set<ZSetOperations.TypedTuple<Object>> resultSet = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, -1);
        if (CollectionUtil.isEmpty(resultSet)) {
            return;
        }
        for (ZSetOperations.TypedTuple<Object> tuple : resultSet) {
            log.info("Value: " + tuple.getValue() + ", Score: " + tuple.getScore());
        }
    }
}
