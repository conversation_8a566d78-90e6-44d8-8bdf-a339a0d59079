package com.fzzixun.appstore.temu.utils;

import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @create 2022/6/7
 */
public class UrlUtil {

    public static String getOneParameter(String url, String keyWord) {
        String retValue = null;
        try {
            final String charset = "utf-8";
            url = URLDecoder.decode(url, charset);
            if (url.indexOf('?') != -1) {
                final String contents = url.substring(url.indexOf('?') + 1);
                String[] keyValues = contents.split("&");
                for (String keyValue : keyValues) {
                    String key = keyValue.substring(0, keyValue.indexOf("="));
                    String value = keyValue.substring(keyValue.indexOf("=") + 1);
                    if (key.equalsIgnoreCase(keyWord)) {
                        retValue = value;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retValue;
    }

    public static void main(String[] args) {
        String url = "https://sellingpartnerapi-eu.amazon.com/reports/2021-06-30/reports?reportTypes=GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE&processingStatuses=DONE&marketplaceIds=A1F83G8C2ARO7P&pageSize=10&createdSince=2022-05-21T23:58:36.452Z&createdUntil=2022-05-26T23:58:36.452Z";
        url = "https://sellingpartnerapi-na.amazon.com/catalog/2022-04-01/items/B092MDSVTX?locale&marketplaceIds=ATVPDKIKX0DER&includedData=attributes,dimensions,identifiers,images,productTypes,relationships,salesRanks,summaries";
        System.out.println(UrlUtil.getOneParameter(url, "marketplaceIds"));
    }
}
