package com.fzzixun.appstore.temu.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/5/7
 */
@Configuration
@ConfigurationProperties(prefix = "temu.setting")
public class TemuConfig {
    private Map<String, String> map;

    public String getTemuSettingInfo(String keyStr) {
        return map.get(keyStr);
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }
}
