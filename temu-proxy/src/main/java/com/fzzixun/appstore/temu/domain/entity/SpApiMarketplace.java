package com.fzzixun.appstore.temu.domain.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "spapi_marketplace")
public class SpApiMarketplace {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "marketplace_id")
    private String marketplaceId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    @PrePersist
    protected void prePersist() {
        if (this.createTime == null) {
            createTime = new Date();
        }
        if (this.updateTime == null) {
            updateTime = new Date();
        }
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = new Date();
    }

    @PreRemove
    protected void preRemove() {
        this.updateTime = new Date();
    }
}
