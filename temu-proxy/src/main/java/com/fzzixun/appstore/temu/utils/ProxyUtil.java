package com.fzzixun.appstore.temu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;
import com.fzzixun.appstore.temu.config.TemuConfig;
import com.fzzixun.appstore.temu.servlet.RequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

@Slf4j
@Component
public class ProxyUtil {

    @Autowired
    private TemuConfig temuConfig;

    public String getTemuSettingInfo(String keyStr) {
        return temuConfig.getTemuSettingInfo(keyStr);
    }

    public JSONObject createRequestParams(String appKey, String appSecret, String apiType, String accessToken, JSONObject params) {
        params.remove("sign");
        // 公共请求参数
        params.put("type", apiType);
        params.put("app_key", appKey);
        params.put("timestamp", Instant.now().toEpochMilli() / 1000);
        params.put("data_type", "JSON");
        params.put("access_token", accessToken);
        String signContent = SignUtil.getSignContent(params);
        String sign = DigestUtils.md5Hex(appSecret + signContent + appSecret).toUpperCase();
        params.put("sign", sign);
        return params;
    }

    public RequestBuilder createOkHttpRequest(HttpServletRequest servletRequest, String region, String manageType) throws Exception {
        String regionKeySuffix;
        if ("cn".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn";
        } else if ("cn-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn_test";
        } else if ("us".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us";
        } else if ("us-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us_test";
        } else if ("eu".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu";
        } else if ("eu-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu_test";
        } else if ("global".equalsIgnoreCase(region)) {
            regionKeySuffix = "_global";
        }  else if ("partner".equalsIgnoreCase(region)) {
            regionKeySuffix = "_partner";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效API站点");
        }
        if (StringUtils.isEmpty(manageType)) {
            return new RequestBuilder(null, null, false, "请求地址错误，缺少店铺管理类型");
        }
        String manageTypeKeySuffix;
        if ("semi-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_semi_managed";
        } else if ("full-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_full_managed";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效店铺管理类型");
        }

        String apiUrl = getTemuSettingInfo("url" + regionKeySuffix);
        String appKey = getTemuSettingInfo("app_key" + manageTypeKeySuffix + regionKeySuffix);
        String appSecret = getTemuSettingInfo("app_secret" + manageTypeKeySuffix + regionKeySuffix);
        if (StringUtils.isEmpty(appKey)) {
            return new RequestBuilder(null, null, false, "暂不支持此站点该店铺管理类型");
        }

        String method = servletRequest.getMethod();
        if (StringUtils.isNotBlank(servletRequest.getQueryString())) {
            apiUrl = apiUrl + "?" + servletRequest.getQueryString();
        }

        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {
            if (servletRequest.getHeader("content-type") != null &&
                    !servletRequest.getHeader("content-type").contains("x-www-form-urlencoded")) {
                ServletInputStream servletInputStream = servletRequest.getInputStream();
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = servletInputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String body = result.toString(StandardCharsets.UTF_8.name());
                JSONObject params;
                try {
                    params = JSON.parseObject(body);
                } catch (Exception e) {
                    log.error("参数解析异常, body:{}", body, e);
                    return new RequestBuilder(null, null, false, "参数解析异常");
                }
                String apiType = params.getString("type");
                String accessToken = params.getString("access_token");
//                if (StringUtils.isEmpty(apiType)) {
//                    apiType = servletRequest.getHeader("x-temu-api");
//                }
//                if (StringUtils.isEmpty(accessToken)) {
//                    accessToken = servletRequest.getHeader("x-temu-access-token");
//                }
                if (StringUtils.isEmpty(apiType)) {
                    return new RequestBuilder(null, null, false, "API接口名不能为空");
                }
                if (StringUtils.isEmpty(accessToken)) {
                    return new RequestBuilder(null, null, false, "授权令牌不能为空");
                }
                String requestParamsStr = this.createRequestParams(appKey, appSecret, apiType, accessToken, params).toString();
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), requestParamsStr);
                RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, requestBody), requestParamsStr, true, null);
                requestBuilder.setApiType(apiType);
                return requestBuilder;
            }
            return null;
        }
        if (method.equalsIgnoreCase("POST") || method.equalsIgnoreCase("PUT")) {
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), new byte[0]);
            return new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, requestBody), null, true, null);
        }
        return new RequestBuilder((new Request.Builder()).url(apiUrl).method(method, null), null, true, null);
    }


    public Request createHeader(Request.Builder requestBuilder, HttpServletRequest httpRequest) {
        Enumeration<String> enumerationOfHeaderNames = httpRequest.getHeaderNames();
        while (enumerationOfHeaderNames.hasMoreElements()) {
            String headerName = (enumerationOfHeaderNames.nextElement()).toLowerCase();
            if ("content-type".equals(headerName)) {
                String[] values = httpRequest.getHeader(headerName).split(";");
                requestBuilder.addHeader("Content-Type", values[0]);
            }
        }
        return requestBuilder.build();
    }

    public RequestBuilder createDownloadOkHttpRequest(HttpServletRequest servletRequest, String region, String manageType) throws Exception {
        String regionKeySuffix;
        if ("cn".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn";
        } else if ("cn-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn_test";
        } else if ("us".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us";
        } else if ("us-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us_test";
        } else if ("eu".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu";
        } else if ("eu-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu_test";
        } else if ("global".equalsIgnoreCase(region)) {
            regionKeySuffix = "_global";
        }  else if ("partner".equalsIgnoreCase(region)) {
            regionKeySuffix = "_partner";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效API站点");
        }
        if (StringUtils.isEmpty(manageType)) {
            return new RequestBuilder(null, null, false, "请求地址错误，缺少店铺管理类型");
        }
        String manageTypeKeySuffix;
        if ("semi-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_semi_managed";
        } else if ("full-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_full_managed";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效店铺管理类型");
        }

        String apiUrl = getTemuSettingInfo("url" + regionKeySuffix);
        String appKey = getTemuSettingInfo("app_key" + manageTypeKeySuffix + regionKeySuffix);
        String appSecret = getTemuSettingInfo("app_secret" + manageTypeKeySuffix + regionKeySuffix);
        if (StringUtils.isEmpty(appKey)) {
            return new RequestBuilder(null, null, false, "暂不支持此站点该店铺管理类型");
        }

        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {
            if (servletRequest.getHeader("content-type") != null &&
                    !servletRequest.getHeader("content-type").contains("x-www-form-urlencoded")) {
                ServletInputStream servletInputStream = servletRequest.getInputStream();
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = servletInputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String body = result.toString(StandardCharsets.UTF_8.name());
                JSONObject params;
                try {
                    params = JSON.parseObject(body);
                } catch (Exception e) {
                    log.error("参数解析异常, body:{}", body, e);
                    return new RequestBuilder(null, null, false, "参数解析异常");
                }
                String fileUrl = params.getString("url");
                String accessToken = params.getString("access_token");
                if (StringUtils.isEmpty(fileUrl)) {
                    return new RequestBuilder(null, null, false, "文件地址不能为空");
                }
                if (StringUtils.isEmpty(accessToken)) {
                    return new RequestBuilder(null, null, false, "授权令牌不能为空");
                }
//                String regionKeySuffix;
//                if (fileUrl.startsWith("https://openapi-b-us.temu.com")) {
//                    regionKeySuffix = "_us";
//                } else if (fileUrl.startsWith("https://openapi-b-global.temu.com")) {
//                    regionKeySuffix = "_global";
//                } else if (fileUrl.startsWith("https://openapi.kuajingmaihuo.com")) {
//                    regionKeySuffix = "_cn";
//                } else if (fileUrl.startsWith("https://openapi-b-eu.temu.com")) {
//                    regionKeySuffix = "_eu";
//                } else {
//                    return new RequestBuilder(null, null, false, "文件地址域名解析失败");
//                }
                RequestBuilder.TemuInfo temuInfo = new RequestBuilder.TemuInfo(appKey, appSecret, accessToken);
                String[] fileUrlSplit = fileUrl.split("com/");
                String[] apiUrlSplit = apiUrl.split("/openapi/router");
                String reqUrl = apiUrlSplit[0] + "/" + fileUrlSplit[1];
                RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(reqUrl).method("GET", null), null, true, null);
                requestBuilder.setApiType("file_download");
                requestBuilder.setTemuInfo(temuInfo);
                return requestBuilder;
            }
            return null;
        }
        return new RequestBuilder(null, null, false, "错误的请求");
    }
    public Request createFileDownloadHeader(Request.Builder requestBuilder, HttpServletRequest httpRequest,
                                            String appKey, String appSecret, String accessToken) {
        Enumeration<String> enumerationOfHeaderNames = httpRequest.getHeaderNames();
        while (enumerationOfHeaderNames.hasMoreElements()) {
            String headerName = (enumerationOfHeaderNames.nextElement()).toLowerCase();
            if ("content-type".equals(headerName)) {
                String[] values = httpRequest.getHeader(headerName).split(";");
                requestBuilder.addHeader("Content-Type", values[0]);
            }
        }
        JSONObject params = new JSONObject();
        params.put("toa-app-key", appKey);
        params.put("toa-access-token", accessToken);
        params.put("toa-timestamp", Instant.now().toEpochMilli() / 1000);
        params.put("toa-random", RandomStringUtils.randomAlphanumeric(32));
        String signContent = SignUtil.getSignContent(params);
        String sign = DigestUtils.md5Hex(appSecret + signContent + appSecret).toUpperCase();
        params.put("toa-sign", sign);
        params.forEach((k, v) -> requestBuilder.addHeader(k, v.toString()));
        return requestBuilder.build();
    }


    public RequestBuilder createPrintOkHttpRequest(HttpServletRequest servletRequest, String region, String manageType) throws Exception {
        String regionKeySuffix;
        if ("cn".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn";
        } else if ("cn-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_cn_test";
        } else if ("us".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us";
        } else if ("us-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_us_test";
        } else if ("eu".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu";
        } else if ("eu-test".equalsIgnoreCase(region)) {
            regionKeySuffix = "_eu_test";
        } else if ("global".equalsIgnoreCase(region)) {
            regionKeySuffix = "_global";
        }  else if ("partner".equalsIgnoreCase(region)) {
            regionKeySuffix = "_partner";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效API站点");
        }
        if (StringUtils.isEmpty(manageType)) {
            return new RequestBuilder(null, null, false, "请求地址错误，缺少店铺管理类型");
        }
        String manageTypeKeySuffix;
        if ("semi-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_semi_managed";
        } else if ("full-managed".equalsIgnoreCase(manageType)) {
            manageTypeKeySuffix = "_full_managed";
        } else {
            return new RequestBuilder(null, null, false, "请求地址错误，无效店铺管理类型");
        }

        String apiUrl = getTemuSettingInfo("url" + regionKeySuffix);
        String appKey = getTemuSettingInfo("app_key" + manageTypeKeySuffix + regionKeySuffix);
        String appSecret = getTemuSettingInfo("app_secret" + manageTypeKeySuffix + regionKeySuffix);
        if (StringUtils.isEmpty(appKey)) {
            return new RequestBuilder(null, null, false, "暂不支持此站点该店铺管理类型");
        }

        String[] apiUrlSplit = apiUrl.split("/openapi/router");
        String reqUrl = apiUrlSplit[0] + "/tool/print?" + servletRequest.getQueryString();
        RequestBuilder requestBuilder = new RequestBuilder((new Request.Builder()).url(reqUrl).method("GET", null), null, true, null);
        requestBuilder.setApiType("tool_print");
        return requestBuilder;
    }

}
