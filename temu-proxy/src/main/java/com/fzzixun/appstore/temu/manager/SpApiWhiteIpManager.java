package com.fzzixun.appstore.temu.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.fzzixun.appstore.temu.dto.SpApiIpDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SpApiWhiteIpManager {

    @Getter
    private static final Map<String, SpApiIpDTO> whiteIpMap = new ConcurrentHashMap<>();

    private static final SpApiCompanyPropertyListener LISTENER;
    private static DataProperty<SpApiIpDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SpApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
    }

    public static void register2Property(DataProperty<SpApiIpDTO> property) {
        synchronized (LISTENER) {
            log.info("[SpApiWhiteIpManager] Registering new property to sp-api ip manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SpApiCompanyPropertyListener implements PropertyListener<SpApiIpDTO> {

        private String getKey(SpApiIpDTO value) {
            return value.getIp();
        }

        @Override
        public synchronized void configUpdate(SpApiIpDTO oldValue, SpApiIpDTO value) {
            // 更新数据
            log.info("[SpApiWhiteIpManager] received: {}", value);
            if (oldValue.getType() == 3) {
                whiteIpMap.remove(this.getKey(oldValue));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
            if (value.getType() == 3) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configLoad(SpApiIpDTO value) {
            // 加载数据
            log.info("[SpApiWhiteIpManager] loaded: {}", value);
            if (value.getType() == 3) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configDelete(SpApiIpDTO value) {
            // 删除数据
            log.info("[SpApiWhiteIpManager] deleted: {}", value);
            if (value.getType() == 3) {
                whiteIpMap.remove(this.getKey(value));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }
    }

}
