package com.fzzixun.appstore.temu;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 *
 * @create 2022/4/22
 */
@SpringBootApplication
@ServletComponentScan
@EnableApolloConfig
@EnableAsync
public class TemuProxyApplication {
    public static void main(String[] args) {
        SpringApplication.run(TemuProxyApplication.class, args);
    }
}

