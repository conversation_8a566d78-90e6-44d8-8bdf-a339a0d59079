package com.fzzixun.appstore.temu.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.temu.dto.RequestRecord;
import com.fzzixun.appstore.temu.dto.SpApiCompanyDTO;
import com.fzzixun.appstore.temu.dto.SpApiIpDTO;
import com.fzzixun.appstore.temu.manager.SpApiCompanyManager;
import com.fzzixun.appstore.temu.manager.SpApiWhiteIpManager;
import com.fzzixun.appstore.temu.utils.*;
import com.squareup.okhttp.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StopWatch;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.Authenticator;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SP-API代理转发服务
 */
@WebServlet("/*")
@Slf4j
public class ProxyServlet extends HttpServlet {

    public static final String LIMIT_KEY = "spapi_limit_key:%s";
    public static final String NUMBER_OF_CALLS_KEY = "numberOfCalls";
    public static final String REQUEST_SIZE_KEY = "requestSize";
    public static final String RESPONSE_SIZE_KEY = "responseSize";


    @Value("${limitStatusCode:429}")
    private Integer limitStatusCode;

    @Value("${limitTime:10}")
    private Integer limitTime;

    @Value("${limitReturnCode:403}")
    private Integer limitReturnCode;

    @Value("${printResponseLog:false}")
    private Boolean printResponseLog;

    @Value("${logStore.name:temu-record}")
    private String logStore;

    @Value("${param.okhttp.read-timeout:30}")
    private Integer okhttpReadTimeout;

    // 测试用，开启后所有转发请求的店铺保存
    @Value("${marketplaceRecord.test:false}")
    private Boolean marketplaceRecordTest;
    @Autowired
    private ProxyUtil proxyUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${eu.proxy.enable:true}")
    private Boolean proxyEnable;
    @Value("${eu.proxy.username:}")
    private String proxyUsername;
    @Value("${eu.proxy.password:}")
    private String proxyPassword;
    @Value("${eu.proxy.ip:}")
    private String proxyIp;
    @Value("${eu.proxy.port:}")
    private Integer proxyPort;

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 3036995738596829749L;

    private OkHttpClient getProxyOkHttpClient() {
        if (!proxyEnable) {
            return new OkHttpClient();
        }
        if (Boolean.TRUE.equals(printResponseLog)) {
            log.info("使用代理地址：{}, 代理端口：{}", proxyIp, proxyPort);
        }
        InetSocketAddress proxyAddr = new InetSocketAddress(proxyIp, proxyPort);
        Proxy proxy = new Proxy(Proxy.Type.SOCKS, proxyAddr);
        java.net.Authenticator.setDefault(new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                if (Boolean.TRUE.equals(printResponseLog)) {
                    log.info("使用代理认证：{}, {}", proxyUsername, proxyPassword);
                }
                return new PasswordAuthentication(proxyUsername, proxyPassword.toCharArray());
            }
        });
        OkHttpClient proxyOkHttpClient = new OkHttpClient();
        proxyOkHttpClient.setProxy(proxy);
        return proxyOkHttpClient;
    }

    @Override
    protected void service(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {
        try {
            String uri = servletRequest.getRequestURI();
            String[] uriInfo = uri.split("/");
            if (uriInfo.length < 3) {
                badRequest(servletResponse, "请求地址错误，缺少API站点或店铺托管类型");
                return;
            }
            if ("/routing.json".equals(uri)) {
                writeResponse(servletResponse, 200, "{\"routes\":[]}");
                return;
            }
            String ipAddress = IpUtil.getIP(servletRequest);
            SpApiIpDTO spApiIpDTO = SpApiWhiteIpManager.getWhiteIpMap().get(ipAddress);
            if (!"/".equals(uri)) {
                log.info("======>>>调用URI地址：{}, 调用IP地址：{}", uri, ipAddress);
                if (StringUtils.isNotEmpty(ipAddress) && spApiIpDTO == null) {
                    log.warn("该IP禁止访问: {}", ipAddress);
                    forbidden(servletResponse, "该IP禁止访问：" + ipAddress);
                    return;
                }
            }
            String region = uriInfo[1];
            String manageType = uriInfo[2];
            RequestBuilder requestBuilder;
            Request request;
            // 文件下载请求
            if (uriInfo.length > 3) {
                String specialUri = uriInfo[3];
                if ("file_download".equals(specialUri)) {
                    requestBuilder = proxyUtil.createDownloadOkHttpRequest(servletRequest, region, manageType);
                    if (!requestBuilder.isSuccess()) {
                        badRequest(servletResponse, requestBuilder.getErrorMsg());
                        return;
                    }
                    RequestBuilder.TemuInfo temuInfo = requestBuilder.getTemuInfo();
                    request = proxyUtil.createFileDownloadHeader(requestBuilder.getReqBuilder(), servletRequest,
                            temuInfo.getAppKey(), temuInfo.getAppSecret(), temuInfo.getAccessToken());
                } else if ("tool".equals(specialUri) && uriInfo.length > 4 && "print".equals(uriInfo[4])) {
                    requestBuilder = proxyUtil.createPrintOkHttpRequest(servletRequest, region, manageType);
                    if (!requestBuilder.isSuccess()) {
                        badRequest(servletResponse, requestBuilder.getErrorMsg());
                        return;
                    }
                    request = proxyUtil.createHeader(requestBuilder.getReqBuilder(), servletRequest);
                } else {
                    log.info("无效的请求路径:{}", uri);
                    badRequest(servletResponse, "请求地址错误，请检查");
                    return;
                }
            } else {
                requestBuilder = proxyUtil.createOkHttpRequest(servletRequest, region, manageType);
                if (Boolean.TRUE.equals(printResponseLog)) {
                    log.info("请求参数===> {}", requestBuilder.getRequestBody());
                }
                if (!requestBuilder.isSuccess()) {
                    badRequest(servletResponse, requestBuilder.getErrorMsg());
                    return;
                }
                request = proxyUtil.createHeader(requestBuilder.getReqBuilder(), servletRequest);
            }
            if (Boolean.TRUE.equals(printResponseLog)) {
                log.info("======>>>[{}][{}]调用地址：{}", servletRequest.getMethod(), ipAddress, requestBuilder.getApiType());
            }

            long requestSize = requestBuilder.getRequestBody() == null ? 0 : requestBuilder.getRequestBody().length();
            String statisticsKey = "temu_" + ipAddress + "_" + LocalDate.now().minusDays(1);
            long responseSize = 0;
            if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(statisticsKey, NUMBER_OF_CALLS_KEY))) {
                hashIncrBy(statisticsKey, NUMBER_OF_CALLS_KEY, 1);
                if (requestSize > 0) {
                    hashIncrBy(statisticsKey, REQUEST_SIZE_KEY, requestSize);
                }
            } else {
                Map<String, Object> maps = new HashMap<>();
                maps.put(NUMBER_OF_CALLS_KEY, 1);
                maps.put(REQUEST_SIZE_KEY, requestSize);
                maps.put(RESPONSE_SIZE_KEY, responseSize);
                hPutAll(statisticsKey, maps);
            }
            OkHttpClient httpClient;
            // 如果是欧洲站点通过代理访问
            if ("eu".equalsIgnoreCase(region)) {
                httpClient = this.getProxyOkHttpClient();
            } else {
                httpClient = new OkHttpClient();
            }
            httpClient.setReadTimeout(okhttpReadTimeout, TimeUnit.SECONDS);
            StopWatch stopWatch = new StopWatch();
            Call call = httpClient.newCall(request);
            stopWatch.start();
            Response response = call.execute();
            stopWatch.stop();
            long responseTime = stopWatch.getTotalTimeMillis();
            if (Boolean.TRUE.equals(printResponseLog)) {
                log.info("[{}],返回码[{}]response=>{}", ipAddress, response.code(), response.toString());
            }
//            limitByStatusCode(key, response.code(), request.url());
            servletResponse.setStatus(response.code(), response.message());
            Headers headers = response.headers();
            for (String headerName : headers.names()) {
//                if (Boolean.TRUE.equals(printResponseLog)) {
//                    log.info("addHeader->> name:{}，value:{}", headerName, headers.get(headerName));
//                }
                if ("Content-Length".equals(headerName)) {
                    servletResponse.addHeader(headerName, String.valueOf(response.body().contentLength()));
                    continue;
                }
                if (!"Transfer-Encoding".equals(headerName)) {
                    servletResponse.addHeader(headerName, headers.get(headerName));
                } else {
//                    if (Boolean.TRUE.equals(printResponseLog)) {
//                        log.info("no addHeader:{}", headerName);
//                    }
                }
            }
            ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
            byte[] responseByte = response.body().bytes().clone();
            String responseResult = new String(responseByte, StandardCharsets.UTF_8);
            if (Boolean.TRUE.equals(printResponseLog)) {
                log.info("[{}]返回结果大小{} ===>>>{}", ipAddress, responseByte.length, responseResult);
            }
            try {
                JSONObject jsonObject = JSONObject.parseObject(responseResult);
                Boolean success = jsonObject.getBoolean("success");
                if (Boolean.FALSE.equals(success)) {
                    log.info("temu响应失败, req: [{}]请求地址：{}, {}, 请求体: {}",  ipAddress, request.url(), requestBuilder.getApiType(), requestBuilder.getRequestBody());
                    String result = responseResult.replaceAll("\\n", "");
                    log.info("temu响应失败, resp: [{}]请求地址：{}, response=>{}", ipAddress, requestBuilder.getApiType(), result);
                }
            } catch (Exception e) {
                log.error("响应非json格式:{}", responseResult);
            }
            if (500 == response.code()) {
                //响应500打印请求和响应
                log.info("500内部错误req: [{}]请求地址：{}, {}, 请求体: {}", ipAddress, request.url(), requestBuilder.getApiType(), requestBuilder.getRequestBody());
                String result = responseResult.replaceAll("\\n", "");
                log.info("500内部错误resp: [{}],返回码[{}], response=>{}", ipAddress, response.code(), result);
            }
            hashIncrBy(statisticsKey, RESPONSE_SIZE_KEY, responseByte.length);
            // 请求记录 ip、公司名、店铺id、url、日期、请求大小、响应大小、
            try {
                String companyName = Optional.ofNullable(SpApiCompanyManager.getCompanyMap().get(spApiIpDTO.getCompanyId()))
                        .map(SpApiCompanyDTO::getCompanyName).orElse("");
                MDC.put("logStore", logStore);
                RequestRecord requestRecord = new RequestRecord(ipAddress, companyName, null,
                        uri, requestBuilder.getApiType(), LocalDate.now(), requestSize, responseByte.length,
                        response.code(), responseTime);
                log.info(JSON.toJSONString(requestRecord));
                MDC.remove("logStore");
            } catch (Exception e) {
                log.error("记录请求记录失败", e);
            }
            servletOutputStream.write(responseByte);
        } catch (Exception e) {
            log.error("代理请求异常！", e);
        }
    }

    private Long hashIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    private void forbidden(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 403, "{\"errors\": \"" + msg + "\"}");
    }

    private void badRequest(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 400, "{\"errors\": \"" + msg + "\"}");
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }

}
