package com.fzzixun.appstore.temu.servlet;

import com.squareup.okhttp.Request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022/6/7
 */
@Data
@NoArgsConstructor
public class RequestBuilder {
    private Request.Builder reqBuilder;
    private String requestBody;
    private boolean success = true;
    private String errorMsg;
    private TemuInfo temuInfo;
    private String apiType;

    public RequestBuilder(Request.Builder reqBuilder, String requestBody, boolean success, String errorMsg) {
        this.reqBuilder = reqBuilder;
        this.requestBody = requestBody;
        this.success = success;
        this.errorMsg = errorMsg;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TemuInfo {
        private String appKey;
        private String appSecret;
        private String accessToken;
    }
}
