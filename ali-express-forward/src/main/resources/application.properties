spring.profiles.active=local
app.id=ali-express-auth
# 阿波罗配置
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=application
# 健康检查
management.endpoints.web.base-path=/status
management.endpoints.web.exposure.include=health
management.server.port=19002
# 数据库配置
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# jpa
#spring.jpa.properties.hibernate.globally_quoted_identifiers=true
#spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
# 文件上传大小
spring.servlet.multipart.max-file-size = 200MB
spring.servlet.multipart.max-request-size = 400MB

logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} [%X{req.trace:-}] %clr(${LOG_LEVEL_PATTERN:%5p}) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n
