package com.fzzixun.appstore.aliexpress.controller;


import com.fzzixun.appstore.aliexpress.base.infrastructure.annotation.WebLog;
import com.fzzixun.appstore.aliexpress.po.ForwardRequestPo;
import com.fzzixun.appstore.aliexpress.service.ExpressApiService;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.global.iop.util.ApiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@WebLog
@RequestMapping("/ali-express-forward/rest/v1/forward")
@RestController
@Api(tags = "代理转发")
@RequiredArgsConstructor
public class ForwardController {

    private final ExpressApiService expressApiService;

    @PostMapping("")
    @ApiOperation("代理转发")
    public ResponseVo<String> forwardRequest(@RequestBody @Valid ForwardRequestPo po) throws ApiException {
        return expressApiService.forwardRequest(po);
    }
}
