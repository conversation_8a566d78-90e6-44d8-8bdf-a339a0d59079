package com.fzzixun.appstore.aliexpress.po;

import com.fzzixun.appstore.aliexpress.valid.EnumValue;
import com.global.iop.domain.Protocol;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;


@Data
public class ForwardRequestPo {

    @ApiModelProperty("AliExpress接口路径")
    @NotEmpty(message = "AliExpress接口路径不能为空")
    private String apiName;

    @ApiModelProperty("AliExpress接口协议类型: TOP,GOP,REST_VND_2")
    @NotEmpty(message = "接口协议类型不能为空")
    @EnumValue(target = Protocol.class, message = "接口协议类型有误")
    private String protocol;

    @ApiModelProperty("AliExpress授权token")
    @NotEmpty(message = "AliExpress授权token不能为空")
    private String accessToken;

    @ApiModelProperty("AliExpress接口业务参数")
    private Map<String, String> bizContent;

}
