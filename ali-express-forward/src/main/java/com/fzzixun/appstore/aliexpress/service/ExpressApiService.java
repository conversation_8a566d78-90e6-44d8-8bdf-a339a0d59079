package com.fzzixun.appstore.aliexpress.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.aliexpress.base.infrastructure.util.MdcUtil;
import com.fzzixun.appstore.aliexpress.po.ForwardRequestPo;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import com.global.iop.util.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExpressApiService {

    @Value("${ali-express.api.response.log.enable:false}")
    private Boolean logEnable;

    private final IopClient iopClient;


    public ResponseVo<String> forwardRequest(ForwardRequestPo po) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName(po.getApiName());
        Map<String, String> bizContent = po.getBizContent();
        if (bizContent != null) {
            bizContent.forEach(request::addApiParameter);
        }
        log.info("请求AliExpress apiParams：{}", JSON.toJSONString(request.getApiParams()));
        IopResponse response = iopClient.execute(request, po.getAccessToken(), Protocol.valueOf(po.getProtocol()));
        log.info("[{}]请求AliExpress接口[{}], type: {}", response.getRequestId(), po.getApiName(), response.getType());
        String body = response.getBody();
        if (Boolean.TRUE.equals(logEnable)) {
            log.info("[{}]接口[{}]response: {}", response.getRequestId(), po.getApiName(), body);
        }
        ResponseVo<String> responseVo = ResponseVo.ofSuccess(body);
        responseVo.setId(MdcUtil.getRequestId());
        return responseVo;
    }

}
