include:
  - project: 'zixun-op/sharelib/gitlabci'
    file: 
      - jobs/build/java.yml
      - jobs/docker/docker.yml
      - jobs/deploy/k8s.yml
      - jobs/tools.yml

default:
  tags:
    - k8s

# 流水线控制
workflow:
  rules:
    - !reference [.com_workflow_rules, rules]
    - if: $CI_COMMIT_BEFORE_SHA != '0000000000000000000000000000000000000000' && ( $CI_COMMIT_TAG || $CI_COMMIT_REF_NAME == "pre" ||  $CI_COMMIT_REF_NAME == 'test' )
    - when: never

variables:
  ## GIT_DEPTH (int): 设置浅克隆
  GIT_DEPTH: 1
  #必要变量
  PROJECT_NAME: a1-02-appstore

  ## 部署应用k8s
  HELM_IMAGE_REGISTRY: "op-docker.fzzixun.com"
  HELM_IMAGE_REPOSITORY: $PROJECT_NAME/$APP_NAME/$CI_COMMIT_REF_SLUG
  HELM_IMAGE_TAG: $CI_COMMIT_SHORT_SHA
  HELM_WORKDIR: /opt

  ##钉钉
  DING_TOKEN: "8e101bc27e5b1a5951d9513a708792d110e088774a5921f704425eaef2969e97"
  DING_SECRET: "SECc0d0b6db8c4ea908e3f44e2faa76d0f7d54cc4b7237900a001ae08589d027cb9"
  DING_ATMOBILES_ALL: "false"

  K8S_RESOURCES: deployment

# 运行阶段  
stages:
  - build
  - docker
  - qatrigger
  - qaverify
  - deploy-k8s


#######################
# 通知步骤
钉钉通知_成功:
  extends: .success_dingding

审计发送_成功:
  extends: .pushMesgAuditLog
  variables:
    EXTRA_PARAMETERS: status=succeed
  when: on_success

钉钉通知_失败:
  extends: .failure_dingding

审计发送_失败:
  extends: .pushMesgAuditLog
  variables:
    EXTRA_PARAMETERS: status=failed
  when: on_failure



## 构建作业
全局-build:
  tags:
    - k8s-eci
  variables:
    KUBERNETES_POD_ANNOTATIONS_1: "k8s.aliyun.com/eci-use-specs=4-8Gi"
    DOCKER_IMAGE_BUILD: op-docker.fzzixun.com/jenkins/maven-openjdk8:3.8-v1
    KUBERNETES_CPU_LIMIT: 4
    KUBERNETES_MEMORY_LIMIT: 8Gi
    JAVA_WORK_DIR: "."
  allow_failure: false
  extends: .java_build
  script:
  - set -x
  - >
    mvn clean  package -B -ntp -e -U -T 2C 
    -Dmaven.test.skip=true
  artifacts:
    paths:
      - ./*/target/*.jar
    expire_in: 1 hrs
  rules:
  - if: $CI_COMMIT_REF_NAME

##########以下是发布到 k8s 的服务
######## 通用模板job
.common_docker:
  tags:
    - k8s
  stage: docker
  needs:
    - job: 全局-build
      artifacts: true
  variables:
    DOCKER_IMAGE_BASE: op-docker.fzzixun.com/jenkins/openjdk:8u342-jdk
    JAVA_JAR_OUTPUT: target
    #JAVA_WORK_DIR
  extends: .java_docker
  allow_failure: false
  rules:
  - if: $CI_COMMIT_REF_NAME
    changes:
      - $JAVA_WORK_DIR/**/*

.common_deploy:
  stage: deploy-k8s
  allow_failure: false
  needs:
    - job: QA_check_downstream
      artifacts: false
  variables:
    GIT_CHECKOUT: "false"
  extends: .only_update_image_tag
  environment:
    name: $deploy_env
    action: prepare
  before_script:
  - !reference [.initialization_k8s_cluster, before_script]
  script:
  - !reference [.only_update_image_tag, script]
  - >
    kubectl  get $K8S_RESOURCES $APP_NAME -n $NAMESPACE   -o json  --no-headers=true  --server-print=true
    |jq 'del(.metadata.creationTimestamp)|del(.metadata.resourceVersion)|del(.metadata.uid)'
    |jq "(.spec.template.spec.containers[] |select (.name == \"$APP_NAME\")|.args[-1:] )= [\"/opt/$APP_NAME.jar\"]"
    |kubectl apply -f  -


.common_deploy_test:
  extends: .common_deploy
  variables:
    deploy_env: test
  parallel:
    matrix:
      - CLUSTER_NAME: superbrowser
        NAMESPACE: a1-02-appstore-$deploy_env
  rules:
  - if: $CI_COMMIT_REF_NAME == 'test'
    when: manual
    changes:
      - $JAVA_WORK_DIR/**/*
  - when: never

.common_deploy_pre:
  extends: .common_deploy
  variables:
    deploy_env: pre
  parallel:
    matrix:
      - CLUSTER_NAME: superbrowser
        NAMESPACE: a1-02-appstore-$deploy_env
  rules:
  - if: $CI_COMMIT_REF_NAME == 'pre'
    when: manual
    changes:
      - $JAVA_WORK_DIR/**/*
  - when: never

.common_deploy_prod:
  extends: .common_deploy
  variables:
    deploy_env: prod
  parallel:
    matrix:
      - CLUSTER_NAME: superbrowser
        NAMESPACE: a1-02-appstore-$deploy_env
  rules:
  - if: $CI_COMMIT_TAG
    when: manual
    changes:
      - $JAVA_WORK_DIR/**/*
  - when: never

##############
# 各自的服务
#############
#############
#############
# ad-proxy
.ad-proxy_template: &ad-proxy_template
  variables: 
    JAVA_WORK_DIR: aws-ad-proxy
    APP_NAME: ad-proxy
  needs:
    - job: ad-proxy-docker
      artifacts: false

ad-proxy-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: aws-ad-proxy
    APP_NAME: ad-proxy

ad-proxy-发布测试环境:
  extends: .common_deploy_test
  <<: *ad-proxy_template

ad-proxy-预上环境:
  extends: .common_deploy_pre
  <<: *ad-proxy_template

ad-proxy-正式环境:
  extends: .common_deploy_prod
  <<: *ad-proxy_template


#############
# ad-auth
.ad-auth_template: &ad-auth_template
  variables: 
    JAVA_WORK_DIR: aws-ad-auth
    APP_NAME: ad-auth
  needs:
    - job: ad-auth-docker
      artifacts: false

ad-auth-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: aws-ad-auth
    APP_NAME: ad-auth

ad-auth-发布测试环境:
  extends: .common_deploy_test
  <<: *ad-auth_template

ad-auth-预上环境:
  extends: .common_deploy_pre
  <<: *ad-auth_template

ad-auth-正式环境:
  extends: .common_deploy_prod
  <<: *ad-auth_template

#############
# ali-express-push
.ali-express-push_template: &ali-express-push_template
  variables: 
    JAVA_WORK_DIR: ali-express-push
    APP_NAME: ali-express-push
  needs:
    - job: ali-express-push-docker
      artifacts: false

ali-express-push-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: ali-express-push
    APP_NAME: ali-express-push

ali-express-push-发布测试环境:
  extends: .common_deploy_test
  <<: *ali-express-push_template

ali-express-push-预上环境:
  extends: .common_deploy_pre
  <<: *ali-express-push_template

ali-express-push-正式环境:
  extends: .common_deploy_prod
  <<: *ali-express-push_template


#############
# ali-express-auth
.ali-express-auth_template: &ali-express-auth_template
  variables: 
    JAVA_WORK_DIR: ali-express-auth
    APP_NAME: ali-express-auth
  needs:
    - job: ali-express-auth-docker
      artifacts: false

ali-express-auth-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: ali-express-auth
    APP_NAME: ali-express-auth

ali-express-auth-发布测试环境:
  extends: .common_deploy_test
  <<: *ali-express-auth_template

ali-express-auth-预上环境:
  extends: .common_deploy_pre
  <<: *ali-express-auth_template

ali-express-auth-正式环境:
  extends: .common_deploy_prod
  <<: *ali-express-auth_template


#############
# ali-express-forward
.ali-express-forward_template: &ali-express-forward_template
  variables: 
    JAVA_WORK_DIR: ali-express-forward
    APP_NAME: ali-express-forward
  needs:
    - job: ali-express-forward-docker
      artifacts: false

ali-express-forward-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: ali-express-forward
    APP_NAME: ali-express-forward

ali-express-forward-发布测试环境:
  extends: .common_deploy_test
  <<: *ali-express-forward_template

ali-express-forward-预上环境:
  extends: .common_deploy_pre
  <<: *ali-express-forward_template

ali-express-forward-正式环境:
  extends: .common_deploy_prod
  <<: *ali-express-forward_template


#############
# spapi-auth
.spapi-auth_template: &spapi-auth_template
  variables: 
    JAVA_WORK_DIR: spapi-auth
    APP_NAME: spapi-auth
  needs:
    - job: spapi-auth-docker
      artifacts: false

spapi-auth-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: spapi-auth
    APP_NAME: spapi-auth

spapi-auth-发布测试环境:
  extends: .common_deploy_test
  <<: *spapi-auth_template

spapi-auth-预上环境:
  extends: .common_deploy_pre
  <<: *spapi-auth_template

spapi-auth-正式环境:
  extends: .common_deploy_prod
  <<: *spapi-auth_template

#############
# spapi-proxy
.spapi-proxy_template: &spapi-proxy_template
  variables: 
    JAVA_WORK_DIR: spapi-proxy
    APP_NAME: spapi-proxy
  needs:
    - job: spapi-proxy-docker
      artifacts: false

spapi-proxy-docker:
  extends: .common_docker
  variables: 
    JAVA_WORK_DIR: spapi-proxy
    APP_NAME: spapi-proxy

spapi-proxy-发布测试环境:
  extends: .common_deploy_test
  <<: *spapi-proxy_template

spapi-proxy-预上环境:
  extends: .common_deploy_pre
  <<: *spapi-proxy_template

spapi-proxy-正式环境:
  extends: .common_deploy_prod
  <<: *spapi-proxy_template

#############
# temu-proxy
.temu-proxy_template: &temu-proxy_template
  variables:
    JAVA_WORK_DIR: temu-proxy
    APP_NAME: temu-proxy
  needs:
    - job: temu-proxy-docker
      artifacts: false

temu-proxy-docker:
  extends: .common_docker
  variables:
    JAVA_WORK_DIR: temu-proxy
    APP_NAME: temu-proxy

temu-proxy-发布测试环境:
  extends: .common_deploy_test
  <<: *temu-proxy_template

temu-proxy-预上环境:
  extends: .common_deploy_pre
  <<: *temu-proxy_template

temu-proxy-正式环境:
  extends: .common_deploy_prod
  <<: *temu-proxy_template

#############
# sif-proxy
.sif-proxy_template: &sif-proxy_template
  variables:
    JAVA_WORK_DIR: sif-proxy
    APP_NAME: sif-proxy
  needs:
    - job: sif-proxy-docker
      artifacts: false

sif-proxy-docker:
  extends: .common_docker
  variables:
    JAVA_WORK_DIR: sif-proxy
    APP_NAME: sif-proxy

sif-proxy-发布测试环境:
  extends: .common_deploy_test
  <<: *sif-proxy_template

sif-proxy-预上环境:
  extends: .common_deploy_pre
  <<: *sif-proxy_template

sif-proxy-正式环境:
  extends: .common_deploy_prod
  <<: *sif-proxy_template

#############
# shein-auth
.shein-auth: &shein-auth_template
  variables:
    JAVA_WORK_DIR: shein-auth
    APP_NAME: shein-auth
  needs:
    - job: shein-auth-docker
      artifacts: false

shein-auth-docker:
  extends: .common_docker
  variables:
    JAVA_WORK_DIR: shein-auth
    APP_NAME: shein-auth

shein-auth-发布测试环境:
  extends: .common_deploy_test
  <<: *shein-auth_template

shein-auth-预上环境:
  extends: .common_deploy_pre
  <<: *shein-auth_template

shein-auth-正式环境:
  extends: .common_deploy_prod
  <<: *shein-auth_template

#############
# shein-proxy
.shein-proxy_template: &shein-proxy_template
  variables:
    JAVA_WORK_DIR: shein-proxy
    APP_NAME: shein-proxy
  needs:
    - job: shein-proxy-docker
      artifacts: false

shein-proxy-docker:
  extends: .common_docker
  variables:
    JAVA_WORK_DIR: shein-proxy
    APP_NAME: shein-proxy

shein-proxy-发布测试环境:
  extends: .common_deploy_test
  <<: *shein-proxy_template

shein-proxy-预上环境:
  extends: .common_deploy_pre
  <<: *shein-proxy_template

shein-proxy-正式环境:
  extends: .common_deploy_prod
  <<: *shein-proxy_template

# 新增job用于触发下游仓库流水线
QA_Check_trigger_job:
  stage: qatrigger
  extends: .QA_Check_trigger_job
  variables:
    trigger_project_id: "2306"
    APP_NAME: $CI_JOB_NAME
  
# 新增job监听下游流水线状态
QA_check_downstream:
  stage: qaverify
  extends: .QA_check_downstream
  variables:
    trigger_project_id: "2306"