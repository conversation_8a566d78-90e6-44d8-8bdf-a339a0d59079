package com.fzzixun.appstore.ad.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: YZH
 * @Date: 2021/6/11 19:52
 */
@Data
@ToString
public class TokenDto {

    @JsonProperty(value = "access_token")
    private String accessToken;

    @JsonProperty(value = "refresh_token")
    private String refreshToken;

    @JsonProperty(value = "token_type")
    private String tokenType;

    @JsonProperty(value = "expires_in")
    private Long expiresIn;

    @JsonProperty(value = "state")
    private String state;

    public boolean isGetTokenSucceed() {
        return StringUtils.isNoneEmpty(accessToken, refreshToken, tokenType);
    }

    public static void main(String[] args) {
        TokenDto tokenDto = new TokenDto();
        tokenDto.setAccessToken("1");
        tokenDto.setRefreshToken("2");
        tokenDto.setTokenType("4");
        System.out.println(tokenDto.isGetTokenSucceed());
    }
}
