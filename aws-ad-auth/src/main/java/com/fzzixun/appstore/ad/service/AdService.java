package com.fzzixun.appstore.ad.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.ad.enums.AuthType;
import com.fzzixun.appstore.ad.enums.RegionEnum;
import com.fzzixun.appstore.ad.enums.RegionToken;
import com.fzzixun.appstore.ad.response.TokenDto;
import com.fzzixun.appstore.ad.util.AesUtil;
import com.fzzixun.appstore.ad.util.FeishuNotifyUtil;
import com.fzzixun.appstore.aliexpress.base.infrastructure.util.MdcUtil;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023/7/4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdService {

    @Value("${app.aws-ad-client-id:amzn1.application-oa2-client.c45a02bc6a8d4633876adef5789ce073}")
    private String clientId;

    @Value("${app.aws-ad-client-secret:9bb7030b1ee614948264fcac3b8edc34c64f331c5bb6c6fd774a120ebf8adcd1}")
    private String clientSecret;

    @Value("${ad.redirect.uri:https://sbappstoreapi.ziniao.com/ad-auth/rest/v1/auth/finish}")
    public String redirectUri;

    @Value("${ad.v2.redirect.uri:https://sbappstoreapi.ziniao.com/ad-auth/rest/v2/auth/finish}")
    public String redirectUriV2;

    @Value("${auth.index:}")
    private String authIndex;

    @Value("${proxy.index:https://sbappstoreapi.ziniao.com/ad-proxy}")
    private String proxyIndex;

    @Autowired
    private CallbackService callbackService;

    @Value("${stateExpiredHours:1}")
    private Integer stateExpiredHours;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${enabledStateEncrypt:false}")
    private Boolean enabledStateEncrypt;

    @Value("${illegal.state.alert.token:c49856ae7e115277ff9d434fa0c267425d2eb8f2621b7f18846e6d47b3f2c146}")
    private String illegalStateAlertToken;

    // ================================飞书机器人==================================================
    // 告警机器人
    @Value("${param.feishu.warning-notify.token:9f461fa1-1f69-48e4-b060-d9e7f3b7c9d4}")
    private String feishuWarningNotifyToken;

    @Value("${param.feishu.warning-notify.secret:xItIkoPRND0wQdZigTtrF}")
    private String feishuWarningNotifySecret;

    /**
     * https://www.amazon.com/ap/oa
     * ?client_id=YOUR_LWA_CLIENT_ID
     * &scope=advertising::campaign_management
     * &response_type=code
     * &redirect_uri=YOUR_RETURN_URL
     *
     * @param region
     * @param state
     * @param request
     * @return
     */
    public ResponseVo<String> getAuthUrl(String region, String state, String appId, HttpServletRequest request) {
        String authUrl;
        try {
            authUrl = getAuthUrl(region, state, appId);
        } catch (Exception e) {
            return ResponseVo.ofBadRequestWithId("get auth url error :" + e.getMessage(), MdcUtil.getRequestId());
        }
        ResponseVo<String> responseVo = ResponseVo.ofSuccess(authUrl);
        responseVo.setId(MdcUtil.getRequestId());
        return responseVo;
    }

    public String getAuthUrl(String region, String state, String appId) throws URISyntaxException, UnsupportedEncodingException {
        String authUrl;
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("site", region);
        String redirectUrl = redirectUri;
        String stateParam;
        if (appId != null) {
            //V2版本新增自定义参数
            queryParams.put("appId", appId);
            if (StringUtils.isNotBlank(state)) {
                queryParams.put("extInfo", state);
            }
            redirectUrl = redirectUriV2;
            stateParam = addQueryParamsToURL(redirectUrl, queryParams);
        } else {
            stateParam = addQueryParamsToURL(state, queryParams);
        }

        String stateKey = URLEncoder.encode(stateParam, "UTF-8");
        if (enabledStateEncrypt) {
            SecureUtil.disableBouncyCastle();
            stateKey = IdUtil.fastSimpleUUID();
            String encryptResult = AesUtil.encryptBase64(stateParam, AesUtil.defaultKey);
            log.info("加密state:{} ，加密结果（Base64）: {}， key：{}", stateParam, encryptResult, stateKey);
            //stateKey存入redis,encryptBase64为值，默认1小时过期
            redisTemplate.opsForValue().set(stateKey, encryptResult, stateExpiredHours, TimeUnit.HOURS);
        }

        authUrl = String.format("%s?response_type=code&scope=advertising::campaign_management&redirect_uri=%s&client_id=%s&state=%s",
                RegionEnum.getRegionByName(region).getUrl(), redirectUrl, clientId, stateKey);
        return authUrl;
    }

    public static String addQueryParamsToURL(String url, Map<String, String> queryParams) throws URISyntaxException, UnsupportedEncodingException {
        if (queryParams != null && !queryParams.isEmpty()) {
            URI uri = new URI(url);
            String query = uri.getQuery();
            StringBuilder sb = new StringBuilder(query != null ? query : "");
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                String param = entry.getKey();
                String value = URLEncoder.encode(entry.getValue(), "UTF-8");
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(param).append("=").append(value);
            }
            query = sb.toString();
            return new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), query, uri.getFragment()).toString();
        }
        return url;
    }

    public static String getQueryParamValue(String url, String param) {
        try {
            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                String[] queryParams = query.split("&");
                for (String queryParam : queryParams) {
                    String[] paramParts = queryParam.split("=");
                    if (paramParts.length == 2 && paramParts[0].equals(param)) {
                        return URLDecoder.decode(paramParts[1], "UTF-8");
                    }
                }
            }
        } catch (URISyntaxException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws UnsupportedEncodingException, URISyntaxException {
//        String url = "https://example.com";
//        Map<String, String> queryParams = new HashMap<>();
//        queryParams.put("site", "na");
//        queryParams.put("channel", "cca");
//        String modifiedURL = addQueryParamsToURL(url, queryParams);
//        System.out.println(modifiedURL);
//        String param = "channel";
//        String paramValue = getQueryParamValue(modifiedURL, param);
//        System.out.println("Value of " + param + ": " + paramValue);
//        String paramToRemove = "param1";
//        String modifiedURL2 = removeQueryParam("https://example.com?param1=value1", paramToRemove);
//        System.out.println(modifiedURL2);
//        String modifiedURL3 = removeQueryParam("https://example.com?param1=value1&param2=value2", paramToRemove);
//        System.out.println(modifiedURL3);
//        AdService adService = new AdService();
//        String authUrl = adService.getAuthUrl("na", "custom params", "2019212121");
//        System.out.println(authUrl);
//        System.out.println(getQueryParamValue("https://sbappstoreapi.ziniao.com/ad-auth/rest/v1/auth/finish?site=na&appId=2019212121&extInfo=custom+params", "appId"));
        System.out.println(getQueryParamValue("https://test-sbappstoreapi.ziniao.com/spapi-auth/authorize/finish?appId=202192122121214&extInfo=%257B%2522xx%2522%253A%2522y%2522%257D", "appId"));
        System.out.println(getQueryParamValue("https://test-sbappstoreapi.ziniao.com/spapi-auth/authorize/finish?appId=202192122121214&extInfo=%257B%2522xx%2522%253A%2522y%2522%257D", "extInfo"));
        String uri = "https://test-sbappstoreapi.ziniao.com/spapi-auth/authorize/finish?appId=202192122121214&extInfo=%257B%2522xx%2522%253A%2522y%2522%257D";
        System.out.println(URLDecoder.decode(URLDecoder.decode(uri, "UTF-8"), "UTF-8"));
    }

    public void createToken(String code, String state, String scope, HttpServletResponse response) throws IOException {

        String decryptState = state;
        if (enabledStateEncrypt) {
            //判断redis中是否有某个key
            if (Boolean.FALSE.equals(redisTemplate.hasKey(state))) {
                log.error("找不到state，授权失败！{} ", state);
                FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【AD】找不到state，授权失败！" + state, false);
                //重定向到授权失败页面
                response.sendRedirect(authIndex + "/ad-auth/failure.html");
                return;
            }
            String stateValue;
            try {
                stateValue = (String) redisTemplate.opsForValue().get(state);
                log.info("获取的state：{} ", stateValue);
            } catch (Exception e) {
                log.info("获取state异常！{} ", state);
                FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【AD】获取state异常，授权失败！" + state, false);
                response.sendRedirect(authIndex + "/ad-auth/failure.html");
                return;
            }

            // 执行解密
            SecureUtil.disableBouncyCastle();
            decryptState = AesUtil.decryptStr(stateValue, AesUtil.defaultKey);
            log.info("解密decryptState:{}，key:{}，value:{}", decryptState, state, stateValue);
            if (StringUtils.isBlank(decryptState)) {
                log.error("解密decryptState失败:{}，key:{}，value:{}", decryptState, state, stateValue);
                FeishuNotifyUtil.sendMsgToGroup(feishuWarningNotifyToken, feishuWarningNotifySecret, "【AD】解密decryptState失败，授权失败！" + state, false);
                //授权失败
                response.sendRedirect(authIndex + "/spapi/auth/failure.html");
                return;
            }
        }

        String site = getQueryParamValue(decryptState, "site");
        Map<String, String> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("redirect_uri", redirectUri);
        params.put("code", code);
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);

        String body;
        try {
            String tokenUrl = RegionToken.getAuthUrl(site);
            String contentType = "application/x-www-form-urlencoded;charset=UTF-8";
            if ("FE".equalsIgnoreCase(site)) {
                //远东走代理
                //https://api.amazon.co.jp/auth/o2/token
                //https://sbappstoreapi.ziniao.com/ad-proxy/FE/auth/o2/token
                tokenUrl = proxyIndex + "/" + site + "/auth/o2/token";
                //            contentType = "application/json";
            }
            HttpRequest httpRequest = HttpUtil.createPost(
                    tokenUrl
            );
            httpRequest.header("Content-Type", contentType);
            httpRequest.formStr(params);
            HttpResponse httpResponse = httpRequest.execute();
            body = httpResponse.body();
        } catch (HttpException e) {
            log.error("获取token异常！请求：{} ", params, e);
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
            return;
        }
        log.info("获取AD令牌响应：{}", body);
        if (StringUtils.isBlank(body)) {
            log.info("获取AD令牌响应为空！响应：{} ", body);
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
            return;
        }
        TokenDto tokenDto = JSONObject.parseObject(
                body,
                TokenDto.class
        );

        if (!tokenDto.isGetTokenSucceed()) {
            log.info("获取AD令牌失败，不推送");
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
            return;
        }
        // 重定向令牌
        String redirectUrl = removeQueryParam(decryptState, "site");

        //推送令牌
        callbackService.sendCallback("AWS-AD-TOKEN", redirectUrl, JSONObject.toJSONString(tokenDto), Constant.CALLBACK_MESSAGE_TYPE_ADS_TOKEN);

        try {
            String urlParams = "accessToken=" + URLEncoder.encode(tokenDto.getAccessToken(), "utf-8")
                    + "&tokenType=" + tokenDto.getTokenType()
                    + "&expiresIn=" + tokenDto.getExpiresIn()
                    + "&refreshToken=" + URLEncoder.encode(tokenDto.getRefreshToken(), "utf-8");
            if (redirectUrl.contains("?")) {
                redirectUrl += "&" + urlParams;
            } else {
                redirectUrl += "?" + urlParams;
            }
            response.sendRedirect(redirectUrl);
        } catch (Exception ex) {
            log.error(String.format("重定向失败， 接口地址： %s， 参数：%s", redirectUrl, JSONObject.toJSONString(tokenDto)), ex);
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
        }
    }

    public void createTokenV2(String code, String state, String scope, HttpServletResponse response) throws IOException, URISyntaxException {
        String appId = getQueryParamValue(state, "appId");
        log.info("获取appId:{}", appId);
        String extInfo = getQueryParamValue(state, "extInfo");
        log.info("获取extInfo:{}", extInfo);
        String site = getQueryParamValue(state, "site");
        log.info("获取site:{}", site);
        Map<String, String> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("redirect_uri", redirectUriV2);
        params.put("code", code);
        params.put("client_id", clientId);
        params.put("client_secret", clientSecret);
        HttpRequest httpRequest = HttpUtil.createPost(
                RegionToken.getAuthUrl(site)
        );
        httpRequest.header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        httpRequest.formStr(params);
        String body;
        try (HttpResponse httpResponse = httpRequest.execute()) {
            body = httpResponse.body();
        }
        log.info("获取AD令牌响应：{}", body);
        if (StringUtils.isBlank(body)) {
            log.error("获取AD令牌body为空，授权失败。{}", appId);
            //重定向到授权失败页面
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
            return;
        }
        TokenDto tokenDto = JSONObject.parseObject(
                body,
                TokenDto.class
        );

        if (!tokenDto.isGetTokenSucceed()) {
            log.error("获取AD令牌失败，授权失败。{}", appId);
            //重定向到授权失败页面
            response.sendRedirect(authIndex + "/ad-auth/failure.html");
            return;
        }

        if (StringUtils.isNotBlank(appId)) {
            if (StringUtils.isNotBlank(extInfo)) {
                //推送数据添加extInfo
                tokenDto.setState(extInfo);
            }
            //推送令牌
            String result = callbackService.sendCallback(appId, JSONObject.toJSONString(tokenDto), AuthType.AD);
            if (StringUtils.equals(result, "success")) {
                log.info("授权成功! {}, {}", appId, result);
                response.sendRedirect(authIndex + "/ad-auth/success.html");
            } else {
                log.error("推送AD令牌失败，授权失败{}", appId);
                //重定向到授权失败页面
                response.sendRedirect(authIndex + "/ad-auth/failure.html");
            }
        }
    }

    public static String removeQueryParam(String url, String paramToRemove) {
        try {
            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                Map<String, String> queryParams = new HashMap<>();
                String[] queryParamsArray = query.split("&");
                for (String queryParam : queryParamsArray) {
                    String[] paramParts = queryParam.split("=");
                    if (paramParts.length == 2) {
                        queryParams.put(paramParts[0], paramParts[1]);
                    }
                }
                queryParams.remove(paramToRemove);
                if (!queryParams.isEmpty()) {
                    StringBuilder sb = new StringBuilder();
                    for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                        if (sb.length() > 0) {
                            sb.append("&");
                        }
                        sb.append(entry.getKey()).append("=").append(entry.getValue());
                    }
                    query = sb.toString();
                } else {
                    query = null;
                }
            }
            return new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), query, uri.getFragment()).toString();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        return url;
    }
}
