package com.fzzixun.appstore.ad.controller;

import com.fzzixun.appstore.ad.request.AuthorizeV2Req;
import com.fzzixun.appstore.ad.service.AdService;
import com.fzzixun.appstore.aliexpress.base.infrastructure.annotation.WebLog;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import com.fzzixun.appstore.common.StoreProxyComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;

@Slf4j
@WebLog
@RequestMapping("/ad-auth/rest/v2/auth")
@RestController
@Api(tags = "广告授权V2")
@RequiredArgsConstructor
public class AuthV2Controller {

    @Autowired
    private StoreProxyComponent storeProxyComponent;

    private final AdService adService;

    @PostMapping("url")
    @ApiOperation("获取授权地址")
    public ResponseVo<String> authUrl(AuthorizeV2Req authorizeV2Req,
                                      HttpServletRequest request,
                                      HttpServletResponse response) {
        return adService.getAuthUrl(authorizeV2Req.getRegion(), authorizeV2Req.getState(), authorizeV2Req.getAppId(), request);
    }

    @GetMapping("/finish")
    @ApiOperation("接收code")
    public void receiveCode(@RequestParam(required = false) String code,
                            @RequestParam(required = false) String state,
                            @RequestParam(required = false) String scope,
                            HttpServletResponse response) throws IOException, URISyntaxException {
        log.info("V2接收到code: {},{},{}", code, state, scope);
        adService.createTokenV2(code, state, scope, response);
    }

    @GetMapping("/storeProxy")
    @ApiOperation("storeProxy")
    public void storeProxy(@RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long storeId) {
        storeProxyComponent.storeProxy(companyId, storeId);
    }

}
