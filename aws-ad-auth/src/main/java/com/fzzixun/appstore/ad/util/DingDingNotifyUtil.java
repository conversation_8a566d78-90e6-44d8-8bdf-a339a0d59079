package com.fzzixun.appstore.ad.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description DingDingNotifyUtil
 * @date 2021/5/13 20:18
 */
@Slf4j
public class DingDingNotifyUtil {

    private static final HttpTool httpTool = new HttpTool();

    public static final String DEFAULT_TOKEN = "0831bf2c8f4876048a84ad0dc25034b7f03e012979fba8993ea196f512ee35e4";

    //正式
    public static final String WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=";
    //测试
//    public static final String WEBHOOK = "https://oapi.dingtalk.com/robot/send?access_token=a45c1ebbfef5614e55216a6ade34a3aa526e31f12169a3701a6c84ff2aaf5f58";

    public static void sendMsg(String msg, String token) {
        if (StringUtils.isEmpty(token)) {
            token = DEFAULT_TOKEN;
        }
        Map<String, String> textMap = new HashMap<>();
        textMap.put("content", msg);
        Map<String, Object> atMap = new HashMap<>();
        atMap.put("isAtAll", false);
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("msgtype", "text");
        bodyMap.put("text", JSON.toJSONString(textMap));
        bodyMap.put("at", JSON.toJSONString(atMap));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        String ret = null;
        try {
            Response response = httpTool.post(WEBHOOK + token, JSON.toJSONString(bodyMap), headers);
            ret = response.body().string();
        } catch (Exception e) {
            log.error("send ding msg error:{}", e.getMessage());
        }
        log.info("send ding msg success:{}", ret);
    }


    public static boolean sendDingMdMsg(String token, String title, String text, boolean isAtAll) {
        Map<String, Object> atMap = new HashMap<>();
//        atMap.put("atMobiles", Arrays.asList("13705937021"));
        atMap.put("isAtAll", isAtAll);

        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("msgtype", "markdown");
        Map<String, Object> markdown = new HashMap<>();
        markdown.put("title", title);
        markdown.put("text", text);
        bodyMap.put("markdown", JSONObject.toJSONString(markdown));
        bodyMap.put("at", JSONObject.toJSONString(atMap));
        String bodyStr = JSONObject.toJSONString(bodyMap);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        String ret = null;
        try {
            Response response = httpTool.post(WEBHOOK + token, bodyStr, headers);
            ret = response.body().string();
            if (response.code() != HttpStatus.OK.value()) {
                log.error("send ding msg error:{}, body:{}", ret, bodyStr);
                return false;
            }
            JSONObject jsonObject = JSON.parseObject(ret);
            Long errcode = jsonObject.getLong("errcode");
            if (errcode != 0) {
                log.error("send ding msg error:{}, message:{}, body:{}", ret, jsonObject.getString("errmsg"), bodyStr);
                return false;
            }
            log.info("send ding msg success:{}", ret);
            return true;
        } catch (Exception e) {
            log.error("send ding msg error:{}, body:{}", e.getMessage(), bodyStr, e);
            return false;
        }
    }


    public static void main(String[] args) {
        DingDingNotifyUtil.sendMsg("重要通知", "0831bf2c8f4876048a84ad0dc25034b7f03e012979fba8993ea196f512ee35e4");
    }
}
