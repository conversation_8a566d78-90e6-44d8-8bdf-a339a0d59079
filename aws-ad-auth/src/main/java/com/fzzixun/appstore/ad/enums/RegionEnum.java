package com.fzzixun.appstore.ad.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023/7/4
 */
public enum RegionEnum {
    NA("NA", "https://www.amazon.com/ap/oa"),
    EU("EU", "https://eu.account.amazon.com/ap/oa"),
    FE("FE", "https://apac.account.amazon.com/ap/oa");

    @Getter
    private final String region;
    @Getter
    private final String url;

    RegionEnum(String region, String url) {
        this.region = region;
        this.url = url;
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RegionEnum getRegionByName(String region) {
        if (region == null) return null;
        for (RegionEnum item : values()) {
            if (item.getRegion().equalsIgnoreCase(region)) return item;
        }
        throw new IllegalArgumentException(
                "enum region invalid"
        );
    }

    public static void main(String[] args) {
        System.out.println(RegionEnum.getRegionByName("na").getUrl());
        System.out.println(RegionEnum.getRegionByName("EU").getUrl());
        System.out.println(RegionEnum.getRegionByName("FE").getUrl());
    }
}
