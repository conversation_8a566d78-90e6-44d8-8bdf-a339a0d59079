package com.fzzixun.appstore.ad.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023/7/4
 */
public enum RegionToken {
    /**
     * 北美
     */
    NA("NA", "https://api.amazon.com/auth/o2/token"),
    /**
     * 欧洲
     */
    EU("EU", "https://api.amazon.co.uk/auth/o2/token"),
    /**
     * 远东
     */
    FE("FE", "https://api.amazon.co.jp/auth/o2/token");

    @Getter
    private final String authUrl;

    @Getter
    private final String region;

    RegionToken(String region, String authUrl) {
        this.authUrl = authUrl;
        this.region = region;
    }

    /**
     * 根据区域获取不同授权地址
     *
     * @param region
     * @return
     */
    public static String getAuthUrl(String region) {
        for (RegionToken item : values()) {
            if (item.getRegion().equalsIgnoreCase(region)) {
                return item.getAuthUrl();
            }
        }
        return RegionToken.NA.getAuthUrl();
    }
}
