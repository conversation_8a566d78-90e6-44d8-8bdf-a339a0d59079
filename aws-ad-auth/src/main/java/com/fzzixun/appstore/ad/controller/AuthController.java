package com.fzzixun.appstore.ad.controller;

import com.fzzixun.appstore.ad.request.AuthorizeUrlReq;
import com.fzzixun.appstore.ad.service.AdService;
import com.fzzixun.appstore.aliexpress.base.infrastructure.annotation.WebLog;
import com.fzzixun.appstore.aliexpress.vo.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@WebLog
@RequestMapping("/ad-auth/rest/v1/auth")
@RestController
@Api(tags = "广告授权")
@RequiredArgsConstructor
public class AuthController {

    private final AdService adService;

    @GetMapping("/url")
    @ApiOperation("获取授权地址")
    public ResponseVo<String> authUrl(AuthorizeUrlReq authorizeUrlReq, HttpServletRequest request) {
        return adService.getAuthUrl(authorizeUrlReq.getRegion(), authorizeUrlReq.getState(), null, request);
    }

    @GetMapping("/finish")
    @ApiOperation("接收code")
    public void receiveCode(@RequestParam(required = false) String code,
                            @RequestParam(required = false) String state,
                            @RequestParam(required = false) String scope,
                            HttpServletResponse response) throws IOException {
        log.info("接收到code: {},{},{}", code, state, scope);
        adService.createToken(code, state, scope, response);
    }

}
