package com.fzzixun.appstore.ad.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.ad.enums.AuthType;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.isvNotify.common.po.NotifyPo;
import com.fzzixun.appstore.isvNotify.common.po.NotifyWithUrlPo;
import com.fzzixun.appstore.isvNotify.common.service.NotifyMicroService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CallbackService {

    private final NotifyMicroService notifyMicroService;

    public void sendCallback(String appKey, String callbackUrl, String data, Integer messageType) {
        NotifyWithUrlPo notifyPo = new NotifyWithUrlPo();
        notifyPo.setAppKey(appKey);
        notifyPo.setCompanyId(0L);
        notifyPo.setData(data);
        notifyPo.setMessageType(messageType);
        notifyPo.setCallbackUrl(callbackUrl);
        if (StringUtils.isEmpty(notifyPo.getCallbackUrl())) {
            log.error("未配置回调地址, appKey:{}", appKey);
            return;
        }
        String msgType = messageType.equals(Constant.CALLBACK_MESSAGE_TYPE_ADS_TOKEN) ? "Token" : "Event";
        try {
            log.info("推送{}通知, url:{}, appKey:{}, {}", callbackUrl, msgType, appKey, JSON.toJSONString(notifyPo));
            notifyMicroService.sentNotifyWithCallbackUrl(notifyPo);
        } catch (FeignException.BadRequest badRequest) {
            log.error("推送{}通知失败, url:{}, appKey:{}, {}", callbackUrl, msgType, appKey, badRequest.getMessage());
        } catch (Exception e) {
            log.error("推送{}通知异常, url:{}, appKey:{},", callbackUrl, msgType, appKey, e);
        }
    }


    /**
     * @param appKey
     * @param data
     * @param authType
     * @return
     */
    public String sendCallback(String appKey, String data, AuthType authType) {
        log.info("推送通知！" + appKey + "," + authType + "," + data);
        try {
            NotifyPo notifyPo = new NotifyPo();
            notifyPo.setAppKey(appKey);
            notifyPo.setCompanyId(0L);
            //222:亚马逊spapi授权，221:亚马逊广告api授权
            notifyPo.setMessageType(AuthType.SPAPI.equals(authType) ? Constant.CALLBACK_MESSAGE_TYPE_SPAPI_TOKEN : Constant.CALLBACK_MESSAGE_TYPE_ADS_TOKEN);
            notifyPo.setData(data);
            String result = notifyMicroService.sentNotify(notifyPo);
            if (!"success".equals(result)) {
                log.error("推送通知失败！result:{} " + appKey + "," + authType + "," + data, result);
            }
            return result;
        } catch (Exception e) {
            log.error("推送通知异常error：" + appKey + "," + authType + "," + data, e);
        }
        return null;
    }
}
