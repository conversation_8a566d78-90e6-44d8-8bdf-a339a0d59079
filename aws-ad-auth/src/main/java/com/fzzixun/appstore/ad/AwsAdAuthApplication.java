package com.fzzixun.appstore.ad;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.fzzixun.appstore.aliexpress.AliExpressConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

@EnableAsync
@EnableApolloConfig
@Slf4j
@EnableSwagger2
@EnableCaching
@SpringBootApplication(scanBasePackages = {"com.fzzixun.appstore.common"},
        scanBasePackageClasses = {AwsAdAuthApplication.class, AliExpressConfig.class},
        exclude = DataSourceAutoConfiguration.class)

public class AwsAdAuthApplication {

    /**
     * 应用上下文1
     */
    private static ConfigurableApplicationContext ctx;

    /**
     * 打印文档地址
     */
    private static String getDocAddress() {
        try {
            String host = InetAddress.getLocalHost().getHostAddress();
            TomcatServletWebServerFactory tomcatServletWebServerFactory = (TomcatServletWebServerFactory) ctx
                    .getBean("tomcatServletWebServerFactory");
            int port = tomcatServletWebServerFactory.getPort();
            String contextPath = tomcatServletWebServerFactory.getContextPath();
            return "http://" + host + ":" + port + contextPath + "/doc.html";
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return "文档地址获取失败";
        }
    }

    /**
     * 启动方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        ctx = SpringApplication.run(AwsAdAuthApplication.class, args);
        log.info("系统启动成功, 接口文档: " + getDocAddress());
    }
}


