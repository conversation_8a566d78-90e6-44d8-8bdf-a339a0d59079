spring.profiles.active=local
app.id=aws-ad-auth
# 阿波罗配置
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=application
# 健康检查
management.endpoints.web.base-path=/status
management.endpoints.web.exposure.include=health
management.server.port=19001

logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} [%X{req.trace:-}] %clr(${LOG_LEVEL_PATTERN:%5p}) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n
