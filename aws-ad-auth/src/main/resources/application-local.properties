# 调试配置
server.port=8086
management.endpoints.web.exposure.include=*
swagger.enable=true
app.aws-ad-client-id=amzn1.application-oa2-client.c45a02bc6a8d4633876adef5789ce073
app.aws-ad-client-secret=9bb7030b1ee614948264fcac3b8edc34c64f331c5bb6c6fd774a120ebf8adcd1
ad.redirect.uri=https://test-sbappstoreapi.ziniao.com/ad-auth/rest/v1/auth/finish
auth.index=https://test-sbappstoreapi.ziniao.com/ad-auth

proxy.index=http://localhost:8087

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000