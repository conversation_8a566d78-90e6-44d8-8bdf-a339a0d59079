global:
  choose_project: true
  group_name: a1-02-appstore
  sonarsource: true
  branch_env:
    test: test
    pre: pre
    master: prod
  action:
    build: java
cicd:
  - dir_name: aws-ad-proxy
    enable: true
    language: java
    action:
      #      - build
      - publish
      - ingress
    publish:
      service_name: ad-proxy
      healthCheck: /status/health
      healthCheckPort: 19002
      replicas:
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /ad-proxy
            port: 80
  - dir_name: aws-ad-auth
    enable: true
    language: java
    action:
      - publish
      - ingress
    publish:
      service_name: ad-auth
      ports:
        - 80
      healthCheck: /status/health
      healthCheckPort: 19001
      replicas:
        test: 1
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /ad-auth
            port: 80
  - dir_name: ali-express-push
    enable: true
    language: java
    action:
      - publish
#      - ingress
    publish:
      service_name: ali-express-push
      ports:
        - 80
      healthCheck: /status/health
      healthCheckPort: 19003
      replicas:
        test: 1
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
  - dir_name: ali-express-auth
    enable: true
    language: java
    action:
      - publish
      - ingress
    publish:
      service_name: ali-express-auth
      ports:
        - 80
      healthCheck: /status/health
      healthCheckPort: 19001
      replicas:
        test: 1
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /ali-express-auth
            port: 80
  - dir_name: ali-express-forward
    enable: true
    language: java
    action:
      - publish
      - ingress
    publish:
      service_name: ali-express-forward
      ports:
        - 80
      healthCheck: /status/health
      healthCheckPort: 19002
      replicas:
        test: 1
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /ali-express-forward
            port: 80
  - dir_name: spapi-auth
    enable: true
    language: java
    action:
#      - build
      - publish
      - ingress
    publish:
      service_name: spapi-auth
      ports:
        - 80
#        - 9000
      healthCheck: /status/health
      healthCheckPort: 19001
      replicas:
        test: 1
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=test"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /spapi-auth
            port: 80

  - dir_name: spapi-proxy
    enable: true
    language: java
    action:
#      - build
      - publish
      - ingress
    publish:
      service_name: spapi-proxy
      healthCheck: /status/health
      healthCheckPort: 19002
      replicas:
        pre: 2
        prod: 2
      custom_jar_var:
        test: "-Dspring.profiles.active=online"
        pre: "-Dspring.profiles.active=online"
        prod: "-Dspring.profiles.active=online"
    ingress:
      - host: sbappstoreapi.ziniao.com
        paths:
          - path: /spapi-proxy
            port: 80