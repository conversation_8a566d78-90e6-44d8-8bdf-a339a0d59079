package com.zixun.sif.proxy.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.zixun.sif.proxy.dto.SifApiIpDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SifApiWhiteIpManager {

    @Getter
    private static final Map<String, SifApiIpDTO> whiteIpMap = new ConcurrentHashMap<>();
    private static final int API_TYPE = 4;
    private static final SifApiCompanyPropertyListener LISTENER;
    private static DataProperty<SifApiIpDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SifApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
        //本地测试
//        whiteIpMap.put("127.0.0.1", new SifApiIpDTO(0L, 0L, 4, "127.0.0.1", 1));
//        whiteIpMap.put("**************", new SifApiIpDTO(0L, 0L, 4, "127.0.0.1", 1));
    }

    public static void register2Property(DataProperty<SifApiIpDTO> property) {
        synchronized (LISTENER) {
            log.info("[SifApiWhiteIpManager] Registering new property to sif-api ip manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SifApiCompanyPropertyListener implements PropertyListener<SifApiIpDTO> {

        private String getKey(SifApiIpDTO value) {
            return value.getIp();
        }

        @Override
        public synchronized void configUpdate(SifApiIpDTO oldValue, SifApiIpDTO value) {
            // 更新数据
            log.info("[SifApiWhiteIpManager] received: {}", value);
            if (oldValue.getType() == API_TYPE) {
                whiteIpMap.remove(this.getKey(oldValue));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
            if (value.getType() == API_TYPE) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configLoad(SifApiIpDTO value) {
            // 加载数据
            log.info("[SifApiWhiteIpManager] loaded: {}", value);
            if (value.getType() == API_TYPE) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configDelete(SifApiIpDTO value) {
            // 删除数据
            log.info("[SifApiWhiteIpManager] deleted: {}", value);
            if (value.getType() == API_TYPE) {
                whiteIpMap.remove(this.getKey(value));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }
    }

}
