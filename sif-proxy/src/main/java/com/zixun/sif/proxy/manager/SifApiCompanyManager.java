package com.zixun.sif.proxy.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.zixun.sif.proxy.dto.SifApiCompanyDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SifApiCompanyManager {

    @Getter
    private static final Map<Long, SifApiCompanyDTO> companyMap = new ConcurrentHashMap<>();

    private static final SifApiCompanyPropertyListener LISTENER;
    private static DataProperty<SifApiCompanyDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SifApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
    }

    public static void register2Property(DataProperty<SifApiCompanyDTO> property) {
        synchronized (LISTENER) {
            log.info("[SifApiCompanyManager] Registering new property to sp-api company manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SifApiCompanyPropertyListener implements PropertyListener<SifApiCompanyDTO> {

        private Long getKey(SifApiCompanyDTO value) {
            return value.getCompanyId();
        }

        @Override
        public synchronized void configUpdate(SifApiCompanyDTO oldValue, SifApiCompanyDTO value) {
            // 更新数据
            log.info("[SifApiCompanyManager] received: {}", value);
            companyMap.put(this.getKey(value), value);
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }

        @Override
        public void configLoad(SifApiCompanyDTO value) {
            // 加载数据
            log.info("[SifApiCompanyManager] loaded: {}", value);
            companyMap.put(this.getKey(value), value);
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }

        @Override
        public void configDelete(SifApiCompanyDTO value) {
            // 删除数据
            log.info("[SifApiCompanyManager] deleted: {}", value);
            companyMap.remove(this.getKey(value));
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }
    }

}
