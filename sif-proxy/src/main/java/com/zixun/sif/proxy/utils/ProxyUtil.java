
package com.zixun.sif.proxy.utils;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;
import com.zixun.sif.proxy.config.SifApiConfig;
import com.zixun.sif.proxy.servlet.RequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;

@Slf4j
@Component
public class ProxyUtil {

    @Autowired
    private SifApiConfig sifApiConfig;

    public String getSifApiSettingInfo(String keyStr) throws Exception {
        return sifApiConfig.getSifApiSettingInfo(keyStr);
    }


    public RequestBuilder createOkHttpRequest(HttpServletRequest servletRequest, String region) throws Exception {

        String sifUrl = getSifApiSettingInfo("sifUrl");
        String method = servletRequest.getMethod();
        String requestURI = servletRequest.getRequestURI();
        //替换/sif-proxy/region 为空
        if (requestURI.startsWith("/sif-proxy/" + region)) {
            requestURI = requestURI.replace("/sif-proxy/" + region, "");
        }
        String url = sifUrl + requestURI;
        if (StringUtils.isNotBlank(region)) {
            url = url + "?country=" + region.toUpperCase();
        }
        //如果请求的queryString不为空，则拼接上去
        if (StringUtils.isNotBlank(servletRequest.getQueryString())) {
            url = url + "&" + servletRequest.getQueryString();
        }

        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {
            if (servletRequest.getHeader("content-type") != null &&
                    !servletRequest.getHeader("content-type").contains("x-www-form-urlencoded")) {
                ServletInputStream servletInputStream = servletRequest.getInputStream();
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = servletInputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String str = result.toString(StandardCharsets.UTF_8.name());
                // 使用 ObjectMapper 生成紧凑的 JSON 字符串
                ObjectMapper mapper = new ObjectMapper();
                String compactJsonStr = mapper.writeValueAsString(mapper.readValue(str, Map.class));
//
//                // 打印请求参数
                log.info("POST-proxy-url: {}，params: {}", url, compactJsonStr);
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), str);
                return new RequestBuilder((new Request.Builder()).url(url).method(method, requestBody), str);
            }
            return null;
        }
        if (method.equalsIgnoreCase("POST") || method.equalsIgnoreCase("PUT")) {
            RequestBody requestBody = RequestBody.create(null, new byte[0]);
            return new RequestBuilder((new Request.Builder()).url(url).method(method, requestBody), null);
        }
        log.info("GET-proxy-url: {}", url);
        return new RequestBuilder((new Request.Builder()).url(url).method(method, null), null);
    }


    public Request createHeader(Request.Builder requestBuilder, HttpServletRequest httpRequest, String token) throws Exception {
        Enumeration<String> enumerationOfHeaderNames = httpRequest.getHeaderNames();
        while (enumerationOfHeaderNames.hasMoreElements()) {
            String headerName = (enumerationOfHeaderNames.nextElement()).toLowerCase();
            if ("accept".equals(headerName) || "user-agent".equals(headerName) || "authorization".equals(headerName)) {
                if ("authorization".equals(headerName)) {
                    requestBuilder.addHeader(headerName, token);
                } else {
                    requestBuilder.addHeader(headerName, httpRequest.getHeader(headerName));
                }
                continue;
            }
            if ("content-type".equals(headerName)) {
                String[] values = httpRequest.getHeader(headerName).split(";");
                requestBuilder.addHeader("Content-Type", values[0]);
            }
        }
        return requestBuilder.build();
    }


    public Request.Builder createTokenOkHttpRequest(HttpServletRequest servletRequest) throws Exception {
        String url = getSifApiSettingInfo("sifUrl");

        String secretId = getSifApiSettingInfo("secretId");

        String requestURI = servletRequest.getRequestURI();

        if (requestURI.startsWith("/sif-proxy/api/user/token")) {
            requestURI = requestURI.replace("/sif-proxy", "");
            url = url + requestURI + "?secretid=" + secretId;
        }
        String method = servletRequest.getMethod();

        if (method.equalsIgnoreCase("POST")) {
            RequestBody requestBody = RequestBody.create(null, new byte[0]);
            return (new Request.Builder()).url(url).method(method, requestBody);
        }
        return (new Request.Builder()).url(url).method(method, null);
    }

    public Request.Builder createTokenOkHttpRequest() throws Exception {
        String url = getSifApiSettingInfo("sifUrl");
        String secretId = getSifApiSettingInfo("secretId");
        url = url + "/api/user/token?secretid=" + secretId;
        String method = "GET";
        return (new Request.Builder()).url(url).method(method, null);
    }
}
