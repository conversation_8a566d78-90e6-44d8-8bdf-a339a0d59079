package com.zixun.sif.proxy.init;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.BaseApolloDataSource;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.namespace.NamespaceApolloDataSource;
import com.zixun.sif.proxy.dto.SifApiCompanyDTO;
import com.zixun.sif.proxy.dto.SifApiIpDTO;
import com.zixun.sif.proxy.manager.SifApiCompanyManager;
import com.zixun.sif.proxy.manager.SifApiWhiteIpManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApolloDatasourceInit implements ApplicationRunner {

    @Value("${micro.framework.datasource.apollo.sp-api-company.namespace-name:a1-02-appstore.sp-api-company}")
    String sifApiCompanyNamespaceName;

    @Value("${micro.framework.datasource.apollo.sp-api-white-ip.namespace-name:a1-02-appstore.sp-api-white-ip}")
    String sifApiIpNamespaceName;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        BaseApolloDataSource<String, SifApiCompanyDTO> spApiCompanyApolloDataSource = new NamespaceApolloDataSource<>(sifApiCompanyNamespaceName,
                source -> JSON.parseObject(source, SifApiCompanyDTO.class));
        SifApiCompanyManager.register2Property(spApiCompanyApolloDataSource.getProperty());
        log.info("Apollo-SIF-API-Company数据初始化");
        spApiCompanyApolloDataSource.initData();

        BaseApolloDataSource<String, SifApiIpDTO> spApiIpApolloDataSource = new NamespaceApolloDataSource<>(sifApiIpNamespaceName,
                source -> JSON.parseObject(source, SifApiIpDTO.class));
        SifApiWhiteIpManager.register2Property(spApiIpApolloDataSource.getProperty());
        log.info("Apollo-SIF-API-White-IP数据初始化");
        spApiIpApolloDataSource.initData();

    }
}
