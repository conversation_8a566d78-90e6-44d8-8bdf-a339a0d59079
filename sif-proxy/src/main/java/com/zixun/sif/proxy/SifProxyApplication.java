package com.zixun.sif.proxy;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ServletComponentScan
@EnableApolloConfig
@EnableAsync
public class SifProxyApplication {
    public static void main(String[] args) {
        SpringApplication.run(SifProxyApplication.class, args);
    }
}
