package com.zixun.sif.proxy.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.squareup.okhttp.*;
import com.zixun.sif.proxy.dto.RequestRecord;
import com.zixun.sif.proxy.dto.SifApiCompanyDTO;
import com.zixun.sif.proxy.dto.SifApiIpDTO;
import com.zixun.sif.proxy.manager.SifApiCompanyManager;
import com.zixun.sif.proxy.manager.SifApiWhiteIpManager;
import com.zixun.sif.proxy.utils.IpUtil;
import com.zixun.sif.proxy.utils.ProxyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * sif代理转发服务
 */
@WebServlet("/*")
@Slf4j
public class ProxyServlet extends HttpServlet {


    @Value("${limitStatusCode:429}")
    private Integer limitStatusCode;

    @Value("${limitTime:10}")
    private Integer limitTime;

    @Value("${printSifResponseLog:false}")
    private Boolean printSifResponseLog;

    @Value("${logStore.name:sif-api-record}")
    private String logStore;

    @Value("${sif-api-key-config:}")
    private String sifApiKeyConfig;

    @Autowired
    private ProxyUtil proxyUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApiCounter apiCounter;

    @Autowired
    private RedisService redisService;


    //初始化枚举类 US/UK/DE/JP/CA/FR/ES/IT
    private static final String[] REGION_ARRAY = {"US", "UK", "DE", "JP", "CA", "FR", "ES", "IT"};

    private String parseSifApiKeyConfig(String apiKey) {
        try {
            // 校验 sifApiKeyConfig 是否为空
            if (StringUtils.isBlank(sifApiKeyConfig)) {
                log.warn("sif-api-key-config 配置为空");
                return null;
            }

            // 使用 JSON.parseObject 解析为 List<Map<String, String>>
            List<Map<String, String>> apiKeyConfigList = JSON.parseObject(sifApiKeyConfig, new TypeReference<List<Map<String, String>>>() {
            });
            log.info("解析 sif-api-key-config 成功: {}", apiKeyConfigList);

            // 遍历列表，查找匹配的 API_KEY
            for (Map<String, String> apiKeyConfig : apiKeyConfigList) {
                String configApiKey = apiKeyConfig.get("apiKey");
                if (StringUtils.equals(apiKey, configApiKey)) {
                    return configApiKey;
                }
            }

        } catch (Exception e) {
            log.error("解析 sif-api-key-config 失败", e);
        }
        return null;
    }


    /**
     * 服务器初始化
     */
    @Override
    public void init() {
        log.info("sif代理服务启动...");
    }

    @Override
    protected void service(HttpServletRequest servletRequest, HttpServletResponse servletResponse) {

        try {
            String uri = servletRequest.getRequestURI();
            String ipAddress = IpUtil.getIP(servletRequest);
            SifApiIpDTO sifApiIpDTO = SifApiWhiteIpManager.getWhiteIpMap().get(ipAddress);
            if (!"/routing.json".equals(uri) && !"/".equals(uri)) {
                log.info("URI:{},IP:{}", uri, ipAddress);
//                if (!StringUtils.contains(uri, "/api/user/token")) {
                if (StringUtils.isNotEmpty(ipAddress) && sifApiIpDTO == null) {
                    log.warn("该IP禁止访问: {}", ipAddress);
                    forbidden(servletResponse, "该IP禁止访问：" + ipAddress);
                    return;
                }
//                }
            }

            String[] uriInfo = uri.split("/");
            if (!"sif-proxy".equals(uriInfo[1])) {
                forbidden(servletResponse, "请求URI有误：" + uri);
                return;
            }
            if (!"/".equals(uri)) {
                String apiKey = servletRequest.getHeader("authorization");
                if (StringUtils.isBlank(apiKey)) {
                    forbidden(servletResponse, "API_KEY不能为空");
                    return;
                }

                //token 是否以 Bearer 开头
                if (!apiKey.startsWith("Bearer ")) {
                    forbidden(servletResponse, "API_KEY不正确");
                    return;
                }

                //截取 Bearer 后面的字符串token
                apiKey = apiKey.substring(7);
                if (StringUtils.isBlank(parseSifApiKeyConfig(apiKey))) {
                    forbidden(servletResponse, "API_KEY不匹配");
                    return;
                }

//                    String tokenMd5 = SecureUtil.md5(token);
                String region = uriInfo[2];
                //校验region值必须在 US/UK/DE/JP/CA/FR/ES/IT 这枚举里面（忽略大小写）
                if (!StringUtils.equalsAnyIgnoreCase(region, REGION_ARRAY)) {
                    forbidden(servletResponse, "站点有误：" + region);
                    return;
                }
                RequestBuilder requestBuilder = proxyUtil.createOkHttpRequest(servletRequest, region);
                Request request = proxyUtil.createHeader(requestBuilder.getReqBuilder(), servletRequest, getAccessToken(apiKey));
//                    String limitApi = request.method() + "-" + request.uri().getRawPath();
//                    //全局限制请求
//                    if (redisService.handle4xxLimit(tokenMd5, limitApi, servletResponse)) {
//                        return;
//                    }

//                    String statisticsKey = ipAddress + "_" + LocalDate.now().minusDays(1);
                long requestSize = servletRequest.getContentLengthLong() < 0 ? 0 : servletRequest.getContentLengthLong();
//                    long responseSize = 0;
//                    if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(statisticsKey, NUMBER_OF_CALLS_KEY))) {
//                        hashIncrBy(statisticsKey, NUMBER_OF_CALLS_KEY, 1);
//                        if (requestSize > 0) {
//                            hashIncrBy(statisticsKey, REQUEST_SIZE_KEY, requestSize);
//                        }
//                    } else {
//                        Map<String, Object> maps = new HashMap<>();
//                        maps.put(NUMBER_OF_CALLS_KEY, 1);
//                        maps.put(REQUEST_SIZE_KEY, requestSize);
//                        maps.put(RESPONSE_SIZE_KEY, responseSize);
//                        hPutAll(statisticsKey, maps);
//                    }
                OkHttpClient httpClient = new OkHttpClient();
                Call call = httpClient.newCall(request);
                Response response = call.execute();
                if (Boolean.TRUE.equals(printSifResponseLog)) {
                    log.info("[{}],返回码[{}]response=>{}", ipAddress, response.code(), response);
                }
                servletResponse.setStatus(response.code(), response.message());
                Headers headers = response.headers();
                for (String headerName : headers.names()) {
                    if (Boolean.TRUE.equals(printSifResponseLog)) {
                        log.info("addHeader->> name:{}，value:{}", headerName, headers.get(headerName));
                    }
                    if ("Content-Length".equals(headerName)) {
                        servletResponse.addHeader(headerName, String.valueOf(response.body().contentLength()));
                        continue;
                    }
                    if (!"Transfer-Encoding".equals(headerName)) {
                        servletResponse.addHeader(headerName, headers.get(headerName));
                    } else {
                        if (Boolean.TRUE.equals(printSifResponseLog)) {
                            log.info("no addHeader:{}", headerName);
                        }
                    }
                }
                ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
                byte[] responseByte = response.body().bytes().clone();
                if (Boolean.TRUE.equals(printSifResponseLog)) {
                    log.info("[{}]返回结果大小{} ===>>>{}", ipAddress, responseByte.length, new String(responseByte, StandardCharsets.UTF_8));
                }
                if (500 == response.code()) {
                    //响应500打印请求和响应
                    log.info("500内部错误req: [{}][{}]请求地址：{}, 请求体: {}", servletRequest.getMethod(), ipAddress, request.url(), requestBuilder.getRequestBody());
                    String result = new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", "");
                    log.info("500内部错误resp: [{}][{}],返回码[{}], response=>{}", servletRequest.getMethod(), ipAddress, response.code(), result);
                }
//                    log.info("响应大小：(body)" + responseByte.length + ",(header)" + length);
//                    hashIncrBy(statisticsKey, RESPONSE_SIZE_KEY, responseByte.length);
//                    redisService.calculate4xxLimit(response.code(), tokenMd5, limitApi);
                // 请求记录 ip、公司名、店铺id、url、日期、请求大小、响应大小、
                try {
                    String companyName = Optional.ofNullable(SifApiCompanyManager.getCompanyMap().get(sifApiIpDTO.getCompanyId()))
                            .map(SifApiCompanyDTO::getCompanyName).orElse("");
//                        String[] marketplaceIdArray;
//                        if (marketplaceIds == null) {
//                            marketplaceIdArray = new String[]{""};
//                        } else {
//                            marketplaceIdArray = marketplaceIds.split("[,，]");
//                        }
                    MDC.put("logStore", logStore);
                    int statusCode = response.code();
                    RequestRecord requestRecord = new RequestRecord(ipAddress, companyName, apiKey,
                            request.uri().getRawPath(), LocalDate.now(), requestSize, responseByte.length, statusCode);
                    // 500记录请求体和响应体
                    if (statusCode >= 500 && statusCode < 600) {
                        requestRecord.setRequestContent(requestBuilder.getRequestBody());
                        requestRecord.setResponseContent(new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", ""));
                    }
                    log.info(JSON.toJSONString(requestRecord));
                    MDC.remove("logStore");
                } catch (Exception e) {
                    log.error("记录请求记录失败", e);
                }
                servletOutputStream.write(responseByte);
            } else {
                writeResponse(servletResponse, 200, "{\"errors\": \"未找到相对应的SIF-API接口模块，请确认接口地址\"}");
            }
        } catch (Exception e) {
            log.error("代理请求异常！", e);
            try {
                forbidden(servletResponse, "request error");
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }


    private String getAccessToken(String apiKey) throws Exception {
        // 从 Redis 中获取 token
        String token = (String) redisTemplate.opsForValue().get(apiKey);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        log.error("未找到对应的 token，apiKey: {}，主动获取", apiKey);

        // 创建 HTTP 请求
        Request.Builder requestBuilder = proxyUtil.createTokenOkHttpRequest();
        OkHttpClient httpClient = new OkHttpClient();

        Response response = null;
        try {
            // 执行 HTTP 请求
            Call call = httpClient.newCall(requestBuilder.build());
            response = call.execute();

            // 读取响应体
            if (response.isSuccessful() && response.body() != null) {
                byte[] responseByte = response.body().bytes().clone();
                String result = new String(responseByte, StandardCharsets.UTF_8);
                log.info("AT返回结果: {}", result);

                // 解析 JSON 数据
                JSONObject jsonObject = JSON.parseObject(result);
                if (jsonObject != null && jsonObject.containsKey("data")) {
                    token = jsonObject.getString("data");

                    // 写入 Redis，设置过期时间（23小时）
                    if (StringUtils.isNotBlank(token)) {
                        redisTemplate.opsForValue().set(apiKey, token, 23, TimeUnit.HOURS);
                    }
                } else {
                    log.error("解析失败，返回结果中缺少 data 字段，apiKey: {}", apiKey);
                }
            } else {
                log.error("HTTP 请求失败，状态码: {}, apiKey: {}", response != null ? response.code() : "未知", apiKey);
            }
        } catch (Exception e) {
            log.error("获取 token 失败，apiKey: {}，异常信息: {}", apiKey, e.getMessage(), e);
            throw e; // 抛出异常以便调用方处理
        } finally {
            if (response != null && response.body() != null) {
                response.body().close(); // 确保资源释放
            }
        }

        return token;
    }


    private Long hashIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    private void forbidden(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 403, "{\"errors\": \"" + msg + "\"}");
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }


    private void limitByStatusCode(String key, int code, URL url) {
        if (code != limitStatusCode) {
            return;
        }
        try {
            //返回码为limitByStatusCode，一定时间(s)内限制调用
            redisTemplate.opsForValue().set(key, String.valueOf(limitStatusCode), limitTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("limitByStatusCode Exception！", e);
        }
    }


}
