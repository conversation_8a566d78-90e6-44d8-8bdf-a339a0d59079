package com.zixun.sif.proxy.config;

import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/5/7
 */
@Setter
@Configuration
@ConfigurationProperties(prefix = "sif-api.setting")
public class SifApiConfig {
    private Map<String, String> map;

    public String getSifApiSettingInfo(String keyStr) {
        return map.get(keyStr);
    }

}
