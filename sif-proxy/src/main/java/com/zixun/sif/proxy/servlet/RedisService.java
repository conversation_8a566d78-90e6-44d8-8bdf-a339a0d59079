package com.zixun.sif.proxy.servlet;

import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 存放限流token的key
    private static final String SIFAPI_REQ_LIMIT_KEY = "sifapi:req:4xx-limit:list";
    private static final String SIFAPI_REQ_LIMIT_400_LIST_KEY = "sifapi:req:400-limit:list";
    private static final String SIFAPI_REQ_LIMIT_403_LIST_KEY = "sifapi:req:403-limit:list";
    private static final String SIFAPI_REQ_LIMIT_404_LIST_KEY = "sifapi:req:404-limit:list";
    private static final String SIFAPI_REQ_LIMIT_429_LIST_KEY = "sifapi:req:429-limit:list";
    private static final String SIFAPI_REQ_LIMIT_429_KEY = "sifapi:req:429-limit";

    // 4xx限流开关
    @Value("${sifapi.req.4xx-limit.enable:true}")
    private Boolean req4xxLimitEnable;

    // 最小请求计数阈值
    @Value("${sifapi.req.4xx-limit.min-count:1}")
    private Integer minRequestCount;
    // 统计的时间 10秒
    @Value("${sifapi.req.4xx-limit.stats-time:10}")
    private Integer statsTime;
    // 错误比率限流
    @Value("${sifapi.req.4xx-limit.error-rate-limit.enable:false}")
    private Boolean errorRateLimitEnable ;
    // 错误比率阈值
    @Value("${sifapi.req.4xx-limit.error-rate-limit.threshold:0.3}")
    private Double errorRateThreshold ;
    // 错误次数限流
    @Value("${sifapi.req.4xx-limit.error-count-limit.enable:true}")
    private Boolean errorCountLimitEnable ;
    // 错误次数阈值
    @Value("${sifapi.req.4xx-limit..error-count-limit.threshold:10}")
    private Integer errorCountThreshold;
    // 限流时间（秒
    @Value("${sifapi.req.4xx-limit.limit-time-seconds:10}")
    private Integer limitTimeSeconds;

    //==================================================================================

    // 统计的时间 秒
    @Value("${sifapi.req.400-limit.stats-time:60}")
    private Integer stats400Time;
    @Value("${sifapi.req.400-limit.second-stats-time:600}")
    private Integer stats400SecondTime;

    @Value("${sifapi.req.403-limit.stats-time:60}")
    private Integer stats403Time;
    @Value("${sifapi.req.403-limit.second-stats-time:600}")
    private Integer stats403SecondTime;

    @Value("${sifapi.req.404-limit.stats-time:60}")
    private Integer stats404Time;
    @Value("${sifapi.req.404-limit.second-stats-time:600}")
    private Integer stats404SecondTime;

    @Value("${sifapi.req.429-limit.stats-time:60}")
    private Integer stats429Time;
    @Value("${sifapi.req.429-limit.second-stats-time:120}")
    private Integer stats429SecondTime;
    @Value("${sifapi.req.429-limit.third-stats-time:180}")
    private Integer stats429ThirdTime;

    // 错误次数阈值
    @Value("${sifapi.req.400-limit.error-count-limit.threshold:3}")
    private Integer errorCount400Threshold;
    @Value("${sifapi.req.400-limit.error-count-limit.second-threshold:4}")
    private Integer errorCount400SecondThreshold;

    @Value("${sifapi.req.403-limit.error-count-limit.threshold:2}")
    private Integer errorCount403Threshold;
    @Value("${sifapi.req.403-limit.error-count-limit.second-threshold:4}")
    private Integer errorCount403SecondThreshold;

    @Value("${sifapi.req.404-limit.error-count-limit.threshold:3}")
    private Integer errorCount404Threshold;
    @Value("${sifapi.req.404-limit.error-count-limit.second-threshold:4}")
    private Integer errorCount404SecondThreshold;

    @Value("${sifapi.req.429-limit.error-count-limit.threshold:1}")
    private Integer errorCount429Threshold;
    @Value("${sifapi.req.429-limit.error-count-limit.second-threshold:2}")
    private Integer errorCount429SecondThreshold;
    @Value("${sifapi.req.429-limit.error-count-limit.third-threshold:4}")
    private Integer errorCount429ThirdThreshold;


    // 限流时间（秒
    @Value("${sifapi.req.400-limit.limit-time-seconds:300}")
    private Integer limit400TimeSeconds;
    @Value("${sifapi.req.400-limit.second-limit-time-seconds:1200}")
    private Integer limit400SecondTimeSeconds;

    @Value("${sifapi.req.403-limit.limit-time-seconds:300}")
    private Integer limit403TimeSeconds;
    @Value("${sifapi.req.403-limit.second-limit-time-seconds:1800}")
    private Integer limit403SecondTimeSeconds;

    @Value("${sifapi.req.404-limit.limit-time-seconds:300}")
    private Integer limit404TimeSeconds;
    @Value("${sifapi.req.404-limit.second-limit-time-seconds:1800}")
    private Integer limit404SecondTimeSeconds;

    @Value("${sifapi.req.429-limit.limit-time-seconds:15}")
    private Integer limit429TimeSeconds;
    @Value("${sifapi.req.429-limit.second-limit-time-seconds:30}")
    private Integer limit429SecondTimeSeconds;
    @Value("${sifapi.req.429-limit.third-limit-time-seconds:120}")
    private Integer limit429ThirdTimeSeconds;

    // ==========================================================================

    @Value("${limitReturnCode:403}")
    private Integer limitReturnCode;


    public boolean shouldLimit(String tokenMd5, String api) {
        if (!req4xxLimitEnable) {
            return false;
        }
        String limitKey = tokenMd5 + "-" + api;
        try {
            long now = Instant.now().toEpochMilli();
            Double limitEndTime = redisTemplate.opsForZSet().score(SIFAPI_REQ_LIMIT_KEY, limitKey);
            if (limitEndTime == null) {
                return false;
            }
            if (limitEndTime > now) {
                return true;
            } else {
                redisTemplate.opsForZSet().remove(SIFAPI_REQ_LIMIT_KEY, limitKey);
//            System.out.println("移除限流");
                return false;
            }
        } catch (Exception e) {
            log.error("[{}]判断是否限流异常:{}", limitKey, e.getMessage(), e);
            return false;
        }
    }
    public void calculateLimit(String tokenMd5, String api, boolean isError) {
        if (!req4xxLimitEnable) {
            return;
        }
        String limitKey = tokenMd5 + "-" + api;
        try {
            long now = Instant.now().toEpochMilli();
            String countKey = "sifapi:token:req_count:" + tokenMd5 + ":" + api;
            String errorKey = "sifapi:token:req_errors:" + tokenMd5 + ":" + api;
            String uuid = UUID.randomUUID().toString();
            String value = now + ":" + uuid;
            // 记录当前请求
            redisTemplate.opsForZSet().add(countKey, value, now);
            redisTemplate.expire(countKey, statsTime, TimeUnit.SECONDS);
            if (isError) {
                redisTemplate.opsForZSet().add(errorKey, value, now);
                redisTemplate.expire(errorKey, statsTime, TimeUnit.SECONDS);
            }
            // 删除STATS_TIME秒前的请求
            long statsRetainTime = now - (statsTime * 1000);
            redisTemplate.opsForZSet().removeRangeByScore(countKey, 0, statsRetainTime);
            redisTemplate.opsForZSet().removeRangeByScore(errorKey, 0, statsRetainTime);

            // 计算STATS_TIME秒内的错误请求数和总请求数
            Long totalCount = redisTemplate.opsForZSet().count(countKey, statsRetainTime, now);
            Long errorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainTime, now);
            // 如果总请求次数小于最小请求计数阈值，不计算错误率，不执行限制
            if (errorCount == null || totalCount == null || totalCount < minRequestCount) {
//            System.out.println(errorCount + " -- " + totalCount + " -- " + errorCount / totalCount + " -- " + "false不限制");
                return;
            }
            if (errorRateLimitEnable) {
                // 计算错误率
                double errorRate = (double) errorCount / totalCount;
                boolean errorRateLimit = errorRate > errorRateThreshold;
//        System.out.println(errorCount + " -- " + totalCount + " -- " + errorCount / totalCount + " -- " + errorRate);
                if (errorRateLimit) {
                    long limitEndTime = now + limitTimeSeconds * 1000;
                    redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_KEY, limitKey, limitEndTime);
                    log.info("[{}]触发4xx限流, 错误率:{}, errorCount:{}, totalCount:{}", limitKey, errorRate, errorCount, totalCount);
                }
            }
            if (errorCountLimitEnable) {
                if (errorCount > errorCountThreshold) {
                    long limitEndTime = now + limitTimeSeconds * 1000;
                    redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_KEY, limitKey, limitEndTime);
                    log.info("[{}]触发4xx限流, 错误次数:{}", limitKey, errorCount);
                }
            }
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", limitKey, e.getMessage(), e);
        }
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }


    // 400 请求异常：按一个店铺+接口，如果60s内400错误次数超过3次，则这个店铺+这个接口就休眠300s。 如果10分钟内出现超过4次，则禁用20分钟。（返回值改为450，message：400,请求异常,请优化您的参数）。
    // 403 没有权限：按店铺维度，如果一个店铺在60s内出现超过2次403，则禁用这个店铺所有的接口5分钟。 如果10分钟内出现超过4次，则禁用30分钟。（返回值改为453，message：403,店铺未授权,请先授权）
    // 404 资源不存在：按一个店铺+接口，如果60s内404错误次数超过3次，则这个店铺+这个接口就休眠300s。 如果10分钟内出现超过4次，则禁用30分钟。（返回值改为454，message：请优化您的参数）
    // 429 限流：按店铺+接口，60s内：出现第一次限流15s。 如果2分钟内出现超过2次，则禁用30s。 如果3分钟内出现超过4次，则禁用2分钟。（返回值改为459，message：429限流中，请降低频率）
    public boolean handle4xxLimit(String tokenMd5, String api, HttpServletResponse servletResponse) {
        if (!req4xxLimitEnable) {
            return false;
        }
        long now = Instant.now().toEpochMilli();
        try {
            if (handle4xxLimit(now, SIFAPI_REQ_LIMIT_400_LIST_KEY, SecureUtil.md5(tokenMd5 + "-" + api))) {
                log.info("[{}]触发400限流, 调用地址:{}", tokenMd5, api);
                writeResponse(servletResponse, 450, "{\"errors\":[{\"message\":\"400，请求异常，请优化您的参数\"}]}");
                return true;
            } else if (handle4xxLimit(now, SIFAPI_REQ_LIMIT_403_LIST_KEY, tokenMd5)) {
                log.info("[{}]触发403限流, 调用地址:{}", tokenMd5, api);
                writeResponse(servletResponse, 453, "{\"errors\":[{\"message\":\"403，店铺未授权，请先授权\"}]}");
                return true;
            } else if (handle4xxLimit(now, SIFAPI_REQ_LIMIT_404_LIST_KEY, SecureUtil.md5(tokenMd5 + "-" + api))) {
                log.info("[{}]触发404限流, 调用地址:{}", tokenMd5, api);
                writeResponse(servletResponse, 454, "{\"errors\":[{\"message\":\"404，资源不存在，请优化您的参数\"}]}");
                return true;
            } else if (handle4xxLimit(now, SIFAPI_REQ_LIMIT_429_LIST_KEY, SecureUtil.md5(tokenMd5 + "-" + api))) {
                log.info("[{}]触发429限流, 调用地址:{}", tokenMd5, api);
                writeResponse(servletResponse, 459, "{\"errors\":[{\"message\":\"429限流中，请降低频率\"}]}");
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("[{}]判断是否限流异常, api:{}, message:{}", tokenMd5, api, e.getMessage(), e);
            return false;
        }
    }

    private boolean handle4xxLimit(long now, String limitZsetKey, String limitKey) {
        try {
            Double limitEndTime = redisTemplate.opsForZSet().score(limitZsetKey, limitKey);
            if (limitEndTime == null) {
                return false;
            }
            if (limitEndTime > now) {
                return true;
            } else {
                redisTemplate.opsForZSet().remove(limitZsetKey, limitKey);
//            System.out.println("移除限流");
                return false;
            }
        } catch (Exception e) {
            log.error("[{}]判断是否限流异常, limitKey:{}, message:{}", limitZsetKey, limitKey, e.getMessage(), e);
            return false;
        }
    }
    public void calculate4xxLimit(int statusCode, String tokenMd5, String api) {
        if (statusCode < 400 || statusCode >= 500) {
            return;
        }
        long now = Instant.now().toEpochMilli();
        if (statusCode == 400) {
            calculate400Limit(now, tokenMd5, api);
        } else if (statusCode == 403) {
            calculate403Limit(now, tokenMd5);
        } else if (statusCode == 404) {
            calculate404Limit(now, tokenMd5, api);
        } else if (statusCode == 429) {
            calculate429Limit(now, tokenMd5, api);
        } else {
            log.info("未处理4xx响应:{}", statusCode);
        }
    }

    // 400 请求异常：按一个店铺+接口，如果60s内400错误次数超过3次，则这个店铺+这个接口就休眠300s。
    // 如果10分钟内出现超过4次，则禁用20分钟。（返回值改为450，message：400,请求异常,请优化您的参数）。
    private void calculate400Limit(long now, String tokenMd5, String api) {
        String limitKey = tokenMd5 + "-" + api;
        String limitKeyMd5 = SecureUtil.md5(limitKey);

        String uuid = UUID.randomUUID().toString();
        String value = now + ":" + uuid;
        String errorKey = "sifapi:token:req_400_errors:" + limitKeyMd5;
        try {
            redisTemplate.opsForZSet().add(errorKey, value, now);
            redisTemplate.expire(errorKey, stats400SecondTime, TimeUnit.SECONDS);
            // 删除第二次STATS_TIME秒前的请求
            long statsRetainSecondTime = now - (stats400SecondTime * 1000);
            redisTemplate.opsForZSet().removeRangeByScore(errorKey, 0, statsRetainSecondTime);

            // 计算第二次STATS_TIME秒内的错误请求数
            Long secondErrorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainSecondTime, now);
            if (secondErrorCount != null && errorCountLimitEnable && secondErrorCount > errorCount400SecondThreshold) {
                long limitEndTime = now + limit400SecondTimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_400_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发400限流{}秒, {}秒错误次数:{}", limitKey, limit400SecondTimeSeconds, stats400SecondTime, secondErrorCount);
                return;
            }
            // 计算STATS_TIME秒内的错误请求数
            long statsRetainTime = now - (stats400Time * 1000);
            Long errorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainTime, now);
            if (errorCount != null && errorCountLimitEnable && errorCount > errorCount400Threshold) {
                long limitEndTime = now + limit400TimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_400_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发400限流{}秒, {}秒错误次数:{}", limitKey, limit400TimeSeconds, stats400Time, errorCount);
                return;
            }
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", limitKey, e.getMessage(), e);
        }

    }

    // 403 没有权限：按店铺维度，如果一个店铺在60s内出现超过2次403，则禁用这个店铺所有的接口5分钟。
    // 如果10分钟内出现超过4次，则禁用30分钟。（返回值改为453，message：403,店铺未授权,请先授权）
    private void calculate403Limit(long now, String tokenMd5) {
        String uuid = UUID.randomUUID().toString();
        String value = now + ":" + uuid;
        String errorKey = "sifapi:token:req_403_errors:" + tokenMd5;
        try {
            redisTemplate.opsForZSet().add(errorKey, value, now);
            redisTemplate.expire(errorKey, stats403SecondTime, TimeUnit.SECONDS);
            // 删除第二次STATS_TIME秒前的请求
            long statsRetainSecondTime = now - (stats403SecondTime * 1000);
            redisTemplate.opsForZSet().removeRangeByScore(errorKey, 0, statsRetainSecondTime);

            // 计算第二次STATS_TIME秒内的错误请求数
            Long secondErrorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainSecondTime, now);
            if (secondErrorCount != null && errorCountLimitEnable && secondErrorCount > errorCount403SecondThreshold) {
                long limitEndTime = now + limit403SecondTimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_403_LIST_KEY, tokenMd5, limitEndTime);
                log.info("[{}]计算触发403限流{}秒, {}秒错误次数:{}", tokenMd5, limit403SecondTimeSeconds, stats403SecondTime, secondErrorCount);
                return;
            }
            // 计算STATS_TIME秒内的错误请求数
            long statsRetainTime = now - (stats403Time * 1000);
            Long errorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainTime, now);
            if (errorCount != null && errorCountLimitEnable && errorCount > errorCount403Threshold) {
                long limitEndTime = now + limit403TimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_403_LIST_KEY, tokenMd5, limitEndTime);
                log.info("[{}]计算触发403限流{}秒, {}秒错误次数:{}", tokenMd5, limit403TimeSeconds, stats403Time, errorCount);
                return;
            }
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", tokenMd5, e.getMessage(), e);
        }
    }

    // 404 资源不存在：按一个店铺+接口，如果60s内404错误次数超过3次，则这个店铺+这个接口就休眠300s。
    // 如果10分钟内出现超过4次，则禁用30分钟。（返回值改为454，message：请优化您的参数）
    private void calculate404Limit(long now, String tokenMd5, String api) {
        String limitKey = tokenMd5 + "-" + api;
        String limitKeyMd5 = SecureUtil.md5(limitKey);

        String uuid = UUID.randomUUID().toString();
        String value = now + ":" + uuid;
        String errorKey = "sifapi:token:req_404_errors:" + limitKeyMd5;
        try {
            redisTemplate.opsForZSet().add(errorKey, value, now);
            redisTemplate.expire(errorKey, stats404SecondTime, TimeUnit.SECONDS);
            // 删除第二次STATS_TIME秒前的请求
            long statsRetainSecondTime = now - (stats404SecondTime * 1000);
            redisTemplate.opsForZSet().removeRangeByScore(errorKey, 0, statsRetainSecondTime);

            // 计算第二次STATS_TIME秒内的错误请求数
            Long secondErrorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainSecondTime, now);
            if (secondErrorCount != null && errorCountLimitEnable && secondErrorCount > errorCount404SecondThreshold) {
                long limitEndTime = now + limit404SecondTimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_404_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发404限流{}秒, {}秒错误次数:{}", limitKey, limit404SecondTimeSeconds, stats404SecondTime, secondErrorCount);
                return;
            }
            // 计算STATS_TIME秒内的错误请求数
            long statsRetainTime = now - (stats404Time * 1000);
            Long errorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainTime, now);
            if (errorCount != null && errorCountLimitEnable && errorCount > errorCount404Threshold) {
                long limitEndTime = now + limit404TimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_404_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发404限流{}秒, {}秒错误次数:{}", limitKey, limit404TimeSeconds, stats404Time, errorCount);
                return;
            }
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", limitKey, e.getMessage(), e);
        }
    }

    // 429 限流：按店铺+接口，60s内：出现第一次限流15s。
    // 如果2分钟内出现超过2次，则禁用30s。
    // 如果3分钟内出现超过4次，则禁用2分钟。（返回值改为459，message：429限流中，请降低频率）
    private void calculate429Limit(long now, String tokenMd5, String api) {
        String limitKey = tokenMd5 + "-" + api;
        String limitKeyMd5 = SecureUtil.md5(limitKey);

        String uuid = UUID.randomUUID().toString();
        String value = now + ":" + uuid;
        String errorKey = "sifapi:token:req_429_errors:" + limitKeyMd5;
        try {
            redisTemplate.opsForZSet().add(errorKey, value, now);
            redisTemplate.expire(errorKey, stats429ThirdTime, TimeUnit.SECONDS);
            // 删除第三次STATS_TIME秒前的请求
            long statsRetainThirdTime = now - (stats429ThirdTime * 1000);
            redisTemplate.opsForZSet().removeRangeByScore(errorKey, 0, statsRetainThirdTime);

            // 计算第三次STATS_TIME秒内的错误请求数
            Long thirdErrorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainThirdTime, now);
            if (thirdErrorCount != null && errorCountLimitEnable && thirdErrorCount > errorCount429ThirdThreshold) {
                long limitEndTime = now + limit429ThirdTimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_429_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发429限流{}秒, {}秒错误次数:{}", limitKey, limit429ThirdTimeSeconds, stats429ThirdTime, thirdErrorCount);
                return;
            }
            // 计算第二次STATS_TIME秒内的错误请求数
            long statsRetainSecondTime = now - (stats429SecondTime * 1000);
            Long secondErrorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainSecondTime, now);
            if (secondErrorCount != null && errorCountLimitEnable && secondErrorCount > errorCount429SecondThreshold) {
                long limitEndTime = now + limit429SecondTimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_429_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发429限流{}秒, {}秒错误次数:{}", limitKey, limit429SecondTimeSeconds, stats429SecondTime, secondErrorCount);
                return;
            }
            // 计算STATS_TIME秒内的错误请求数
            long statsRetainTime = now - (stats429Time * 1000);
            Long errorCount = redisTemplate.opsForZSet().count(errorKey, statsRetainTime, now);
            if (errorCount != null && errorCountLimitEnable && errorCount >= errorCount429Threshold) { // 出现一次429，就限流
                long limitEndTime = now + limit429TimeSeconds * 1000;
                redisTemplate.opsForZSet().add(SIFAPI_REQ_LIMIT_429_LIST_KEY, limitKeyMd5, limitEndTime);
                log.info("[{}]计算触发429限流{}秒, {}秒错误次数:{}", limitKey, limit429TimeSeconds, stats429Time, errorCount);
                return;
            }
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", limitKey, e.getMessage(), e);
        }
    }
    private void calculate429Limit(String tokenMd5, String api) {
        try {
            String limitKey = String.join(":", SIFAPI_REQ_LIMIT_429_KEY, tokenMd5, SecureUtil.md5(api));
            //返回码为429，一定时间(s)内限制调用
            redisTemplate.opsForValue().set(limitKey, 429, limit429TimeSeconds, TimeUnit.SECONDS);
            log.info("[{}]计算触发429限流", tokenMd5 + "-" + api);
        } catch (Exception e) {
            log.error("[{}]计算是否限流异常:{}", tokenMd5 + "-" + api, e.getMessage(), e);
        }
    }
}
