server.port=10089
apollo.bootstrap.enabled=false
apollo.meta=http://localhost:8080
management.server.port=19002
logging.level.root=info
logging.level.com.zixun.sif.proxy=debug

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM

#spring.datasource.url=*************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=123456

sif-api.setting.map.secretId = 0VFLUOO1pex9GErTq5svH8Iiq7892X
sif-api.setting.map.sifUrl = https://www.sif.com

sif-api-key-config=[{"company":"润步","apiKey":"7b75f539d74a4ec0971f2cffd23da0eb"}]

testIpList=127.0.0.1,*************

limitStatusCode=429