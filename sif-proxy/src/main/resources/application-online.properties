# 调试配置
server.port=80
# 配置中心
apollo.bootstrap.enabled=true
logging.level.root=info
logging.level.com.zixun.sif.proxy=info

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=${APPSTORE_REDIS_PROXY_URL}
# Redis服务器连接端口
spring.redis.port=${APPSTORE_REDIS_PROXY_PORT}
# Redis服务器连接密码（默认为空）
spring.redis.password=${APPSTORE_REDIS_PROXY_PASSWORD}
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

# db
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=${APPSTORE_MYSQL_SOP_USERNAME}
spring.datasource.password=${APPSTORE_MYSQL_SOP_PASSWORD}
spring.datasource.url=jdbc:mysql://${APPSTORE_MYSQL_SOP_URL}:\
  ${APPSTORE_MYSQL_SOP_PORT}/\
  ${APPSTORE_MYSQL_SOP_DBNAME}?\
  useSSL=false&\
  useUnicode=true&\
  characterEncoding=utf-8&\
  zeroDateTimeBehavior=convertToNull&\
  serverTimezone=Asia/Shanghai
spring.datasource.hikari.minimum-idle = 1
spring.datasource.hikari.maximum-pool-size = 50
spring.datasource.hikari.idle-timeout = 600000
spring.datasource.hikari.max-lifetime = 1800000
spring.datasource.hikari.connection-timeout = 60000