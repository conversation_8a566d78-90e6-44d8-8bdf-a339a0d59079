package com.fzzixun.appstore.ad.component;

import com.fzzixun.appstore.common.StoreProxyComponent;
import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * IP代理信息
 */
@Component
@Data
@ConditionalOnProperty(name = "enable.refreshAgent", havingValue = "true")
public class IpAgent {

    private final StoreProxyComponent storeProxyComponent;

    public IpAgent(StoreProxyComponent storeProxyComponent) {
        this.storeProxyComponent = storeProxyComponent;
    }

    /**
     * 每隔5分钟执行一次
     */
    @Scheduled(cron = "${refreshAgent.cron:0 */5 * * * ?}")
//    @Scheduled(cron = "*/5 * * * * ?")
    public void scheduleRefreshAgentTask() {
        storeProxyComponent.storeProxy(null, null);
    }
}
