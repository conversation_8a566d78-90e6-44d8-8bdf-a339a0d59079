package com.fzzixun.appstore.ad.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023/7/4
 */
public enum AdsUrl {
    /**
     * 北美
     */
    NA("NA", "https://advertising-api.amazon.com", "advertising-api.amazon.com", "https://api.amazon.com/auth/o2/token"),
    /**
     * 欧洲
     */
    EU("EU", "https://advertising-api-eu.amazon.com", "advertising-api-eu.amazon.com", "https://api.amazon.co.uk/auth/o2/token"),
    /**
     * 远东
     */
    FE("FE", "https://advertising-api-fe.amazon.com", "advertising-api-fe.amazon.com", "https://api.amazon.co.jp/auth/o2/token");

    @Getter
    private final String url;

    @Getter
    private final String region;

    @Getter
    private final String host;

    @Getter
    private final String tokenUrl;

    AdsUrl(String region, String url, String host, String tokenUrl) {
        this.url = url;
        this.region = region;
        this.host = host;
        this.tokenUrl = tokenUrl;
    }

    /**
     * 根据区域获取不同Ads地址
     *
     * @param region
     * @return
     */
    public static String getAdsUrl(String region) {
        for (AdsUrl item : values()) {
            if (item.getRegion().equalsIgnoreCase(region)) {
                return item.getUrl();
            }
        }
        return AdsUrl.NA.getUrl();
    }

    public static String getAdsHost(String region) {
        for (AdsUrl item : values()) {
            if (item.getRegion().equalsIgnoreCase(region)) {
                return item.getHost();
            }
        }
        return AdsUrl.NA.getHost();
    }

    public static String getTokenUrl(String region) {
        for (AdsUrl item : values()) {
            if (item.getRegion().equalsIgnoreCase(region)) {
                return item.getTokenUrl();
            }
        }
        return AdsUrl.NA.getTokenUrl();
    }

}
