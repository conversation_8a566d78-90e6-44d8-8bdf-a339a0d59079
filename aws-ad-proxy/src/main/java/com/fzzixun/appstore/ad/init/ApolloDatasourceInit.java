package com.fzzixun.appstore.ad.init;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.ad.dto.SpApiCompanyDTO;
import com.fzzixun.appstore.ad.dto.SpApiIpDTO;
import com.fzzixun.appstore.ad.manager.SpApiCompanyManager;
import com.fzzixun.appstore.ad.manager.SpApiWhiteIpManager;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.BaseApolloDataSource;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.namespace.NamespaceApolloDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApolloDatasourceInit implements ApplicationRunner {

    @Value("${micro.framework.datasource.apollo.sp-api-company.namespace-name:a1-02-appstore.sp-api-company}")
    String spApiCompanyNamespaceName;

    @Value("${micro.framework.datasource.apollo.sp-api-white-ip.namespace-name:a1-02-appstore.sp-api-white-ip}")
    String spApiIpNamespaceName;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        BaseApolloDataSource<String, SpApiCompanyDTO> spApiCompanyApolloDataSource = new NamespaceApolloDataSource<>(spApiCompanyNamespaceName,
                source -> JSON.parseObject(source, SpApiCompanyDTO.class));
        SpApiCompanyManager.register2Property(spApiCompanyApolloDataSource.getProperty());
        log.info("Apollo-SP-API-Company数据初始化");
        spApiCompanyApolloDataSource.initData();

        BaseApolloDataSource<String, SpApiIpDTO> spApiIpApolloDataSource = new NamespaceApolloDataSource<>(spApiIpNamespaceName,
                source -> JSON.parseObject(source, SpApiIpDTO.class));
        SpApiWhiteIpManager.register2Property(spApiIpApolloDataSource.getProperty());
        log.info("Apollo-SP-API-White-IP数据初始化");
        spApiIpApolloDataSource.initData();

    }
}
