package com.fzzixun.appstore.ad.servlet;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.ad.dto.RequestRecord;
import com.fzzixun.appstore.ad.dto.SpApiCompanyDTO;
import com.fzzixun.appstore.ad.dto.SpApiIpDTO;
import com.fzzixun.appstore.ad.enums.AdsUrl;
import com.fzzixun.appstore.ad.manager.SpApiCompanyManager;
import com.fzzixun.appstore.ad.manager.SpApiWhiteIpManager;
import com.fzzixun.appstore.ad.utils.IpUtil;
import com.fzzixun.appstore.common.StoreProxyComponent;
import com.squareup.okhttp.FormEncodingBuilder;
import com.squareup.okhttp.Headers;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;
import com.squareup.okhttp.Response;
import com.squareup.okhttp.ResponseBody;
import lombok.extern.slf4j.Slf4j;
import okio.ByteString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.Authenticator;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * AWS-AD代理转发服务
 */
@WebServlet("/*")
@Slf4j
public class ProxyServlet extends HttpServlet {

    @Value("${app.aws-ad-client-id:amzn1.application-oa2-client.c45a02bc6a8d4633876adef5789ce073}")
    private String clientId;

    @Value("${app.aws-ad-client-secret:9bb7030b1ee614948264fcac3b8edc34c64f331c5bb6c6fd774a120ebf8adcd1}")
    private String clientSecret;

    @Value("${logStore.ad-proxy.name:ad-record}")
    private String logStore;

    @Value("${param.okhttp.read-timeout:30}")
    private Integer okhttpReadTimeout;

    @Value("${param.okhttp.connect-timeout:10}")
    private Integer okhttpConnectTimeout;

    @Value("${param.okhttp.write-timeout:30}")
    private Integer okhttpWriteTimeout;

    public static final String NUMBER_OF_CALLS_KEY = "numberOfCalls";
    public static final String REQUEST_SIZE_KEY = "requestSize";
    public static final String RESPONSE_SIZE_KEY = "responseSize";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StoreProxyComponent storeProxyComponent;

    private OkHttpClient getProxyOkHttpClient() {
        log.info("使用代理地址：{}, 代理端口：{}", storeProxyComponent.getIp(), storeProxyComponent.getPort());
        InetSocketAddress proxyAddr = new InetSocketAddress(storeProxyComponent.getIp(), storeProxyComponent.getPort());
        Proxy proxy = new Proxy(Proxy.Type.SOCKS, proxyAddr);
        Authenticator.setDefault(new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                log.info("使用代理认证：{}, {}", storeProxyComponent.getProxyUsername(), storeProxyComponent.getProxyPassword());
                return new PasswordAuthentication(storeProxyComponent.getProxyUsername(), storeProxyComponent.getProxyPassword().toCharArray());
            }
        });
        OkHttpClient proxyOkHttpClient = new OkHttpClient();
        proxyOkHttpClient.setProxy(proxy);
        return proxyOkHttpClient;
    }

    @Override
    protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            String uri = request.getRequestURI();
            String ipAddress = IpUtil.getIP(request);
            SpApiIpDTO spApiIpDTO = SpApiWhiteIpManager.getWhiteIpMap().get(ipAddress);
            if (!"/routing.json".equals(uri) && !"/ad-proxy".equals(uri) && !"/".equals(uri)) {
                log.info("======>>>调用URI地址：{}, 调用IP地址：{}", uri, ipAddress);
                if (!StringUtils.contains(uri, "/auth/o2/token")) {
                    if (StringUtils.isNotEmpty(ipAddress) && spApiIpDTO == null) {
                        log.warn("该IP禁止访问: {}", ipAddress);
                        forbidden(response, "该IP禁止访问：" + ipAddress);
                        return;
                    }
                }
            }

            String[] uriInfo = uri.split("/");
            if (!"/".equals(uri) && uriInfo.length > 2) {
                String token = request.getHeader("authorization");
//                Enumeration<String> headerNames = request.getHeaderNames();
//                long reqLength = 0;
//                while (headerNames.hasMoreElements()) {
//                    String key = headerNames.nextElement();
//                    String value = request.getHeader(key);
//                    reqLength += key.length() + value.length();
//                }
//                log.info("请求大小：(body){} ,(header){}", request.getContentLengthLong(), reqLength);

                RequestBuilder requestBuilder = getRequestBuilder(request, uri, uriInfo[1], ipAddress);
                OkHttpClient client = new OkHttpClient();
                client.setReadTimeout(okhttpReadTimeout, TimeUnit.SECONDS);
                client.setConnectTimeout(okhttpConnectTimeout, TimeUnit.SECONDS);
                client.setWriteTimeout(okhttpWriteTimeout, TimeUnit.SECONDS);
                if (request.getRequestURI().contains("/auth/o2/token") && StringUtils.equalsIgnoreCase(uriInfo[1], AdsUrl.FE.getRegion())) {
                    //如果是远东站点需要代理访问
                    client = getProxyOkHttpClient();

                }
                Request okHttpRequest = requestBuilder.getReqBuilder().build();
                Response okHttpResponse = client.newCall(okHttpRequest).execute();
                String statisticsKey = "ad_" + ipAddress + "_" + LocalDate.now().minusDays(1);
                long requestSize = request.getContentLengthLong() < 0 ? 0 : request.getContentLengthLong();
                long responseSize = 0;
                if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(statisticsKey, NUMBER_OF_CALLS_KEY))) {
                    hashIncrBy(statisticsKey, NUMBER_OF_CALLS_KEY, 1);
                    if (requestSize > 0) {
                        hashIncrBy(statisticsKey, REQUEST_SIZE_KEY, requestSize);
                    }
                } else {
                    Map<String, Object> maps = new HashMap<>();
                    maps.put(NUMBER_OF_CALLS_KEY, 1);
                    maps.put(REQUEST_SIZE_KEY, requestSize);
                    maps.put(RESPONSE_SIZE_KEY, responseSize);
                    hPutAll(statisticsKey, maps);
                }

//                log.info("[{}],返回码[{}],response=>{}", ipAddress, okHttpResponse.code(), okHttpResponse);
                Headers headers = okHttpResponse.headers();
//                int length = 0;
                for (String headerName : headers.names()) {
//                    length += headerName.length() + headers.get(headerName).length();
//                    log.info("addHeader->> name:{}，value:{}", headerName, headers.get(headerName));
                    //if ("Content-Length".equals(headerName)) {
                    //    response.addHeader(headerName, String.valueOf(okHttpResponse.body().contentLength()));
                    //    continue;
                    //}
                    if (!"Transfer-Encoding".equals(headerName)) {
                        response.addHeader(headerName, headers.get(headerName));
                    }
                }

                response.setStatus(okHttpResponse.code());
                response.setContentType(okHttpResponse.header("Content-Type"));

                ResponseBody responseBody = okHttpResponse.body();
                byte[] responseByte = responseBody.bytes().clone();
//                log.info("响应大小：(body)" + responseByte.length + ",(header)" + length);
                hashIncrBy(statisticsKey, RESPONSE_SIZE_KEY, responseByte.length);
                // 请求记录 ip、公司名、token的sha、url、日期、请求大小、响应大小、
                try {
                    String companyName = Optional.ofNullable(SpApiCompanyManager.getCompanyMap().get(spApiIpDTO.getCompanyId()))
                            .map(SpApiCompanyDTO::getCompanyName).orElse("");
                    MDC.put("logStore", logStore);
                    String tokenSha = StringUtils.isEmpty(token) ? null : DigestUtil.sha512Hex(token);
                    int statusCode = okHttpResponse.code();
                    RequestRecord requestRecord = new RequestRecord(ipAddress, companyName, tokenSha,
                            okHttpRequest.uri().getRawPath(), LocalDate.now(), requestSize, responseByte.length, statusCode);
                    // 500记录请求体和响应体
                    if (statusCode >= 500 && statusCode < 600) {
                        requestRecord.setRequestContent(requestBuilder.getRequestBody());
                        requestRecord.setResponseContent(new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", ""));
                    }
                    log.info(JSON.toJSONString(requestRecord));
                    MDC.remove("logStore");
                } catch (Exception e) {
                    log.error("记录请求记录失败", e);
                }

                ServletOutputStream servletOutputStream = response.getOutputStream();
                servletOutputStream.write(responseByte);
            }
        } catch (Exception e) {
            log.error("广告代理异常:", e);
            writeResponse(response, 500, "{\"message\": \"" + e.getMessage() + "\"}");
        }
    }

    private RequestBuilder getRequestBuilder(HttpServletRequest request, String uri, String region, String ipAddress) throws Exception {

        FormEncodingBuilder formBody = new FormEncodingBuilder();
        Map<String, Object> params = new HashMap<>();
        String url;
        if (request.getRequestURI().contains("/auth/o2/token")) {
            //刷新token构建表单参数
            url = AdsUrl.getTokenUrl(region);
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                formBody.add(paramName, paramValue);
                params.put(paramName, paramValue);
            }
            formBody.add("client_id", clientId);
            formBody.add("client_secret", clientSecret);
        } else {
            String query = request.getQueryString();
            String adsUrl = AdsUrl.getAdsUrl(region);
            url = adsUrl + uri.substring(uri.indexOf("/") + 3);
            if (StringUtils.isNotBlank(query)) {
                url = url + "?" + query;
            }
        }

//        String token = request.getHeader("authorization");
        String method = request.getMethod();
        log.info("======>>>[{}][{}]调用地址：{}", method, ipAddress, url);

        String contentType = request.getHeader("Content-Type");
        String contentLength = request.getHeader("Content-Length");

        //构造header
        Headers headers = getHeaders(request, region);
//        for (String s : headers.names()) {
//            String value = headers.get(s);
//            log.info("req header: {},{}", s, value);
//        }

        String body = getBody(request);
        boolean isNoBody = StringUtils.isBlank(body);

        if (contentLength != null && Integer.parseInt(contentLength) > 0) {
            if (contentType != null && !contentType.contains("x-www-form-urlencoded")) {
                RequestBody requestBody = RequestBody.create(MediaType.parse(contentType), ByteString.encodeUtf8(body));
                if (isNoBody) {
                    requestBody = null;
                }
                return new RequestBuilder(new Request.Builder().url(url).headers(headers).method(method, "GET".equalsIgnoreCase(method) ? null : requestBody), body);
            } else if (contentType != null && contentType.contains("x-www-form-urlencoded")) {
                return new RequestBuilder(new Request.Builder().url(url).headers(headers).method(method, formBody.build()), JSON.toJSONString(params));
            }
        }

        RequestBody requestBody = RequestBody.create(null, "");
        if ("GET".equalsIgnoreCase(method)) {
            requestBody = null;
        }

        return new RequestBuilder(new Request.Builder().url(url).headers(headers).method(method, requestBody), null);
    }

    private Long hashIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    private String getBody(HttpServletRequest request) throws IOException {
        return request.getReader().lines().reduce("", (accumulator, actual) -> accumulator + actual);
    }

    private Headers getHeaders(HttpServletRequest request, String region) {
        Headers.Builder headersBuilder = new Headers.Builder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
//            log.info("headerName:{},value:{}", headerName, headerValue);
            if (HttpHeaders.ACCEPT.equalsIgnoreCase(headerName) || HttpHeaders.USER_AGENT.equalsIgnoreCase(headerName) || HttpHeaders.AUTHORIZATION.equalsIgnoreCase(headerName) || "Amazon-Advertising-API-Scope".equalsIgnoreCase(headerName)) {
                headersBuilder.add(headerName, headerValue);
                continue;
            }
            if (HttpHeaders.HOST.equalsIgnoreCase(headerName)) {
                headersBuilder.add(headerName, AdsUrl.getAdsHost(region));
                log.debug("header->host:{},value:{}", headerName, AdsUrl.getAdsHost(region));
                continue;
            }
            if (HttpHeaders.CONTENT_TYPE.equalsIgnoreCase(headerName)) {
                String[] values = headerValue.split(";");
                headersBuilder.add("Content-Type", values[0]);
            }
//            headersBuilder.add(headerName, headerValue);
        }
        if (!request.getRequestURI().contains("/auth/o2/token")) {
            headersBuilder.add("amazon-advertising-api-clientid", clientId);
        }
        return headersBuilder.build();
    }

    private void forbidden(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 403, "{\"message\": \"" + msg + "\"}");
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }
}
