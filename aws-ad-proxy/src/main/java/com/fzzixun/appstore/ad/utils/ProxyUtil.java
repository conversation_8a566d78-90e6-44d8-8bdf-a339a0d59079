package com.fzzixun.appstore.ad.utils;


import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.Response;

import java.io.IOException;
import java.net.Authenticator;
import java.net.InetSocketAddress;
import java.net.PasswordAuthentication;
import java.net.Proxy;

public class ProxyUtil {
    static final int proxyPort = 30000; //your proxy port
    static final String proxyHost = "*************";
    static final String username = "MhDamc0L";
    static final String password = "PpUAgw1lJO";

    public static void main(String[] args) {
        InetSocketAddress proxyAddr = new InetSocketAddress(proxyHost, proxyPort);
        Proxy proxy = new Proxy(Proxy.Type.SOCKS, proxyAddr);
        Authenticator.setDefault(new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password.toCharArray());
            }
        });
        OkHttpClient proxyOkHttpClient = new OkHttpClient();
        proxyOkHttpClient.setProxy(proxy);
        String url = "https://api.amazon.co.jp/auth/o2/token";
        Request request = new Request.Builder().url(url).build();

        try {
            Response okHttpResponse = proxyOkHttpClient.newCall(request).execute();
            System.out.println(okHttpResponse.code());
            System.out.println(okHttpResponse.body().string());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
