package com.fzzixun.appstore.ad;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @create 2022/4/22
 */
@SpringBootApplication(scanBasePackages = {"com.fzzixun.appstore.common"},
        scanBasePackageClasses = {AwsAdProxyApplication.class})
@ServletComponentScan
@EnableApolloConfig
@EnableScheduling

public class AwsAdProxyApplication {
    public static void main(String[] args) {
        SpringApplication.run(AwsAdProxyApplication.class, args);
    }
}
