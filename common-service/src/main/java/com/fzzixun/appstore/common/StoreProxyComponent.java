package com.fzzixun.appstore.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fzzixun.appstore.browser.data.starter.client.StoreClient;
import com.fzzixun.appstore.browser.data.starter.request.StoreProxyRequest;
import com.fzzixun.appstore.browser.data.starter.response.CodeResponse;
import feign.FeignException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class StoreProxyComponent {

    private final StoreClient storeClient;

    @Value("${store.proxy.username:}")
    private String proxyUsername;
    @Value("${store.proxy.password:}")
    private String proxyPassword;
    @Value("${store.proxy.ip:}")
    private String ip;
    @Value("${store.proxy.port:}")
    private Integer port;

    public void storeProxy(Long companyId, Long storeId) {
        log.info("开始查询proxy信息，companyId:{}, storeId:{}", companyId, storeId);
        //打印已配置的代理信息
        log.info("当前已配置的代理信息：{},{},{},{}", getIp(), getPort(), getProxyUsername(), getProxyPassword());
        StoreProxyRequest storeProxyRequest = new StoreProxyRequest(companyId == null ? 16183902732048L : companyId, storeId == null ? 16207916583916L : storeId);
        CodeResponse<JSONObject> codeResponse;
        try {
            codeResponse = storeClient.queryProxyObject(storeProxyRequest);
        } catch (FeignException e) {
            log.error("查询proxy信息失败, params:{}", JSON.toJSONString(storeProxyRequest), e);
            return;
        }
        if (codeResponse.isSuccess()) {
            JSONObject responseData = codeResponse.getData();
            if (responseData == null) {
                return;
            }
            log.info("原始代理响应：{}", responseData.toJSONString());
            if (StringUtils.isNotBlank(responseData.getString("ip"))) {
                setIp(responseData.getString("ip"));
            }
            if (StringUtils.isNotBlank(responseData.getString("port"))) {
                setPort(responseData.getIntValue("port"));
            }
            if (StringUtils.isNotBlank(responseData.getString("proxyUsername"))) {
                setProxyUsername(responseData.getString("proxyUsername"));
            }
            if (StringUtils.isNotBlank(responseData.getString("proxyPassword"))) {
                setProxyPassword(responseData.getString("proxyPassword"));
            }
            printlnProxy();
        }
    }

    private void printlnProxy() {
        log.info("打印代理信息：{},{},{},{}", getIp(), getPort(), getProxyUsername(), getProxyPassword());
    }
}
