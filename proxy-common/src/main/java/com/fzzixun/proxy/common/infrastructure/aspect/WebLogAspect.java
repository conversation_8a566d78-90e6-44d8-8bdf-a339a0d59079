package com.fzzixun.proxy.common.infrastructure.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Aspect
@Slf4j
@Component
public class WebLogAspect {

    ThreadLocal<Long> startTime = new ThreadLocal<Long>();

    @Pointcut("@annotation(com.fzzixun.proxy.common.infrastructure.annotation.WebLog) " +
            "|| @within(com.fzzixun.proxy.common.infrastructure.annotation.WebLog))")
    public void logAspect() {
    }

    @Before("logAspect()")
    public void doBefore(JoinPoint joinPoint) {

        startTime.set(System.currentTimeMillis());

        // 记录下请求内容
        List<Object> argList = Arrays.asList(joinPoint.getArgs());
        log.info("请求方法: " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + " 参数 : " + argList.toString());
    }

    @AfterReturning(value = "logAspect()", returning="returnValue")
    public void doAfterReturning(JoinPoint joinPoint, Object returnValue) {
        // 处理完请求，打印返回内容
//        log.info("RETURN: " + JSONObject.toJSONString(returnValue));
        log.info("End: " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName()
                + " 耗时（毫秒） : " + (System.currentTimeMillis() - startTime.get()));
    }

}
