package com.fzzixun.proxy.common.infrastructure.config;

import io.swagger.annotations.Api;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR>
 * @description SwaggerConfig
 * @date 2021/4/23 11:22
 */

@Data
@Configuration
public class SwaggerConfig {

    @Value("${swagger.enable:false}")
    private Boolean enableSwagger;

    @Bean
    public Docket createRestApi() {
        return getDocket();
    }

    protected Docket getDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .enable(enableSwagger)
                .select()
                .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("速卖通代理微服务")
                .description("速卖通代理微服务")
                .termsOfServiceUrl("文档")
                .version("1.0")
                .build();
    }
}
