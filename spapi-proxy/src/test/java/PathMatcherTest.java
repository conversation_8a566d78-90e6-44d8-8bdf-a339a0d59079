import org.springframework.util.AntPathMatcher;

public class PathMatcherTest {
    public static void main(String[] args) {
        AntPathMatcher pathMatcher =  new AntPathMatcher();
        System.out.println(pathMatcher.match("/inbound/fba/{date}/inboundPlans", "/inbound/fba/2024-03-20/inboundPlans"));
        System.out.println(pathMatcher.match("/inbound/fba/*/inboundPlans", "/inbound/fba/2024-03-20/inboundPlans"));
        System.out.println(pathMatcher.match("/inbound/fba/*/inboundPlans", "/inbound/fba/2024-03-20/a/inboundPlans"));
        System.out.println(pathMatcher.match("/inbound/fba/inboundPlans/*", "/inbound/fba/inboundPlans/2024-03-20"));
        System.out.println(pathMatcher.match("/inbound/fba/inboundPlans/*", "/inbound/fba/inboundPlans/2024-03-20/a"));
    }
}
