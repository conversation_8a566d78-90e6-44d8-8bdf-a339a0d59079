import cn.hutool.core.util.RandomUtil;
import com.zixun.spapi.proxy.SpapiProxyApplication;
import com.zixun.spapi.proxy.servlet.RedisService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

@TestConfiguration(value = "local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpapiProxyApplication.class)
public class LimitTest {

    @Autowired
    private RedisService redisService;
    @Test
    public void testRedis() throws InterruptedException {
        for (int i = 0; i < 100; i++) {
            boolean isError = RandomUtil.randomBoolean();
            System.out.println("isError:" + isError);
            boolean shouldLimit = redisService.shouldLimit("123", "/api/123");
            if (shouldLimit) {
                System.out.println("已限流");
                Thread.sleep(1000);
                continue;
            }
            System.out.println("计算是否限流");
            redisService.calculateLimit("123", "/api/123", isError);
        }
    }
    @Test
    public void testRedisLimit() throws InterruptedException {
        for (int i = 0; i < 100; i++) {
            boolean isError = RandomUtil.randomBoolean();
            System.out.println("isError:" + isError);
            boolean shouldLimit = redisService.handle4xxLimit("123", "/api/123", null);
            if (shouldLimit) {
                System.out.println("已限流");
                Thread.sleep(1000);
                continue;
            }
            System.out.println("计算是否限流");
            if (isError) {
                int status = 400;
                System.out.println(status);
                redisService.calculate4xxLimit(status, "123", "/api/123");
            }
        }
    }
}
