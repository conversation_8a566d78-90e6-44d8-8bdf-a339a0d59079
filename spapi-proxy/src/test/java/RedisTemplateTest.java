import com.zixun.spapi.proxy.SpapiProxyApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @create 2022/5/27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpapiProxyApplication.class)
public class RedisTemplateTest {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testRedis() {
        try {
            redisTemplate.opsForValue().set("spapi_429", "429", 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(redisTemplate.opsForValue().get("spapi_429"));
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException exception) {
            exception.printStackTrace();
        }
        System.out.println(redisTemplate.opsForValue().get("spapi_429"));
        System.out.println("---------1-----------");
    }

    @Test
    public void sortedSet() {
        //redisTemplate.boundZSetOps("116.6.24.110_" + LocalDate.now().minusDays(1)).add("request_count", 1);
        //redisTemplate.boundZSetOps("116.6.24.110_" + LocalDate.now().minusDays(1)).incrementScore("request_count", 12);
        //Set s = redisTemplate.boundZSetOps("116.6.24.110_" + LocalDate.now().minusDays(1)).range(0, -1);
        //System.out.println(s);
        redisTemplate.opsForHash().put("116.6.24.110_" + LocalDate.now().minusDays(1), "numberOfCalls", 1);
        redisTemplate.opsForHash().put("116.6.24.110_" + LocalDate.now().minusDays(1), "requestSize", 1);
        redisTemplate.opsForHash().put("116.6.24.110_" + LocalDate.now().minusDays(1), "responseSize", 1);
        redisTemplate.opsForHash().increment("116.6.24.110_" + LocalDate.now().minusDays(1), "numberOfCalls", 1);
        redisTemplate.opsForHash().increment("116.6.24.110_" + LocalDate.now().minusDays(1), "numberOfCalls", 1);
        System.out.println(redisTemplate.opsForHash().get("116.6.24.110_" + LocalDate.now().minusDays(1), "numberOfCalls"));
        if (!redisTemplate.opsForHash().hasKey("127.0.0.1_" + LocalDate.now().minusDays(1), "numberOfCalls")) {
            redisTemplate.opsForHash().put("127.0.0.1_" + LocalDate.now().minusDays(1), "numberOfCalls", 5);
        }
        Set s =redisTemplate.opsForHash().keys("116.6.24.110_9" + LocalDate.now().minusDays(1));
        List l =redisTemplate.opsForHash().multiGet("116.6.24.110_9" + LocalDate.now().minusDays(1), s);
        System.out.println(s);
        System.out.println(l);
        Map<String, Long> maps = new HashMap<>();
        maps.put("k1", 1L);
        maps.put("k2", 2L);
        hPutAll("testhsh", maps);
    }
    public void hPutAll(String key, Map<String, Long> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

}
