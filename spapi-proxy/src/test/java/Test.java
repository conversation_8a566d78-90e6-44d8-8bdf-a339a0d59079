import cn.hutool.crypto.digest.DigestUtil;
import com.squareup.okhttp.Request;
import okio.ByteString;

import java.io.IOException;
import java.net.URISyntaxException;
import java.time.LocalDateTime;

public class Test {
    public static void main(String[] args) throws URISyntaxException, IOException {
//        LocalDateTime start = LocalDateTime.now();
//        System.out.println(start);
//        String url = "https://sellingpartnerapi-fe.amazon.com/listings/2021-08-01/items/A6NJ2D77QJIXV/FN016773-03Bjinhuih00026061?marketplaceIds=A39IBJ37TRP1C6&issueLocale=en_US";
//        Request request = (new Request.Builder()).url(url).method("GET", null).build();
//        System.out.println(request.url().toString());
//        System.out.println(request.uri().getHost());
//        System.out.println(request.uri().getPath());
//        System.out.println(request.uri().getRawPath());
//        String marketplaceIds = "";
//        String[] marketplaceIdArray = marketplaceIds.split("[,，]");
//        String[] marketplaceIdArray = new String[]{""};
//        for (String marketplaceId : marketplaceIdArray) {
//            System.out.println(marketplaceId);
//        }
        System.out.println(DigestUtil.sha512Hex(""));
    }
}
