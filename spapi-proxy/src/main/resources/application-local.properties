server.port=10086
apollo.bootstrap.enabled=true
apollo.meta=http://localhost:8080
management.server.port=19002
logging.level.root=info
logging.level.com.zixun.spapi.proxy=debug

spapi.setting.map.url_cn = https://shippingapi.amazon.cn
spapi.setting.map.url_eu = https://sellingpartnerapi-eu.amazon.com
spapi.setting.map.url_na = https://sellingpartnerapi-na.amazon.com
spapi.setting.map.url_fe = https://sellingpartnerapi-fe.amazon.com




spapi.setting.map.region_cn = cn-north-1
spapi.setting.map.region_fe = us-west-2
spapi.setting.map.region_na = us-east-1
spapi.setting.map.region_eu = eu-west-1

spapi.setting.map.module = definitions,aplus,authorization,catalog,fba,feeds,finances,mfn,messaging,notifications,orders,products,reports,sales,sellers,service,shipping,solicitations,tokens,uploads,auth,vendor,listings,inbound

ipWhiteList=127.0.0.1,*************,*************

testIpList=127.0.0.1,*************

testLimit=false

invalidApiList = GET:/inbound/fba
invalidAntApiList = GET:/inbound/fba/{date}/inboundPlans

# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

limitStatusCode=429

#测试
spapi.setting.map.access_key_test = ********************
spapi.setting.map.secret_key_test = +3VaPZJC6LxqUovVSrwJOwEvR6bX3SZkV08BLh+k
spapi.setting.map.client_id_test = amzn1.application-oa2-client.48a90d245897402cac66bc1072d0f811
spapi.setting.map.client_secret_test = a59ec70693f3be110cb0de90abd95f8389359940937ed47b1fce40f23b922c8d

spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM