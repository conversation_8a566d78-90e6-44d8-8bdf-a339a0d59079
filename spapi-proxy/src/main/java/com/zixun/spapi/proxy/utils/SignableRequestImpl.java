package com.zixun.spapi.proxy.utils;

import com.amazonaws.ReadLimitInfo;
import com.amazonaws.SignableRequest;
import com.amazonaws.http.HttpMethodName;
import com.squareup.okhttp.HttpUrl;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.Request;
import okio.Buffer;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class SignableRequestImpl implements SignableRequest<Request> {
    private static final String CONTENT_TYPE_HEADER_NAME = "Content-Type";
    private Request originalRequest;
    private Request.Builder signableRequestBuilder;

    SignableRequestImpl(Request originalRequest) {
        this.originalRequest = originalRequest;
        this.signableRequestBuilder = originalRequest.newBuilder();
    }

    @Override
    public void addHeader(String name, String value) {
        this.signableRequestBuilder.addHeader(name, value);
    }

    @Override
    public void addParameter(String name, String value) {
        HttpUrl newUrl = this.signableRequestBuilder.build().httpUrl().newBuilder().addEncodedQueryParameter(name, value).build();
        this.signableRequestBuilder.url(newUrl);
    }

    @Override
    public void setContent(InputStream inputStream) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap();
        Request requestSnapshot = this.signableRequestBuilder.build();
        requestSnapshot.headers().names().forEach((headerName) -> {
            String var10000 = (String) headers.put(headerName, requestSnapshot.header(headerName));
        });
        if (requestSnapshot.body() != null) {
            MediaType contentType = requestSnapshot.body().contentType();
            if (contentType != null) {
                headers.put("Content-Type", contentType.toString());
            }
        }

        return headers;
    }

    @Override
    public String getResourcePath() {
        try {
            return this.originalRequest.url().toURI().getPath();
        } catch (URISyntaxException var2) {
            throw new RuntimeException(var2);
        }
    }

    @Override
    public Map<String, List<String>> getParameters() {
        HashMap parameters = new HashMap();

        try {
            List<NameValuePair> nameValuePairs = URLEncodedUtils.parse(this.originalRequest.url().toURI(), String.valueOf(StandardCharsets.UTF_8));
            nameValuePairs.forEach((nameValuePair) -> {
                List var10000 = (List) parameters.put(nameValuePair.getName(), Collections.singletonList(nameValuePair.getValue()));
            });
            return parameters;
        } catch (URISyntaxException var3) {
            throw new RuntimeException(var3);
        }
    }

    @Override
    public URI getEndpoint() {
        URI uri = null;

        try {
            uri = this.originalRequest.url().toURI();
        } catch (URISyntaxException var3) {
            throw new RuntimeException(var3);
        }

        return URI.create(String.format("%s://%s", uri.getScheme(), uri.getHost()));
    }

    @Override
    public HttpMethodName getHttpMethod() {
        return HttpMethodName.fromValue(this.originalRequest.method().toUpperCase());
    }

    @Override
    public int getTimeOffset() {
        return 0;
    }

    @Override
    public InputStream getContent() {
        ByteArrayInputStream inputStream = null;
        if (this.originalRequest.body() != null) {
            try {
                Buffer buffer = new Buffer();
                this.originalRequest.body().writeTo(buffer);
                inputStream = new ByteArrayInputStream(buffer.readByteArray());
            } catch (IOException var3) {
                throw new RuntimeException("Unable to buffer request body", var3);
            }
        }

        return inputStream;
    }

    @Override
    public InputStream getContentUnwrapped() {
        return this.getContent();
    }

    @Override
    public ReadLimitInfo getReadLimitInfo() {
        return null;
    }

    @Override
    public Object getOriginalRequestObject() {
        return this.signableRequestBuilder.build();
    }
}
