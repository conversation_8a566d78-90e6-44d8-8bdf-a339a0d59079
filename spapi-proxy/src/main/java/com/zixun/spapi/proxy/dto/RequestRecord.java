package com.zixun.spapi.proxy.dto;

import lombok.Data;

import java.time.LocalDate;

@Data
public class RequestRecord {
    // ip
    private String ip;
    // 公司名
    private String company;
    // 店铺id
    private String marketId;
    // 请求url
    private String url;
    // 日期
    private LocalDate date;
    // 请求大小
    private long requestSize;
    // 响应大小
    private long responseSize;
    // 状态码
    private int statusCode;
    // 请求体
    private String requestContent;
    // 响应体
    private String responseContent;

    public RequestRecord() {
    }

    public RequestRecord(String ip, String company, String marketId, String url,
                         LocalDate date, long requestSize, long responseSize, int statusCode) {
        this.ip = ip;
        this.company = company;
        this.marketId = marketId;
        this.url = url;
        this.date = date;
        this.requestSize = requestSize;
        this.responseSize = responseSize;
        this.statusCode = statusCode;
    }
}