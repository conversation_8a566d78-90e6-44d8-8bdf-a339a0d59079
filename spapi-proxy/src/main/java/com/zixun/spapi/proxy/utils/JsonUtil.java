/**
 * Copyright © 2016zg. All rights reserved.
 */
package com.zixun.spapi.proxy.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * JSON数据解析类
 *
 * <AUTHOR>
 * @version 1.0
 * <p>
 * operation
 * date         operator            content
 * 2016年11月17日      ZX                新建
 * @date 2016年11月17日
 */
public class JsonUtil {

    public static String FORMART_YYYYMMDD_HHMMSS_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 禁止实例化
     */
    private JsonUtil() {

    }

    /**
     * @param jsonStr    JSON字符串
     * @param t          列表对象
     * @param dateformat 日期格式化字符串
     * @return 转换后的列表
     * @throws Exception
     * @Description: 将json字符串转换成list对象
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> jsonToList(String jsonStr, Class<T> t, final String dateformat) throws Exception {

        ObjectMapper mapper = new ObjectMapper();
        final DateFormat df = new SimpleDateFormat(dateformat);
        mapper.setDateFormat(df);

        JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class, t);

        return (List<T>) mapper.readValue(jsonStr, javaType);
    }

    /**
     * @param jsonStr JSON字符串
     * @param t       列表对象
     * @return 转换后的列表
     * @throws Exception
     * @Description: 将json字符串转换成list对象
     * <AUTHOR>
     */
    public static <T> List<T> jsonToList(String jsonStr, Class<T> t) throws Exception {
        return jsonToList(jsonStr, t, FORMART_YYYYMMDD_HHMMSS_DEFAULT);
    }

    /**
     * @param jsonStr JSON字符串
     * @return 转换后的MAP
     * @throws Exception
     * @Description: 将JSON字符串转换成MAP
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> jsonToMap(String jsonStr) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return (Map<String, Object>) mapper.readValue(jsonStr, Map.class);
    }
}
