package com.zixun.spapi.proxy.servlet;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.squareup.okhttp.*;
import com.zixun.spapi.proxy.amazonservices.client.MwsUtl;
import com.zixun.spapi.proxy.dto.RequestRecord;
import com.zixun.spapi.proxy.dto.SpApiCompanyDTO;
import com.zixun.spapi.proxy.dto.SpApiIpDTO;
import com.zixun.spapi.proxy.manager.SpApiCompanyManager;
import com.zixun.spapi.proxy.manager.SpApiInvalidApiManager;
import com.zixun.spapi.proxy.manager.SpApiWhiteIpManager;
import com.zixun.spapi.proxy.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.AbortableHttpRequest;
import org.apache.http.client.utils.URIUtils;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.AntPathMatcher;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpCookie;
import java.net.URI;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SP-API代理转发服务
 */
@WebServlet("/*")
@Slf4j
public class ProxyServlet extends HttpServlet {

    public static final String LIMIT_KEY = "spapi_limit_key:%s";
    public static final String NUMBER_OF_CALLS_KEY = "numberOfCalls";
    public static final String REQUEST_SIZE_KEY = "requestSize";
    public static final String RESPONSE_SIZE_KEY = "responseSize";

    @Value("${ipWhiteList}")
    private String ipWhiteList;

    @Value("${limitStatusCode:429}")
    private Integer limitStatusCode;

    @Value("${limitTime:10}")
    private Integer limitTime;

    @Value("${limitReturnCode:403}")
    private Integer limitReturnCode;

    @Value("${printAwsResponseLog:false}")
    private Boolean printAwsResponseLog;

    @Value("${logStore.name:spapi-record}")
    private String logStore;

    // 测试用，开启后所有转发请求的店铺保存
    @Value("${marketplaceRecord.test:false}")
    private Boolean marketplaceRecordTest;

    @Value("${param.okhttp.read-timeout:30}")
    private Integer okhttpReadTimeout;

    @Value("${param.okhttp.connect-timeout:10}")
    private Integer okhttpConnectTimeout;

    @Value("${param.okhttp.write-timeout:30}")
    private Integer okhttpWriteTimeout;

    @Autowired
    private ProxyUtil proxyUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApiCounter apiCounter;

    @Autowired
    private RedisService redisService;

    private static final AntPathMatcher pathMatcher =  new AntPathMatcher();

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 3036995738596829749L;

    /* INIT PARAMETER NAME CONSTANTS */
    /**
     * A boolean parameter name to enable forwarding of the client IP
     */
    public static final String P_FORWARDEDFOR = "forwardip";

    /**
     * A boolean parameter name to keep HOST parameter as-is
     */
    public static final String P_PRESERVEHOST = "preserveHost";

    /**
     * A boolean parameter name to keep COOKIES as-is
     */
    public static final String P_PRESERVECOOKIES = "preserveCookies";

    /**
     * A boolean parameter name to have auto-handle redirects
     */
    public static final String P_HANDLEREDIRECTS = "http.protocol.handle-redirects"; // ClientPNames.HANDLE_REDIRECTS

    /**
     * A integer parameter name to set the socket connection timeout (millis)
     */
    public static final String P_CONNECTTIMEOUT = "http.socket.timeout"; // CoreConnectionPNames.SO_TIMEOUT

    /**
     * A integer parameter name to set the socket read timeout (millis)
     */
    public static final String P_READTIMEOUT = "http.read.timeout";

    /**
     * A integer parameter name to set the connection request timeout (millis)
     */
    public static final String P_CONNECTIONREQUESTTIMEOUT = "http.connectionrequest.timeout";

    /**
     * A integer parameter name to set max connection number
     */
    public static final String P_MAXCONNECTIONS = "http.maxConnections";

    /**
     * A boolean parameter whether to use JVM-defined system properties to configure various networking aspects.
     */
    public static final String P_USESYSTEMPROPERTIES = "useSystemProperties";

    /**
     * The parameter name for the target (destination) URI to proxy to.
     */
    protected static final String ATTR_TARGET_URI = ProxyServlet.class.getSimpleName() + ".targetUri";
    protected static final String ATTR_TARGET_HOST = ProxyServlet.class.getSimpleName() + ".targetHost";

    /* MISC */

    protected boolean doLog = false;
    protected boolean doForwardIP = true;
    /**
     * User agents shouldn't send the url fragment but what if it does?
     */
    protected boolean doSendUrlFragment = true;
    protected boolean doPreserveHost = false;
    protected boolean doPreserveCookies = false;
    protected boolean doHandleRedirects = false;
    protected boolean useSystemProperties = true;
    protected int connectTimeout = 300000;
    protected int readTimeout = 300000;
    protected int connectionRequestTimeout = 300000;
    protected int maxConnections = 30;

    // These next 3 are cached here, and should only be referred to in
    // initialization logic. See the
    // ATTR_* parameters.
    /**
     * From the configured parameter "targetUri".
     */
    protected String targetUri;
    protected URI targetUriObj;// new URI(targetUri)
    protected HttpHost targetHost;// URIUtils.extractHost(targetUriObj);

    private HttpClient proxyClient;

    @Override
    public String getServletInfo() {
        return "A proxy servlet by David Smiley, <EMAIL>";
    }

    protected String getTargetUri(HttpServletRequest servletRequest) {
        return (String) servletRequest.getAttribute(ATTR_TARGET_URI);
    }

    protected HttpHost getTargetHost(HttpServletRequest servletRequest) {
        return (HttpHost) servletRequest.getAttribute(ATTR_TARGET_HOST);
    }

    /**
     * Reads a configuration parameter. By default it reads servlet init parameters but
     * it can be overridden.
     */
    protected String getConfigParam(String key) {
        Map<String, String> keyMap = new HashMap<String, String>();
        keyMap.put("log", "true");
        return keyMap.get(key);

        // return getServletConfig().getInitParameter(key);
    }

    /**
     * 服务器初始化
     */
    @Override
    public void init() throws ServletException {

        String doForwardIPString = getConfigParam(P_FORWARDEDFOR);
        if (doForwardIPString != null) {
            this.doForwardIP = Boolean.parseBoolean(doForwardIPString);
        }

        String preserveHostString = getConfigParam(P_PRESERVEHOST);
        if (preserveHostString != null) {
            this.doPreserveHost = Boolean.parseBoolean(preserveHostString);
        }

        String preserveCookiesString = getConfigParam(P_PRESERVECOOKIES);
        if (preserveCookiesString != null) {
            this.doPreserveCookies = Boolean.parseBoolean(preserveCookiesString);
        }

        String handleRedirectsString = getConfigParam(P_HANDLEREDIRECTS);
        if (handleRedirectsString != null) {
            this.doHandleRedirects = Boolean.parseBoolean(handleRedirectsString);
        }

        String connectTimeoutString = getConfigParam(P_CONNECTTIMEOUT);
        if (connectTimeoutString != null) {
            this.connectTimeout = Integer.parseInt(connectTimeoutString);
        }

        String readTimeoutString = getConfigParam(P_READTIMEOUT);
        if (readTimeoutString != null) {
            this.readTimeout = Integer.parseInt(readTimeoutString);
        }

        String connectionRequestTimeout = getConfigParam(P_CONNECTIONREQUESTTIMEOUT);
        if (connectionRequestTimeout != null) {
            this.connectionRequestTimeout = Integer.parseInt(connectionRequestTimeout);
        }

        String maxConnections = getConfigParam(P_MAXCONNECTIONS);
        if (maxConnections != null) {
            this.maxConnections = Integer.parseInt(maxConnections);
        }

        String useSystemPropertiesString = getConfigParam(P_USESYSTEMPROPERTIES);
        if (useSystemPropertiesString != null) {
            this.useSystemProperties = Boolean.parseBoolean(useSystemPropertiesString);
        }
        proxyClient = createHttpClient();
    }

    /**
     * Sub-classes can override specific behaviour of {@link RequestConfig}.
     */
    protected RequestConfig buildRequestConfig() {
        return RequestConfig.custom().setRedirectsEnabled(doHandleRedirects).setCookieSpec(CookieSpecs.IGNORE_COOKIES) // we
                // handle
                // them
                // in
                // the
                // servlet
                // instead
                .setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout)
                .setConnectionRequestTimeout(connectionRequestTimeout).build();
    }

    /**
     * Sub-classes can override specific behaviour of {@link SocketConfig}.
     */
    protected SocketConfig buildSocketConfig() {

        if (readTimeout < 1) {
            return null;
        }

        return SocketConfig.custom().setSoTimeout(readTimeout).build();
    }

    /**
     * 初始化连接目标
     *
     * @throws ServletException
     * @date: 2019年1月17日
     * @author: zx
     */
    protected void initTarget(String targetUriStr) throws ServletException {
        targetUri = targetUriStr;
        try {
            targetUriObj = new URI(targetUriStr);
        } catch (Exception e) {
            throw new ServletException("Trying to process targetUri init parameter: " + e, e);
        }
        targetHost = URIUtils.extractHost(targetUriObj);
    }

    /**
     * Called from {@link #init(javax.servlet.ServletConfig)}.
     * HttpClient offers many opportunities for customization.
     * In any case, it should be thread-safe.
     */
    protected HttpClient createHttpClient() {
        HttpClientBuilder clientBuilder = HttpClientBuilder.create().setDefaultRequestConfig(buildRequestConfig())
                .setDefaultSocketConfig(buildSocketConfig());

        clientBuilder.setMaxConnTotal(maxConnections);

        if (useSystemProperties) {
            clientBuilder = clientBuilder.useSystemProperties();
        }
        return clientBuilder.build();
    }

    /**
     * The http client used.
     *
     * @see #createHttpClient()
     */
    protected HttpClient getProxyClient() {
        return proxyClient;
    }

    @Override
    public void destroy() {
        // Usually, clients implement Closeable:
        if (proxyClient instanceof Closeable) {
            try {
                ((Closeable) proxyClient).close();
            } catch (IOException e) {
                log("While destroying servlet, shutting down HttpClient: " + e, e);
            }
        } else {
            // Older releases require we do this:
            if (proxyClient != null)
                proxyClient.getConnectionManager().shutdown();
        }
        super.destroy();
    }

    /**
     * 创建请求对象
     *
     * @param servletRequest
     * @return
     * @throws Exception
     * @date: 2019年1月17日
     * @author: zx
     */
    private HttpRequest createHttpRequest(HttpServletRequest servletRequest) throws Exception {
        String abc = servletRequest.getRequestURL().toString();
        String awsAccessKeyId = "abc";
        String awsSecretKey = "bcd";
        String awsUrl = "https://sellingpartnerapi-na.amazon.com";
        initTarget(awsUrl);
        if (servletRequest.getAttribute(ATTR_TARGET_URI) == null) {
            servletRequest.setAttribute(ATTR_TARGET_URI, this.targetUri);
        }
        if (servletRequest.getAttribute(ATTR_TARGET_HOST) == null) {
            servletRequest.setAttribute(ATTR_TARGET_HOST, this.targetHost);
        }
        String method = servletRequest.getMethod();
        String proxyRequestUri = rewriteUrlFromRequest(servletRequest);
        if (servletRequest.getHeader("Content-Length") != null || servletRequest
                .getHeader("Transfer-Encoding") != null) {
            if (!servletRequest.getHeader("content-type").contains("x-www-form-urlencoded")) {
                return newProxyRequestWithEntity(method, proxyRequestUri, servletRequest);
            }
            Map<String, String> paramsMap = new HashMap<>();
            for (Map.Entry<String, String[]> paramsEntry : (Iterable<Map.Entry<String, String[]>>) servletRequest.getParameterMap().entrySet()) {
                paramsMap.put(paramsEntry.getKey(), ((String[]) paramsEntry.getValue())[0]);
            }
            String signatureMethod = paramsMap.get("SignatureMethod");
            paramsMap.put("AWSAccessKeyId", awsAccessKeyId);
            paramsMap.remove("Signature");
            paramsMap.remove("SignatureMethod");
            URI uriInfo = new URI(proxyRequestUri);
            String signature = MwsUtl.signParameters(uriInfo, paramsMap.get("SignatureVersion"), signatureMethod, paramsMap, awsSecretKey, method);
            paramsMap.put("Signature", signature);
            log.info(signature);
            List<BasicNameValuePair> formValueList = new ArrayList<>();
            for (Map.Entry<String, String> paramsEntry : paramsMap.entrySet()) {
                log.info("Key:{}, Value:{}", paramsEntry.getKey(), paramsEntry.getValue());
                formValueList.add(new BasicNameValuePair(paramsEntry.getKey(), paramsEntry.getValue()));
            }
            return newProxyRequestWithEntity(method, proxyRequestUri, servletRequest, formValueList);
        }
        String[] urlInfos = proxyRequestUri.split("\\?");
        return (HttpRequest) new BasicHttpRequest(method, proxyRequestUri);
    }

    @Override
    protected void service(HttpServletRequest servletRequest, HttpServletResponse servletResponse)
            throws ServletException, IOException {

        try {
            String uri = servletRequest.getRequestURI();
            String ipAddress = IpUtil.getIP(servletRequest);
            SpApiIpDTO spApiIpDTO = SpApiWhiteIpManager.getWhiteIpMap().get(ipAddress);
            if (!"/routing.json".equals(uri) && !"/".equals(uri)) {
                log.info("URI:{},IP:{}", uri, ipAddress);
                // test println count api:counter:********:LilandaTech:************
//                if (uri.contains("println/apiCount")) {
//                    apiCounter.printSortedSetData(apiCounter.getRedisKey("LilandaTech", ipAddress));
//                }
//                if (ipAddress.equals("************")|| ipAddress.equals("127.0.0.1")) {
//                    apiCounter.increaseCount("LilandaTech", ipAddress, uri);
//                }
                if (!StringUtils.contains(uri, "/auth/o2/token")) {
                    if (StringUtils.isNotEmpty(ipAddress) && spApiIpDTO == null) {
                        log.warn("该IP禁止访问: {}", ipAddress);
                        forbidden(servletResponse, "该IP禁止访问：" + ipAddress);
                        return;
                    }
                }
            }

            String[] uriInfo = uri.split("/");
            List<String> spApiModule = Arrays.asList(proxyUtil.getSpApiSettingInfo("module").split(","));
            if (!"/".equals(uri) && uriInfo.length > 2 && (spApiModule.contains(uriInfo[1]) || spApiModule.contains(uriInfo[2]))) {
                String token = servletRequest.getHeader("x-amz-access-token");
                String apiUri = servletRequest.getRequestURI().substring(servletRequest.getRequestURI().indexOf("/") + 3);
                String apiMethodUri = servletRequest.getMethod() + ":" + apiUri;
                if (SpApiInvalidApiManager.getInvalidApiList().contains(apiMethodUri)) {
                    log.info("======>>>调用接口已下线：{}", apiMethodUri);
                    forbidden(servletResponse, "该接口已下线");
                    return;
                }
                if (CollectionUtil.isNotEmpty(SpApiInvalidApiManager.getInvalidAntApiList())) {
                    for (String antApi : SpApiInvalidApiManager.getInvalidAntApiList()) {
                        if (pathMatcher.match(antApi, apiMethodUri)) {
                            log.info("======>>>调用接口已下线：{}", apiMethodUri);
                            forbidden(servletResponse, "该接口已下线");
                            return;
                        }
                    }
                }
                if (StringUtils.isNotBlank(token)) {
                    String tokenMd5 = SecureUtil.md5(token);
                    String region = uriInfo[1];
                    RequestBuilder requestBuilder = proxyUtil.createOkHttpRequest(servletRequest, region);
                    Request request = proxyUtil.createHeader(requestBuilder.getReqBuilder(), servletRequest, region, ipAddress);
                    String limitApi = request.method() + "-" + request.uri().getRawPath();
                    //全局限制请求
                    if (redisService.handle4xxLimit(tokenMd5, limitApi, servletResponse)) {
                        return;
                    }

                    String statisticsKey = ipAddress + "_" + LocalDate.now().minusDays(1);
                    long requestSize = servletRequest.getContentLengthLong() < 0 ? 0 : servletRequest.getContentLengthLong();
                    long responseSize = 0;
                    if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(statisticsKey, NUMBER_OF_CALLS_KEY))) {
                        hashIncrBy(statisticsKey, NUMBER_OF_CALLS_KEY, 1);
                        if (requestSize > 0) {
                            hashIncrBy(statisticsKey, REQUEST_SIZE_KEY, requestSize);
                        }
                    } else {
                        Map<String, Object> maps = new HashMap<>();
                        maps.put(NUMBER_OF_CALLS_KEY, 1);
                        maps.put(REQUEST_SIZE_KEY, requestSize);
                        maps.put(RESPONSE_SIZE_KEY, responseSize);
                        hPutAll(statisticsKey, maps);
                    }
                    OkHttpClient httpClient = new OkHttpClient();
                    httpClient.setReadTimeout(okhttpReadTimeout, TimeUnit.SECONDS);
                    httpClient.setConnectTimeout(okhttpConnectTimeout, TimeUnit.SECONDS);
                    httpClient.setWriteTimeout(okhttpWriteTimeout, TimeUnit.SECONDS);
                    Call call = httpClient.newCall(request);
                    Response response = call.execute();
                    if (Boolean.TRUE.equals(printAwsResponseLog)) {
                        log.info("[{}],返回码[{}]response=>{}", ipAddress, response.code(), response.toString());
                    }
                    servletResponse.setStatus(response.code(), response.message());
                    Headers headers = response.headers();
                    for (String headerName : headers.names()) {
                        if (Boolean.TRUE.equals(printAwsResponseLog)) {
                            log.info("addHeader->> name:{}，value:{}", headerName, headers.get(headerName));
                        }
                        if ("Content-Length".equals(headerName)) {
                            servletResponse.addHeader(headerName, String.valueOf(response.body().contentLength()));
                            continue;
                        }
                        if (!"Transfer-Encoding".equals(headerName)) {
                            servletResponse.addHeader(headerName, headers.get(headerName));
                        } else {
                            if (Boolean.TRUE.equals(printAwsResponseLog)) {
                                log.info("no addHeader:{}", headerName);
                            }
                        }
                    }
                    ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
                    byte[] responseByte = response.body().bytes().clone();
                    if (Boolean.TRUE.equals(printAwsResponseLog)) {
                        log.info("[{}]返回结果大小{} ===>>>{}", ipAddress, responseByte.length, new String(responseByte, StandardCharsets.UTF_8));
                    }
                    if (500 == response.code()) {
                        //响应500打印请求和响应
                        log.info("500内部错误req: [{}][{}]请求地址：{}, 请求体: {}", servletRequest.getMethod(), ipAddress, request.url(), requestBuilder.getRequestBody());
                        String result = new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", "");
                        log.info("500内部错误resp: [{}][{}],返回码[{}], response=>{}", servletRequest.getMethod(), ipAddress, response.code(), result);
                    }
//                    log.info("响应大小：(body)" + responseByte.length + ",(header)" + length);
                    hashIncrBy(statisticsKey, RESPONSE_SIZE_KEY, responseByte.length);
                    redisService.calculate4xxLimit(response.code(), tokenMd5, limitApi);
                    // 请求记录 ip、公司名、店铺id、url、日期、请求大小、响应大小、
                    try {
                        String companyName = Optional.ofNullable(SpApiCompanyManager.getCompanyMap().get(spApiIpDTO.getCompanyId()))
                                .map(SpApiCompanyDTO::getCompanyName).orElse("");
//                        String[] marketplaceIdArray;
//                        if (marketplaceIds == null) {
//                            marketplaceIdArray = new String[]{""};
//                        } else {
//                            marketplaceIdArray = marketplaceIds.split("[,，]");
//                        }
                        MDC.put("logStore", logStore);
                        String marketplaceId = DigestUtil.sha512Hex(token);
                        int statusCode = response.code();
                        RequestRecord requestRecord = new RequestRecord(ipAddress, companyName, marketplaceId,
                                request.uri().getRawPath(), LocalDate.now(), requestSize, responseByte.length, statusCode);
                        // 500记录请求体和响应体
                        if (statusCode >= 500 && statusCode < 600) {
                            requestRecord.setRequestContent(requestBuilder.getRequestBody());
                            requestRecord.setResponseContent(new String(responseByte, StandardCharsets.UTF_8).replaceAll("\\n", ""));
                        }
                        log.info(JSON.toJSONString(requestRecord));
                        MDC.remove("logStore");
                    } catch (Exception e) {
                        log.error("记录请求记录失败", e);
                    }
//                    if ((marketplaceRecordTest && marketplaceIds != null) || (response.isSuccessful() && marketplaceIds != null)) {
//                        recordService.recordMarketplace(spApiIpDTO.getCompanyId(), marketplaceIds);
//                    }
                    servletOutputStream.write(responseByte);
                } else {
                    String region = uriInfo[1];
                    Request.Builder requestBuilder = proxyUtil.createTokenOkHttpRequest(servletRequest, region, ipAddress);
                    OkHttpClient httpClient = new OkHttpClient();
                    Call call = httpClient.newCall(requestBuilder.build());
                    Response response = call.execute();
                    log.info("[{}],AT返回码[{}]resp:{}", ipAddress, response.code(), response.toString());
                    servletResponse.setStatus(response.code(), response.message());
                    Headers headers = response.headers();
                    for (String headerName : headers.names()) {
                        if ("Content-Length".equals(headerName)) {
                            servletResponse.addHeader(headerName, String.valueOf(response.body().contentLength()));
                            continue;
                        }
                        servletResponse.addHeader(headerName, headers.get(headerName));
                    }
                    ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
                    byte[] responseByte = response.body().bytes().clone();
                    log.info("[{}]AT返回结果:{}", ipAddress, new String(responseByte, StandardCharsets.UTF_8));
                    servletOutputStream.write(responseByte);
                }
            } else {
                writeResponse(servletResponse, 200, "{\"errors\": \"未找到相对应的SP-API接口模块，请确认接口地址\"}");
            }
        } catch (Exception e) {
            log.error("代理请求异常！", e);
        }
    }

    private Long hashIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    private void forbidden(HttpServletResponse servletResponse, String msg) throws IOException {
        writeResponse(servletResponse, 403, "{\"errors\": \"" + msg + "\"}");
    }

    private void writeResponse(HttpServletResponse servletResponse, int statusCode, String result) throws IOException {
        servletResponse.setContentType("application/json;charset=utf-8");
        ServletOutputStream servletOutputStream = servletResponse.getOutputStream();
        servletOutputStream.write(result.getBytes(Charset.defaultCharset()));
        servletResponse.setStatus(statusCode);
    }


    private void limitByStatusCode(String key, int code, URL url) {
        if (code != limitStatusCode) {
            return;
        }
        try {
            //返回码为limitByStatusCode，一定时间(s)内限制调用
            redisTemplate.opsForValue().set(key, String.valueOf(limitStatusCode), limitTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("limitByStatusCode Exception！", e);
        }
    }

    protected void handleRequestException(HttpRequest proxyRequest, Exception e) throws ServletException, IOException {
        // abort request, according to best practice with HttpClient
        if (proxyRequest instanceof AbortableHttpRequest) {
            AbortableHttpRequest abortableHttpRequest = (AbortableHttpRequest) proxyRequest;
            abortableHttpRequest.abort();
        }
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        }
        if (e instanceof ServletException) {
            throw (ServletException) e;
        }
        // noinspection ConstantConditions
        if (e instanceof IOException) {
            throw (IOException) e;
        }
        throw new RuntimeException(e);
    }

    protected HttpResponse doExecute(HttpServletRequest servletRequest, HttpServletResponse servletResponse,
                                     HttpRequest proxyRequest) throws IOException {
        if (doLog) {
            log("proxy " + servletRequest.getMethod() + " uri: " + servletRequest.getRequestURI() + " -- "
                    + proxyRequest.getRequestLine().getUri());
        }
        return proxyClient.execute(getTargetHost(servletRequest), proxyRequest);
    }

    protected HttpRequest newProxyRequestWithEntity(String method, String proxyRequestUri,
                                                    HttpServletRequest servletRequest, List<BasicNameValuePair> formValueList) throws IOException {
        HttpEntityEnclosingRequest eProxyRequest = new BasicHttpEntityEnclosingRequest(method, proxyRequestUri);
        // Add the input entity (streamed)
        // note: we don't bother ensuring we close the servletInputStream since
        // the container handles it
        UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(formValueList, "UTF-8");
        eProxyRequest.setEntity(urlEncodedFormEntity);
        return eProxyRequest;
    }

    protected HttpRequest newProxyRequestWithEntity(String method, String proxyRequestUri,
                                                    HttpServletRequest servletRequest) throws IOException {
        HttpEntityEnclosingRequest eProxyRequest = new BasicHttpEntityEnclosingRequest(method, proxyRequestUri);
        // Add the input entity (streamed)
        // note: we don't bother ensuring we close the servletInputStream since
        // the container handles it
        eProxyRequest.setEntity(new InputStreamEntity(servletRequest.getInputStream(), -1));

        return eProxyRequest;
    }

    // Get the header value as a long in order to more correctly proxy very
    // large requests
    private long getContentLength(HttpServletRequest request) {
        String contentLengthHeader = request.getHeader("Content-Length");
        if (contentLengthHeader != null) {
            return Long.parseLong(contentLengthHeader);
        }
        return -1L;
    }

    protected void closeQuietly(Closeable closeable) {
        try {
            closeable.close();
        } catch (IOException e) {
            log(e.getMessage(), e);
        }
    }

    /**
     * These are the "hop-by-hop" headers that should not be copied.
     * http://www.w3.org/Protocols/rfc2616/rfc2616-sec13.html
     * I use an HttpClient HeaderGroup class instead of Set&lt;String&gt; because this
     * approach does case insensitive lookup faster.
     */
    protected static final HeaderGroup hopByHopHeaders;

    static {
        hopByHopHeaders = new HeaderGroup();
        String[] headers = new String[]{"Connection", "Keep-Alive", "Proxy-Authenticate", "Proxy-Authorization", "TE",
                "Trailers", "Transfer-Encoding", "Upgrade"};
        for (String header : headers) {
            hopByHopHeaders.addHeader(new BasicHeader(header, null));
        }
    }

    /**
     * Copy request headers from the servlet client to the proxy request.
     * This is easily overridden to add your own.
     */
    protected void copyRequestHeaders(HttpServletRequest servletRequest, HttpRequest proxyRequest) throws IOException {
        // Get an Enumeration of all of the header names sent by the client
        @SuppressWarnings("unchecked")
        Enumeration<String> enumerationOfHeaderNames = servletRequest.getHeaderNames();
        while (enumerationOfHeaderNames.hasMoreElements()) {
            String headerName = enumerationOfHeaderNames.nextElement();
            if ("authorization".equals(headerName) || "x-amz-date".equals(headerName) || "content-type".equals(headerName)
                    || "accept-encoding".equals(headerName)) {
                continue;
            }
            copyRequestHeader(servletRequest, proxyRequest, headerName);
        }
        AWSAuthenticationCredentials awsAuthenticationCredentials = new AWSAuthenticationCredentials(
                "********************",
                "+3VaPZJC6LxqUovVSrwJOwEvR6bX3SZkV08BLh+k",
                "us-east-1"
        );
        AWSSigV4Signer awsSigV4Signer = new AWSSigV4Signer(awsAuthenticationCredentials);
//        HttpEntityEnclosingRequest tempRequest = (HttpEntityEnclosingRequest)proxyRequest;
//        InputStream inputStream = tempRequest.getEntity().getContent();
//        ByteArrayOutputStream result = new ByteArrayOutputStream();
//        byte[] buffer = new byte[1024];
//        int length;
//        while ((length = inputStream.read(buffer)) != -1) {
//            result.write(buffer, 0, length);
//        }
//        String str = result.toString(StandardCharsets.UTF_8.name());
        String str = "{\"reportType\":\"GET_FLAT_FILE_OPEN_LISTINGS_DATA\",\"marketplaceIds\":[\"ATVPDKIKX0DER\"]}";
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), str);
        Request.Builder requestBuilder = new Request.Builder().url(proxyRequest.getRequestLine().getUri()).method("POST", requestBody);
        for (Header headerInfo : proxyRequest.getAllHeaders()) {
            requestBuilder.addHeader(headerInfo.getName(), headerInfo.getValue());
        }
        Request request = requestBuilder.build();

        // request进行第一次转换

        request = awsSigV4Signer.sign(request);
        proxyRequest.addHeader("content-type", "application/json");
        proxyRequest.addHeader("x-amz-date", request.header("x-amz-date"));
        proxyRequest.addHeader("authorization", request.header("authorization"));
    }

    /**
     * Copy a request header from the servlet client to the proxy request.
     * This is easily overridden to filter out certain headers if desired.
     */
    protected void copyRequestHeader(HttpServletRequest servletRequest, HttpRequest proxyRequest, String headerName) {
        // Instead the content-length is effectively set via InputStreamEntity
        if (headerName.equalsIgnoreCase(HttpHeaders.CONTENT_LENGTH)) {
            return;
        }
        if (hopByHopHeaders.containsHeader(headerName)) {
            return;
        }

        @SuppressWarnings("unchecked")
        Enumeration<String> headers = servletRequest.getHeaders(headerName);
        while (headers.hasMoreElements()) {// sometimes more than one value
            String headerValue = headers.nextElement();
            // In case the proxy host is running multiple virtual servers,
            // rewrite the Host header to ensure that we get content from
            // the correct virtual server
            if (!doPreserveHost && headerName.equalsIgnoreCase(HttpHeaders.HOST)) {
                HttpHost host = getTargetHost(servletRequest);
                headerValue = host.getHostName();
                if (host.getPort() != -1) {
                    headerValue += ":" + host.getPort();
                }
            } else if (!doPreserveCookies && headerName.equalsIgnoreCase(org.apache.http.cookie.SM.COOKIE)) {
                headerValue = getRealCookie(headerValue);
            }
            proxyRequest.addHeader(headerName, headerValue);
        }
    }

    private void setXForwardedForHeader(HttpServletRequest servletRequest, HttpRequest proxyRequest) {
        if (doForwardIP) {
            String forHeaderName = "X-Forwarded-For";
            String forHeader = servletRequest.getRemoteAddr();
            String existingForHeader = servletRequest.getHeader(forHeaderName);
            if (existingForHeader != null) {
                forHeader = existingForHeader + ", " + forHeader;
            }
            proxyRequest.setHeader(forHeaderName, forHeader);

            String protoHeaderName = "X-Forwarded-Proto";
            String protoHeader = servletRequest.getScheme();
            proxyRequest.setHeader(protoHeaderName, protoHeader);
        }
    }

    /**
     * Copy proxied response headers back to the servlet client.
     */
    protected void copyResponseHeaders(HttpResponse proxyResponse, HttpServletRequest servletRequest,
                                       HttpServletResponse servletResponse) {
        for (Header header : proxyResponse.getAllHeaders()) {
            copyResponseHeader(servletRequest, servletResponse, header);
        }
    }

    /**
     * Copy a proxied response header back to the servlet client.
     * This is easily overwritten to filter out certain headers if desired.
     */
    protected void copyResponseHeader(HttpServletRequest servletRequest, HttpServletResponse servletResponse,
                                      Header header) {
        String headerName = header.getName();
        if (hopByHopHeaders.containsHeader(headerName)) {
            return;
        }
        String headerValue = header.getValue();
        if (headerName.equalsIgnoreCase(org.apache.http.cookie.SM.SET_COOKIE)
                || headerName.equalsIgnoreCase(org.apache.http.cookie.SM.SET_COOKIE2)) {
            copyProxyCookie(servletRequest, servletResponse, headerValue);
        } else if (headerName.equalsIgnoreCase(HttpHeaders.LOCATION)) {
            // LOCATION Header may have to be rewritten.
            servletResponse.addHeader(headerName, rewriteUrlFromResponse(servletRequest, headerValue));
        } else {
            servletResponse.addHeader(headerName, headerValue);
        }
    }

    /**
     * Copy cookie from the proxy to the servlet client.
     * Replaces cookie path to local path and renames cookie to avoid collisions.
     */
    protected void copyProxyCookie(HttpServletRequest servletRequest, HttpServletResponse servletResponse,
                                   String headerValue) {
        // build path for resulting cookie
        String path = servletRequest.getContextPath(); // path starts with / or
        // is empty string
        path += servletRequest.getServletPath(); // servlet path starts with /
        // or is empty string
        if (path.isEmpty()) {
            path = "/";
        }

        for (HttpCookie cookie : HttpCookie.parse(headerValue)) {
            // set cookie name prefixed w/ a proxy value so it won't collide w/
            // other cookies
            String proxyCookieName = doPreserveCookies ? cookie.getName()
                    : getCookieNamePrefix(cookie.getName()) + cookie.getName();
            Cookie servletCookie = new Cookie(proxyCookieName, cookie.getValue());
            servletCookie.setComment(cookie.getComment());
            servletCookie.setMaxAge((int) cookie.getMaxAge());
            servletCookie.setPath(path); // set to the path of the proxy servlet
            // don't set cookie domain
            servletCookie.setSecure(cookie.getSecure());
            servletCookie.setVersion(cookie.getVersion());
            servletCookie.setHttpOnly(cookie.isHttpOnly());
            servletResponse.addCookie(servletCookie);
        }
    }

    /**
     * Take any client cookies that were originally from the proxy and prepare them to send to the
     * proxy.  This relies on cookie headers being set correctly according to RFC 6265 Sec 5.4.
     * This also blocks any local cookies from being sent to the proxy.
     */
    protected String getRealCookie(String cookieValue) {
        StringBuilder escapedCookie = new StringBuilder();
        String cookies[] = cookieValue.split("[;,]");
        for (String cookie : cookies) {
            String cookieSplit[] = cookie.split("=");
            if (cookieSplit.length == 2) {
                String cookieName = cookieSplit[0].trim();
                if (cookieName.startsWith(getCookieNamePrefix(cookieName))) {
                    cookieName = cookieName.substring(getCookieNamePrefix(cookieName).length());
                    if (escapedCookie.length() > 0) {
                        escapedCookie.append("; ");
                    }
                    escapedCookie.append(cookieName).append("=").append(cookieSplit[1].trim());
                }
            }
        }
        return escapedCookie.toString();
    }

    /**
     * The string prefixing rewritten cookies.
     */
    protected String getCookieNamePrefix(String name) {
        return "!Proxy!" + getServletConfig().getServletName();
    }

    /**
     * Copy response body data (the entity) from the proxy to the servlet client.
     */
    protected void copyResponseEntity(HttpResponse proxyResponse, HttpServletResponse servletResponse,
                                      HttpRequest proxyRequest, HttpServletRequest servletRequest) throws IOException {
        HttpEntity entity = proxyResponse.getEntity();
        if (entity != null) {
            OutputStream servletOutputStream = servletResponse.getOutputStream();
            entity.writeTo(servletOutputStream);
        }
    }

    /**
     * Reads the request URI from {@code servletRequest} and rewrites it, considering targetUri.
     * It's used to make the new request.
     */
    protected String rewriteUrlFromRequest(HttpServletRequest servletRequest) {
        StringBuilder uri = new StringBuilder(1000);
        uri.append(getTargetUri(servletRequest));
        // Handle the path given to the servlet
        String pathInfo = rewritePathInfoFromRequest(servletRequest);
        if (pathInfo != null) {// ex: /my/path.html
            // getPathInfo() returns decoded string, so we need encodeUriQuery
            // to encode "%" characters
            uri.append(encodeUriQuery(pathInfo, true));
        }
        // Handle the query string & fragment
        String queryString = servletRequest.getQueryString();// ex:(following
        // '?'):
        // name=value&foo=bar#fragment
        String fragment = null;
        // split off fragment from queryString, updating queryString if found
        if (queryString != null) {
            int fragIdx = queryString.indexOf('#');
            if (fragIdx >= 0) {
                fragment = queryString.substring(fragIdx + 1);
                queryString = queryString.substring(0, fragIdx);
            }
        }

        queryString = rewriteQueryStringFromRequest(servletRequest, queryString);
        if (queryString != null && queryString.length() > 0) {
            uri.append('?');
            // queryString is not decoded, so we need encodeUriQuery not to
            // encode "%" characters, to avoid double-encoding
            uri.append(encodeUriQuery(queryString, false));
        }

        if (doSendUrlFragment && fragment != null) {
            uri.append('#');
            // fragment is not decoded, so we need encodeUriQuery not to encode
            // "%" characters, to avoid double-encoding
            uri.append(encodeUriQuery(fragment, false));
        }
        return uri.toString();
    }

    protected String rewriteQueryStringFromRequest(HttpServletRequest servletRequest, String queryString) {
        return queryString;
    }

    /**
     * Allow overrides of {@link HttpServletRequest#getPathInfo()}.
     * Useful when url-pattern of servlet-mapping (web.xml) requires manipulation.
     */
    protected String rewritePathInfoFromRequest(HttpServletRequest servletRequest) {
        return servletRequest.getPathInfo();
    }

    /**
     * For a redirect response from the target server, this translates {@code theUrl} to redirect to
     * and translates it to one the original client can use.
     */
    protected String rewriteUrlFromResponse(HttpServletRequest servletRequest, String theUrl) {
        // TODO document example paths
        final String targetUri = getTargetUri(servletRequest);
        if (theUrl.startsWith(targetUri)) {
            /*-
             * The URL points back to the back-end server.
             * Instead of returning it verbatim we replace the target path with our
             * source path in a way that should instruct the original client to
             * request the URL pointed through this Proxy.
             * We do this by taking the current request and rewriting the path part
             * using this servlet's absolute path and the path from the returned URL
             * after the base target URL.
             */
            StringBuffer curUrl = servletRequest.getRequestURL();// no query
            int pos;
            // Skip the protocol part
            if ((pos = curUrl.indexOf("://")) >= 0) {
                // Skip the authority part
                // + 3 to skip the separator between protocol and authority
                if ((pos = curUrl.indexOf("/", pos + 3)) >= 0) {
                    // Trim everything after the authority part.
                    curUrl.setLength(pos);
                }
            }
            // Context path starts with a / if it is not blank
            curUrl.append(servletRequest.getContextPath());
            // Servlet path starts with a / if it is not blank
            curUrl.append(servletRequest.getServletPath());
            curUrl.append(theUrl, targetUri.length(), theUrl.length());
            return curUrl.toString();
        }
        return theUrl;
    }

    /**
     * The target URI as configured. Not null.
     */
    public String getTargetUri() {
        return targetUri;
    }

    /**
     * Encodes characters in the query or fragment part of the URI.
     *
     * <p>Unfortunately, an incoming URI sometimes has characters disallowed by the spec.  HttpClient
     * insists that the outgoing proxied request has a valid URI because it uses Java's {@link URI}.
     * To be more forgiving, we must escape the problematic characters.  See the URI class for the
     * spec.
     *
     * @param in            example: name=value&amp;foo=bar#fragment
     * @param encodePercent determine whether percent characters need to be encoded
     */
    protected static CharSequence encodeUriQuery(CharSequence in, boolean encodePercent) {
        // Note that I can't simply use URI.java to encode because it will
        // escape pre-existing escaped things.
        StringBuilder outBuf = null;
        Formatter formatter = null;
        for (int i = 0; i < in.length(); i++) {
            char c = in.charAt(i);
            boolean escape = true;
            if (c < 128) {
                if (asciiQueryChars.get((int) c) && !(encodePercent && c == '%')) {
                    escape = false;
                }
            } else if (!Character.isISOControl(c) && !Character.isSpaceChar(c)) {// not-ascii
                escape = false;
            }
            if (!escape) {
                if (outBuf != null) {
                    outBuf.append(c);
                }
            } else {
                // escape
                if (outBuf == null) {
                    outBuf = new StringBuilder(in.length() + 5 * 3);
                    outBuf.append(in, 0, i);
                    formatter = new Formatter(outBuf);
                }
                // leading %, 0 padded, width 2, capital hex
                formatter.format("%%%02X", (int) c);
            }
        }
        return outBuf != null ? outBuf : in;
    }

    protected static final BitSet asciiQueryChars;

    static {
        char[] c_unreserved = "_-!.~'()*".toCharArray();// plus alphanum
        char[] c_punct = ",;:$&+=".toCharArray();
        char[] c_reserved = "?/[]@".toCharArray();// plus punct

        asciiQueryChars = new BitSet(128);
        for (char c = 'a'; c <= 'z'; c++) {
            asciiQueryChars.set((int) c);
        }
        for (char c = 'A'; c <= 'Z'; c++) {
            asciiQueryChars.set((int) c);
        }
        for (char c = '0'; c <= '9'; c++) {
            asciiQueryChars.set((int) c);
        }
        for (char c : c_unreserved) {
            asciiQueryChars.set((int) c);
        }
        for (char c : c_punct) {
            asciiQueryChars.set((int) c);
        }
        for (char c : c_reserved) {
            asciiQueryChars.set((int) c);
        }

        asciiQueryChars.set((int) '%');// leave existing percent escapes in
        // place
    }
}
