/**  
 * Copyright © 2018zx tec. All rights reserved.
 */

package com.zixun.spapi.proxy.utils;

/**
 * TODO
 * <AUTHOR>
 * @date 2018年12月26日
 * @version 1.0
 *
 * operation
 * date         operator            content
 * 2018年12月26日      zx             create
 * 
 */
public class Constant {
    public static final String MAIN_URL = "https://mws.amazonservices.in";

    public static final String AWS_ACCESS_KEY = "********************";

    public static final String AWS_SECRET_KEY = "jJn27l5P/EFklfl4baKGyhrVe8OVEPl9MIEfaQCP";

    public static final String APP_NAME = "kuniao-zg";

    public static final String APP_VERSION = "1.0.0";

    public static final String MAIN_URL_US = "https://mws.amazonservices.com/";

    public static final String AWS_ACCESS_KEY_US = "********************";

    public static final String AWS_SECRET_KEY_US = "jJn27l5P/EFklfl4baKGyhrVe8OVEPl9MIEfaQCP";

    public static final String SP_API_CLIENT_ID = "amzn1.application-oa2-client.228b4eecd9cb4d2a80e9745abb39d8c5";

    public static final String SP_API_CLIENT_SECRET = "5f233d108585576b2ee5023760ff0356496773def5a6cd6b8f2e0e51ba726e3e";

    public static final String SP_API_ACCESS_KEY_ID = "********************";

    public static final String SP_API_SECRET_KEY_ID = "+3VaPZJC6LxqUovVSrwJOwEvR6bX3SZkV08BLh+k";

    public static final String SP_API_URL_NA = "https://sellingpartnerapi-na.amazon.com";

    public static final String SP_API_URL_REGION_NA = "us-east-1";

    public static final String SP_API_URL_EU = "https://sellingpartnerapi-eu.amazon.com";

    public static final String SP_API_URL_REGION_EU = "eu-west-1";

    public static final String SP_API_URL_FE = "https://sellingpartnerapi-fe.amazon.com";

    public static final String SP_API_URL_REGION_FE = "us-west-2";

    public static final String SP_API_MODULE = "aplus,authorization,catalog,fba,feeds,finances,mfn,messaging,notifications,orders,products,reports,sales,sellers,service,shipping,solicitations,tokens,uploads,auth";
}
