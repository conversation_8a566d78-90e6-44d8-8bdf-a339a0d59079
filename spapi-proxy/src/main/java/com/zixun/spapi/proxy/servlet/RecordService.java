package com.zixun.spapi.proxy.servlet;

import com.zixun.spapi.proxy.domain.entity.SpApiMarketplace;
import com.zixun.spapi.proxy.domain.repository.SpApiMarketplaceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecordService {

    private final SpApiMarketplaceRepository spApiMarketplaceRepository;

    @Async
    public void recordMarketplace(Long companyId, String marketplaceIds) {
        log.info("记录店铺, companyId:{}, marketplaceIds:{}", companyId, marketplaceIds);
        List<String> marketplaceIdList = Arrays.asList(marketplaceIds.split("[,，]"));
        marketplaceIdList.forEach(marketplaceId -> {
            if (spApiMarketplaceRepository.existsByCompanyIdAndMarketplaceId(companyId, marketplaceId)) {
                log.info("店铺已存在, companyId:{}, marketplaceId:{}", companyId, marketplaceId);
                return;
            }
            SpApiMarketplace spApiMarketplace = new SpApiMarketplace();
            spApiMarketplace.setCompanyId(companyId);
            spApiMarketplace.setMarketplaceId(marketplaceId);
            spApiMarketplaceRepository.save(spApiMarketplace);
        });
    }
}
