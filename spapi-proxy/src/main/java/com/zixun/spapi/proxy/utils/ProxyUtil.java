
package com.zixun.spapi.proxy.utils;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;

import com.zixun.spapi.proxy.config.SpapiConfig;
import com.zixun.spapi.proxy.servlet.RequestBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProxyUtil {

    @Autowired
    private SpapiConfig spapiConfig;

    @Value("${testIpList}")
    private String testIpList;


    public String getSpApiSettingInfo(String keyStr) throws Exception {
        return spapiConfig.getSpApiSettingInfo(keyStr);
    }


    public RequestBuilder createOkHttpRequest(HttpServletRequest servletRequest, String region) throws Exception {
        String awsUrl = "";

        if ("NA".equalsIgnoreCase(region)) {
            awsUrl = getSpApiSettingInfo("url_na");
        } else if ("EU".equalsIgnoreCase(region)) {
            awsUrl = getSpApiSettingInfo("url_eu");
        } else if ("FE".equalsIgnoreCase(region)) {
            awsUrl = getSpApiSettingInfo("url_fe");
        } else if ("CN".equalsIgnoreCase(region)) {
            awsUrl = getSpApiSettingInfo("url_cn");
        }

        String method = servletRequest.getMethod();
        String url = awsUrl + servletRequest.getRequestURI().substring(servletRequest.getRequestURI().indexOf("/") + 3);
        if (StringUtils.isNotBlank(servletRequest.getQueryString())) {
            url = url + "?" + servletRequest.getQueryString();
        }

        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {
            if (servletRequest.getHeader("content-type") != null &&
                    !servletRequest.getHeader("content-type").contains("x-www-form-urlencoded")) {
                ServletInputStream servletInputStream = servletRequest.getInputStream();
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = servletInputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String str = result.toString(StandardCharsets.UTF_8.name());
//                // 使用 ObjectMapper 生成紧凑的 JSON 字符串
//                ObjectMapper mapper = new ObjectMapper();
//                String compactJsonStr = mapper.writeValueAsString(mapper.readValue(str, Map.class));
//
//                // 打印请求参数
//                log.info("POST request parameters: {}", compactJsonStr);
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), str);
                return new RequestBuilder((new Request.Builder()).url(url).method(method, requestBody), str);
            }
            return null;
        }
        if (method.equalsIgnoreCase("POST") || method.equalsIgnoreCase("PUT")) {
            RequestBody requestBody = RequestBody.create(null, new byte[0]);
            return new RequestBuilder((new Request.Builder()).url(url).method(method, requestBody), null);
        }
        return new RequestBuilder((new Request.Builder()).url(url).method(method, null), null);
    }


    public Request createHeader(Request.Builder requestBuilder, HttpServletRequest httpRequest, String regionCode, String ipAddress) throws Exception {
        Enumeration<String> enumerationOfHeaderNames = httpRequest.getHeaderNames();
        while (enumerationOfHeaderNames.hasMoreElements()) {
            String headerName = (enumerationOfHeaderNames.nextElement()).toLowerCase();
            if ("accept".equals(headerName) || "user-agent".equals(headerName) || "x-amz-access-token".equals(headerName)) {
                requestBuilder.addHeader(headerName, httpRequest.getHeader(headerName));
                continue;
            }
            if ("content-type".equals(headerName)) {
                String[] values = httpRequest.getHeader(headerName).split(";");
                requestBuilder.addHeader("Content-Type", values[0]);
            }
        }

        String region = "";
        String accessKey = "";
        String secretKey = "";

        if ("NA".equalsIgnoreCase(regionCode)) {
            region = getSpApiSettingInfo("region_na");
            accessKey = getSpApiSettingInfo("access_key");
            secretKey = getSpApiSettingInfo("secret_key");
        } else if ("EU".equalsIgnoreCase(regionCode)) {
            region = getSpApiSettingInfo("region_eu");
            accessKey = getSpApiSettingInfo("access_key");
            secretKey = getSpApiSettingInfo("secret_key");
        } else if ("FE".equalsIgnoreCase(regionCode)) {
            region = getSpApiSettingInfo("region_fe");
            accessKey = getSpApiSettingInfo("access_key");
            secretKey = getSpApiSettingInfo("secret_key");
        } else if ("CN".equalsIgnoreCase(regionCode)) {
            region = getSpApiSettingInfo("region_cn");
            accessKey = getSpApiSettingInfo("access_key_cn");
            secretKey = getSpApiSettingInfo("secret_key_cn");
        }

        if (StringUtils.isNotBlank(testIpList)) {
            List<String> ipLists = Arrays.asList(testIpList.split(","));
            //如果是测试IP需要用测试应用的aksk
            if (ipLists.contains(ipAddress)) {
                accessKey = getSpApiSettingInfo("access_key_test");
                secretKey = getSpApiSettingInfo("secret_key_test");
            }
        }

        AWSAuthenticationCredentials awsAuthenticationCredentials = new AWSAuthenticationCredentials(accessKey, secretKey, region);

        AWSSigV4Signer awsSigV4Signer = new AWSSigV4Signer(awsAuthenticationCredentials);

        Request request = requestBuilder.build();

        request = awsSigV4Signer.sign(request);
        return request;
    }


    public Request.Builder createTokenOkHttpRequest(HttpServletRequest servletRequest, String region, String ipAddress) throws Exception {
        String url = "";
        String awsUrl = "";

        String clientId = getSpApiSettingInfo("client_id");
        String clientSecret = getSpApiSettingInfo("client_secret");

        if (StringUtils.isNotBlank(testIpList)) {
            List<String> ipLists = Arrays.asList(testIpList.split(","));
            //如果是测试IP需要用测试应用的aksk
            if (ipLists.contains(ipAddress)) {
                clientId = getSpApiSettingInfo("client_id_test");
                clientSecret = getSpApiSettingInfo("client_secret_test");
            }
        }

        if ("CN".equalsIgnoreCase(region)) {
            awsUrl = getSpApiSettingInfo("url_cn");
            clientId = getSpApiSettingInfo("client_id_cn");
            clientSecret = getSpApiSettingInfo("client_secret_cn");
        }

        if (servletRequest.getRequestURI().contains("/auth/o2/token")) {
            url = "https://api.amazon.com/auth/o2/token";
        } else {
            if ("NA".equalsIgnoreCase(region)) {
                awsUrl = getSpApiSettingInfo("url_na");
            } else if ("EU".equalsIgnoreCase(region)) {
                awsUrl = getSpApiSettingInfo("url_eu");
            } else if ("FE".equalsIgnoreCase(region)) {
                awsUrl = getSpApiSettingInfo("url_fe");
            }
            url = awsUrl + servletRequest.getRequestURI();
            if (StringUtils.isNotBlank(servletRequest.getQueryString())) {
                url = url + "?" + servletRequest.getQueryString();
            }
        }
        String method = servletRequest.getMethod();

        if ((servletRequest.getHeader("Content-Length") != null && Integer.parseInt(servletRequest.getHeader("Content-Length")) > 0)
                || servletRequest.getHeader("Transfer-Encoding") != null) {

            ServletInputStream servletInputStream = servletRequest.getInputStream();
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = servletInputStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            String str = result.toString(StandardCharsets.UTF_8.name());
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> map = (Map) mapper.readValue(str, Map.class);
            map.put("client_id", clientId);
            map.put("client_secret", clientSecret);
            String jsonStr = mapper.writeValueAsString(map);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), jsonStr);
            return (new Request.Builder()).url(url).method(method, requestBody);
        }

        if (method.equalsIgnoreCase("POST")) {
            RequestBody requestBody = RequestBody.create(null, new byte[0]);
            return (new Request.Builder()).url(url).method(method, requestBody);
        }
        return (new Request.Builder()).url(url).method(method, null);
    }
}
