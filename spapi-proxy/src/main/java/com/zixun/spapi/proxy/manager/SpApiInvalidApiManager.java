package com.zixun.spapi.proxy.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.zixun.spapi.proxy.dto.SpApiInvalidApiDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
public class SpApiInvalidApiManager {

    private static final CopyOnWriteArrayList<String> invalidApiList = new CopyOnWriteArrayList<>();
    private static final CopyOnWriteArrayList<String> invalidAntApiList = new CopyOnWriteArrayList<>();

    private static final SpApiCompanyPropertyListener LISTENER;
    private static DataProperty<SpApiInvalidApiDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SpApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
    }

    public static void register2Property(DataProperty<SpApiInvalidApiDTO> property) {
        synchronized (LISTENER) {
            log.info("[SpApiInvalidApiManager] Registering new property to sp-api invalidApi manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SpApiCompanyPropertyListener implements PropertyListener<SpApiInvalidApiDTO> {

        private String formatApi(SpApiInvalidApiDTO value) {
            return value.getMethod() + ":" + value.getApi();
        }

        private void loadData(SpApiInvalidApiDTO value) {
            if (value.getType() == 1) {
                invalidApiList.add(this.formatApi(value));
                log.info("新增数据, 当前无效的api列表:{}", JSON.toJSONString(invalidApiList));
            } else if (value.getType() == 2) {
                invalidAntApiList.add(this.formatApi(value));
                log.info("[ant]新增数据, 当前无效的ant匹配api列表:{}", JSON.toJSONString(invalidAntApiList));
            }
        }
        private void removeData(SpApiInvalidApiDTO oldValue) {
            if (oldValue.getType() == 1) {
                invalidApiList.remove(this.formatApi(oldValue));
                log.info("移除数据, 当前无效的api列表:{}", JSON.toJSONString(invalidApiList));
            } else if (oldValue.getType() == 2) {
                invalidAntApiList.remove(this.formatApi(oldValue));
                log.info("[ant]移除数据, 当前无效的ant匹配api列表:{}", JSON.toJSONString(invalidAntApiList));
            }
        }

        @Override
        public synchronized void configUpdate(SpApiInvalidApiDTO oldValue, SpApiInvalidApiDTO value) {
            // 更新数据
            log.info("[SpApiInvalidApiManager] received: {}", value);
            this.removeData(oldValue);
            this.loadData(value);
        }

        @Override
        public void configLoad(SpApiInvalidApiDTO value) {
            // 加载数据
            log.info("[SpApiInvalidApiManager] loaded: {}", value);
            this.loadData(value);
        }

        @Override
        public void configDelete(SpApiInvalidApiDTO oldValue) {
            // 删除数据
            log.info("[SpApiInvalidApiManager] deleted: {}", oldValue);
            this.removeData(oldValue);
        }
    }

    public static List<String> getInvalidApiList() {
        return invalidApiList;
    }

    public static List<String> getInvalidAntApiList() {
        return invalidAntApiList;
    }
}
