package com.zixun.spapi.proxy.utils;

/**
 * AWSAuthenticationCredentials
 */
public class AWSAuthenticationCredentials {

    /**
     * AWS IAM User Access Key Id
     */
    private String accessKeyId;

    /**
     * AWS IAM User Secret Key
     */
    private String secretKey;

    /**
     * AWS Region
     */
    private String region;

    /**
     * 初始化
     *
     * @param accessKeyId
     * @param secretKey
     * @param region
     */
    public AWSAuthenticationCredentials(String accessKeyId, String secretKey, String region) {
        this.accessKeyId = accessKeyId;
        this.secretKey = secretKey;
        this.region = region;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
}
