package com.zixun.spapi.proxy.init;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.BaseApolloDataSource;
import com.fzzixun.appstore.framework.datasource.apollo.datasource.namespace.NamespaceApolloDataSource;
import com.zixun.spapi.proxy.dto.SpApiCompanyDTO;
import com.zixun.spapi.proxy.dto.SpApiInvalidApiDTO;
import com.zixun.spapi.proxy.dto.SpApiIpDTO;
import com.zixun.spapi.proxy.manager.SpApiCompanyManager;
import com.zixun.spapi.proxy.manager.SpApiInvalidApiManager;
import com.zixun.spapi.proxy.manager.SpApiWhiteIpManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApolloDatasourceInit implements ApplicationRunner {

    @Value("${micro.framework.datasource.apollo.sp-api-company.namespace-name:a1-02-appstore.sp-api-company}")
    String spApiCompanyNamespaceName;

    @Value("${micro.framework.datasource.apollo.sp-api-white-ip.namespace-name:a1-02-appstore.sp-api-white-ip}")
    String spApiIpNamespaceName;

    @Value("${micro.framework.datasource.apollo.sp-api-invalid-api.namespace-name:a1-02-appstore.sp-api-invalidApi}")
    String spApiInvalidApiNamespaceName;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        BaseApolloDataSource<String, SpApiCompanyDTO> spApiCompanyApolloDataSource = new NamespaceApolloDataSource<>(spApiCompanyNamespaceName,
                source -> JSON.parseObject(source, SpApiCompanyDTO.class));
        SpApiCompanyManager.register2Property(spApiCompanyApolloDataSource.getProperty());
        log.info("Apollo-SP-API-Company数据初始化");
        spApiCompanyApolloDataSource.initData();

        BaseApolloDataSource<String, SpApiIpDTO> spApiIpApolloDataSource = new NamespaceApolloDataSource<>(spApiIpNamespaceName,
                source -> JSON.parseObject(source, SpApiIpDTO.class));
        SpApiWhiteIpManager.register2Property(spApiIpApolloDataSource.getProperty());
        log.info("Apollo-SP-API-White-IP数据初始化");
        spApiIpApolloDataSource.initData();

        BaseApolloDataSource<String, SpApiInvalidApiDTO> spApiInvalidApiApolloDataSource = new NamespaceApolloDataSource<>(spApiInvalidApiNamespaceName,
                source -> JSON.parseObject(source, SpApiInvalidApiDTO.class));
        SpApiInvalidApiManager.register2Property(spApiInvalidApiApolloDataSource.getProperty());
        log.info("Apollo-SP-API-Invalid-Api数据初始化");
        spApiInvalidApiApolloDataSource.initData();
    }
}
