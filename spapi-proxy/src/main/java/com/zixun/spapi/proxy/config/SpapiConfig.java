package com.zixun.spapi.proxy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/5/7
 */
@Configuration
@ConfigurationProperties(prefix = "spapi.setting")
public class SpapiConfig {
    private Map<String, String> map;

    public String getSpApiSettingInfo(String keyStr) {
        return map.get(keyStr);
    }

    public void setMap(Map<String, String> map) {
        this.map = map;
    }
}
