package com.zixun.spapi.proxy.domain.repository;

import com.zixun.spapi.proxy.domain.entity.SpApiMarketplace;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


public interface SpApiMarketplaceRepository extends JpaRepository<SpApiMarketplace, Long>,
        JpaSpecificationExecutor<SpApiMarketplace> {
    Optional<SpApiMarketplace> findFirstByCompanyIdAndMarketplaceId(Long companyId, String marketplaceId);

    boolean existsByCompanyIdAndMarketplaceId(Long companyId, String marketplaceId);

}