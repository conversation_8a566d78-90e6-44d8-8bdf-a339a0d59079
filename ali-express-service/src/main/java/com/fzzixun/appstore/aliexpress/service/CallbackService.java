package com.fzzixun.appstore.aliexpress.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.aliexpress.common.Constant;
import com.fzzixun.appstore.isvNotify.common.po.NotifyWithUrlPo;
import com.fzzixun.appstore.isvNotify.common.service.NotifyMicroService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CallbackService {

    private final NotifyMicroService notifyMicroService;

    public void sendCallback(String appKey, String callbackUrl, String data, Integer messageType) {
        NotifyWithUrlPo notifyPo = new NotifyWithUrlPo();
        notifyPo.setAppKey(appKey);
        notifyPo.setCompanyId(0L);
        notifyPo.setData(data);
        notifyPo.setMessageType(messageType);
        notifyPo.setCallbackUrl(callbackUrl);
        if (StringUtils.isEmpty(notifyPo.getCallbackUrl())) {
            log.error("未配置回调地址, appKey:{}", appKey);
            return;
        }
        String msgType = messageType.equals(Constant.CALLBACK_MESSAGE_TYPE_TOKEN) ? "Token" : "Event";
        try {
            log.info("推送{}通知, appKey:{}, {}", msgType, appKey, JSON.toJSONString(notifyPo));
            notifyMicroService.sentNotifyWithCallbackUrl(notifyPo);
        } catch (FeignException.BadRequest badRequest) {
            log.error("推送{}通知失败, appKey:{}, {}", msgType, appKey, badRequest.getMessage());
        } catch (Exception e) {
            log.error("推送{}通知异常, appKey:{},", msgType, appKey, e);
        }
    }
}
