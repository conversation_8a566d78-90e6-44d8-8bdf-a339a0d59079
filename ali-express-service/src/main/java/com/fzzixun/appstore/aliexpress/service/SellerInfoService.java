package com.fzzixun.appstore.aliexpress.service;

import com.fzzixun.appstore.aliexpress.dao.entity.AliExpressAuth;
import com.fzzixun.appstore.aliexpress.dao.repository.AliExpressAuthRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SellerInfoService {


    private final AliExpressAuthRepository aliExpressAuthRepository;

    /**
     * 获取商户的回调地址，多条授权记录取最新一条
     *
     * @param sellerId sellerId
     * @return 回调地址
     */
    public List<Map<String, String>> getCallbackUrl(String sellerId) {
        List<Map<String, String>> mapList = new ArrayList<>();
        List<AliExpressAuth> aliExpressAuthList = aliExpressAuthRepository.findBySellerId(sellerId);
        if (aliExpressAuthList != null && !aliExpressAuthList.isEmpty()) {
            //根据渠道分组取最新的回调地址
            Map<String, List<AliExpressAuth>> map = aliExpressAuthList.stream()
                    .collect(Collectors.groupingBy(AliExpressAuth::getChannel));
            map.forEach((k, v) -> {
                Map<String, String> channelUrlMap = new HashMap<>();
                channelUrlMap.put(k, v.get(0).getCallbackUrl());
                mapList.add(channelUrlMap);
            });
        }
        return mapList;
    }

    /**
     * 绑定商户和店铺sellerId
     */
    public void bindMerchantSellerId(String channel, String sellerId, String callbackUrl) {
        AliExpressAuth aliExpressAuth = aliExpressAuthRepository.findBySellerIdAndChannel(sellerId, channel);
        if (aliExpressAuth == null) {
            aliExpressAuth = new AliExpressAuth();
            aliExpressAuth.setChannel(channel);
            aliExpressAuth.setSellerId(sellerId);
            aliExpressAuth.setCallbackUrl(callbackUrl);
            aliExpressAuth.setAuthCnt(1);
        } else {
            aliExpressAuth.setCallbackUrl(callbackUrl);
            aliExpressAuth.setUpdateTime(new Date());
            aliExpressAuth.setAuthCnt(aliExpressAuth.getAuthCnt() + 1);
        }
        aliExpressAuthRepository.save(aliExpressAuth);
    }

    public void addAliExpressAuth(String channel, String sellerId, String callbackUrl) {
        AliExpressAuth aliExpressAuth = new AliExpressAuth();
        aliExpressAuth.setChannel(channel);
        aliExpressAuth.setSellerId(sellerId);
        aliExpressAuth.setCallbackUrl(callbackUrl);
        aliExpressAuth.setAuthCnt(1);
        aliExpressAuthRepository.save(aliExpressAuth);
    }
}
