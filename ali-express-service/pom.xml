<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spapi-auth-proxy</artifactId>
        <groupId>com.fzzixun.appstore.spapi</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ali-express-service</artifactId>

    <groupId>com.fzzixun.appstore.aliexpress</groupId>
    <version>1.0.0-SNAPSHOT</version>
    <name>ali-express-service</name>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <fastjson.version>1.2.83</fastjson.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
    </properties>

    <dependencies>
        <!-- 微服务 -->
        <dependency>
            <groupId>com.fzzixun.appstore.tool</groupId>
            <artifactId>isv-notify-starter</artifactId>
            <version>0.0.4-SNAPSHOT</version>
        </dependency>

        <!-- 基础组件 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>


        <dependency>
            <groupId>com.fzzixun.appstore.aliexpress</groupId>
            <artifactId>ali-express-dao</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.fzzixun.appstore.aliexpress</groupId>
            <artifactId>ali-express-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>