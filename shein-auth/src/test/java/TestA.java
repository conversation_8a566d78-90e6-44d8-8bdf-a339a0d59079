import com.fzzixun.shein.auth.SheinAuthApplication;
import com.fzzixun.shein.auth.producer.KafkaOpt;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("local")
@SpringBootTest(classes = SheinAuthApplication.class)
public class TestA {

    @Autowired
    private KafkaOpt kafkaOpt;

    @Test
    public void test1() {
        kafkaOpt.send("isv_subscribe_info", "test");
    }
}
