server.port=10088
apollo.bootstrap.enabled=true
apollo.meta=http://localhost:8080
management.server.port=19002
logging.level.root=info
logging.level.com.fzzixun.appstore.temu=debug

temu.setting.map.url_cn = https://openapi.kuajingmaihuo.com/openapi/router
temu.setting.map.url_cn_test = https://kj-openapi.temudemo.com/openapi/router
temu.setting.map.url_us = https://openapi-b-us.temu.com/openapi/router
temu.setting.map.url_us_test = http://openapi-b-us.temudemo.com/openapi/router
temu.setting.map.url_eu = https://openapi-b-eu.temu.com/openapi/router
temu.setting.map.url_eu_test = http://openapi-b-eu.temudemo.com/openapi/router
temu.setting.map.app_key_full_managed_cn = 72bc9e4143e960b2134e1cdf22fec651
temu.setting.map.app_secret_full_managed_cn = c54100b5b15d69d5cf0db9e8a653333a60f73c23
temu.setting.map.app_key_semi_managed_cn = 47bb4bb7769e12d9f7aa93cf029fe529
temu.setting.map.app_secret_semi_managed_cn = ac0a3e952eaaa5b19c0e615c2ef497f50afa6e49
temu.setting.map.app_key_semi_managed_us = 4ebbc9190ae410443d65b4c2faca981f
temu.setting.map.app_secret_semi_managed_us = 4782d2d827276688bf4758bed55dbdd4bbe79a79
temu.setting.map.app_key_semi_managed_eu = f860e759073f9d1e5c8bbeb7baac1dbf
temu.setting.map.app_secret_semi_managed_eu = 121eac72693c6e587f7e15ce4721b42da5df2def

ipWhiteList=127.0.0.1,*************,*************

testLimit=false


# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器地址
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=2000

limitStatusCode=429
printResponseLog = true
param.log.debug = true

spring.datasource.url=****************************************************************************************************************************
spring.datasource.username=appstore
spring.datasource.password=PE8eOsDklj$2BADfCv8wr8PM

# Naming strategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.properties.hibernate.max_fetch_depth = 1
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true
#生产者配置
spring.kafka.producer.bootstrap-servers = b-1.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096,b-2.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096
spring.kafka.producer.properties.sasl.mechanism = SCRAM-SHA-512
spring.kafka.producer.properties.security.protocol = SASL_SSL
spring.kafka.producer.properties.sasl.jaas.config = org.apache.kafka.common.security.scram.ScramLoginModule required username="appstore" password="appstore123";
# 批量大小 默认16k
spring.kafka.producer.batch-size = 325000
# 提交延时 默认0
spring.kafka.producer.properties.linger.ms = 5000

#消费者配置
spring.kafka.consumer.bootstrap-servers = b-1.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096,b-2.sbappstore-test-v1.noh9q9.c4.kafka.cn-north-1.amazonaws.com.cn:9096
spring.kafka.consumer.properties.sasl.mechanism = SCRAM-SHA-512
spring.kafka.consumer.properties.security.protocol = SASL_SSL
spring.kafka.consumer.properties.sasl.jaas.config = org.apache.kafka.common.security.scram.ScramLoginModule required username="appstore" password="appstore123";
#spring.kafka.consumer.group-id=
spring.kafka.consumer.auto-offset-reset = earliest
spring.kafka.consumer.enable-auto-commit = false
spring.kafka.consumer.auto-commit-interval = 1000
spring.kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.max-poll-records = 500
#如果没有足够的数据立即满足“fetch.min.bytes”给出的要求，服务器在回答获取请求之前将阻塞的最长时间（以毫秒为单位）
#默认值为500
spring.kafka.consumer.fetch-max-wait = 5000
#服务器应以字节为单位返回获取请求的最小数据量，默认值为1，对应的kafka的参数为fetch.min.bytes。
spring.kafka.consumer.fetch-min-size = 65000
spring.kafka.listener.type = batch
spring.kafka.listener.concurrency = 3

#测试
shein.api.semi-managed.app_id=10ADAAA5CE0008CF3585A106B6AFF
shein.api.semi-managed.app_secret=D70A14A37467468AA4F5B96CE42A61F2
#shein.api.semi-managed.app_id = 131F55652C002A250174D359F9EC0
#shein.api.semi-managed.app_secret = EA56CA7C3BFB477698C30B09429860B5
shein.api.semi-managed.url=https://openapi-test01.sheincorp.cn

shein.api.full-managed.app_id=F0509DD53F86458F983020D9DBD220F8
shein.api.full-managed.app_secret=B7043FE37FEA4DA99824FBB1E7C84169
shein.api.full-managed.url=https://openapi-test01.sheincorp.cn

shein.api.self-operated.app_id=EED6AEEA6B4741EF94D29FED5A1CE76F
shein.api.self-operated.app_secret=35D01D988EBA46FB9D87CA066FFD1805
shein.api.self-operated.url=https://openapi-test01.sheincorp.cn

shein.auth.login.url=https://openapi-sem-test01.dotfashion.cn/sem/supplier/authorizateTempTest
shein.auth.index.url=http://127.0.0.1:10088
shein.auth.finish.url=http://127.0.0.1:10088/rest/v1/auth/finish