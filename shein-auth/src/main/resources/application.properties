spring.profiles.active=local
app.id=shein-proxy
server.port=10087
spring.application.name=shein-auth
# Apollo
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=application
apollo.bootstrap.eagerLoad.enable=true
apollo.autoUpdateInjectedSpringProperties=true

management.endpoints.web.base-path=/status
management.endpoints.web.exposure.include=health
management.server.port=19002
# 数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# jpa
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

spring.task.execution.pool.coreSize=30

# kafka
shein.groupid = openapi-shein-callback
kafka.listener_topics = shein-callback