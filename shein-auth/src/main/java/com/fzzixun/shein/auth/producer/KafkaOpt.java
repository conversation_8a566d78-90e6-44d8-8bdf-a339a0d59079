package com.fzzixun.shein.auth.producer;

import com.fzzixun.shein.auth.config.ParammConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2022/12/2
 */
@Slf4j
@Component
public class KafkaOpt {

    @Autowired
    private KafkaTemplate<Object, String> kafkaTemplate;

    @Autowired
    private ParammConfig paramConfig;

    /**
     * 发送消息
     */
    public void send(String message) {
        this.send(paramConfig.getKafkaTopic(), message);
    }

    public void send(String topic, String message) {
        try {
            if (paramConfig.getLogDebug()) {
                log.info("发送消息:" + topic + "：" + message);
            }
            kafkaTemplate.send(topic, message).addCallback(success -> {
                // 消息发送到的topic
                if (success != null) {
                    String targetTopic = success.getRecordMetadata().topic();
                    // 消息发送到的分区
                    int partition = success.getRecordMetadata().partition();
                    // 消息在分区内的offset
                    long offset = success.getRecordMetadata().offset();
                    if (paramConfig.getLogDebug()) {
                        log.info("发送消息成功:" + targetTopic + "-" + partition + "-" + offset);
                    }
                }
            }, failure -> log.error("发送消息失败:" + failure.getMessage()));
        } catch (Exception e) {
            log.error("kafka生产者异常：{}", e.getMessage(), e);
        }
    }
}
