package com.fzzixun.shein.auth.controller;

import com.fzzixun.proxy.common.infrastructure.annotation.WebLog;
import com.fzzixun.proxy.common.infrastructure.exception.BadRequestException;
import com.fzzixun.proxy.common.vo.ResponseVo;
import com.fzzixun.shein.auth.config.SheinConfig;
import com.fzzixun.shein.auth.facade.enums.ManageType;
import com.fzzixun.shein.auth.facade.po.AuthorizeUrlReq;
import com.fzzixun.shein.auth.service.AuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@WebLog
@RequestMapping("/rest/v1/auth")
@RestController
@Api(tags = "shein授权")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final SheinConfig sheinConfig;

    @GetMapping("/url")
    @ApiOperation("获取授权地址")
    public ResponseVo<String> authUrl(AuthorizeUrlReq req
    ) {
        // 验证参数
        if (StringUtils.isBlank(req.getRedirectUrl())) {
            throw new BadRequestException("缺少必需的参数redirectUrl");
        }
        if (ManageType.getByType(req.getType()) == null) {
            throw new BadRequestException("type参数错误");
        }
        return authService.getAuthUrl(req.getState(), req.getRedirectUrl(), req.getCallbackUrl(), req.getType());
    }

    @GetMapping("/finish")
    @ApiOperation("接收授权结果")
    public void receiveCode(@RequestParam String appid,
                            @RequestParam String tempToken,
                            @RequestParam String state,
                            HttpServletResponse response
    ) throws IOException {
        try {
            if (!sheinConfig.getSelfOperatedAppId().equals(appid)
                    && !sheinConfig.getSemiManagedAppId().equals(appid)
                    && !sheinConfig.getFullManagedAppId().equals(appid)
            ) {
                log.error("appId error");
                response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
                return;
            }
            authService.authCallback(tempToken, state, response);
        } catch (Exception e) {
            log.error("createToken error", e);
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
        }
    }
}
