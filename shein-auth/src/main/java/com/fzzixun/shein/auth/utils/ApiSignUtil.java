package com.fzzixun.shein.auth.utils;

import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class ApiSignUtil {
    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String AND = "&";
    private static final int RANDOM_LENGTH = 5;


    public static String hmacSha256(String message, String secret) {
        String hash = "";
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKey);
            byte[] bytes = mac.doFinal(message.getBytes());
            hash = byteArrayToHexString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("HmacSHA256 error : {}", ExceptionUtils.getStackTrace(e));
        }
        return hash;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String tmp;
        for (int n = 0; b != null && n < b.length; n++) {
            tmp = Integer.toHexString(b[n] & 0XFF);
            if (tmp.length() == 1) {
                hs.append('0');
            }
            hs.append(tmp);
        }
        return hs.toString().toLowerCase();
    }

    public static String signature(String apiKey, String timestamp, String requestPath, String secret, String randomKey) {
        String signString = apiKey + AND + timestamp + AND + requestPath;
        String secretKey = secret + randomKey;
        String hashValue = hmacSha256(signString, secretKey);
        return Base64.encode(hashValue);
    }

    public static String createSignature(String loginName, String timestamp, String requestPath, String secret, String randomKey) {
        return randomKey + signature(loginName, timestamp, requestPath, secret, randomKey);
    }


    public static boolean verifySign(String signature, String openKey, String secretKey, String requestPath, String timestamp) {
        String randomKey = signature.substring(0, RANDOM_LENGTH);
        String base64Value = createSignature(openKey, timestamp, requestPath, secretKey, randomKey);
        log.info("验证签名字段打印：{},{},{}---sign：{}", openKey, timestamp, requestPath, base64Value);
        if (signature.equals(base64Value)) {
            return true;
        }
        return false;
    }

}