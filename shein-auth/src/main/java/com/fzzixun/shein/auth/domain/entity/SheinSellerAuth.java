package com.fzzixun.shein.auth.domain.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "shein_seller_auth")
public class SheinSellerAuth {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "open_key_id")
    private String openKeyId;

    @Column(name = "encrypt_secret_key")
    private String encryptSecretKey;

    @Column(name = "callback_url")
    private String callbackUrl;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @PrePersist
    protected void prePersist() {
        if (this.createTime == null) {
            createTime = new Date();
        }
        if (this.updateTime == null) {
            updateTime = new Date();
        }
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = new Date();
    }

    @PreRemove
    protected void preRemove() {
        this.updateTime = new Date();
    }
}
