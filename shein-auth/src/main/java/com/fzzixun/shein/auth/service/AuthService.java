package com.fzzixun.shein.auth.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;


import com.fzzixun.proxy.common.infrastructure.exception.BadRequestException;
import com.fzzixun.proxy.common.infrastructure.util.MdcUtil;
import com.fzzixun.proxy.common.vo.ResponseVo;
import com.fzzixun.shein.auth.config.SheinConfig;
import com.fzzixun.shein.auth.domain.entity.SheinSellerAuth;
import com.fzzixun.shein.auth.domain.repository.SheinSellerAuthRepository;
import com.fzzixun.shein.auth.dto.AuthResponse;
import com.fzzixun.shein.auth.utils.AESTools;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    @Value("${enabledStateEncrypt:false}")
    private Boolean enabledStateEncrypt;

    @Value("${stateExpiredHours:1}")
    private Integer stateExpiredHours;

    @Value("${illegal.state.alert.token:c49856ae7e115277ff9d434fa0c267425d2eb8f2621b7f18846e6d47b3f2c146}")
    private String illegalStateAlertToken;
    @Value("${param.okhttp.read-timeout:30}")
    private Integer okhttpReadTimeout;

    private final static String STATE_PREFIX = "shein:state:";

    private final static String SHEIN_OPEN_KEY_PREFIX = "shein:openKey:";


    private final RedisTemplate<String, Object> redisTemplate;

    private final SheinConfig sheinConfig;

    private final SheinSellerAuthRepository sheinSellerAuthRepository;

    /**
     * 获取授权地址
     * @param state 进入重定向页面，该参数将原样返回
     * @param redirectUrl 调用方系统回调地址
     * @param type 店铺类型 预留
     * @return
     */
    public ResponseVo<String> getAuthUrl(String state, String redirectUrl, String callbackUrl, String type) {
        if (StringUtils.isBlank(state)) {
            state = UUID.randomUUID().toString().replaceAll("-", "");
        }
        String stateEncode = IdUtil.fastSimpleUUID();
        JSONObject params = new JSONObject();
        params.put("state", state);
        params.put("redirectUrl", redirectUrl);
        params.put("callbackUrl", callbackUrl);
        params.put("type", type);

        // stateKey存入redis,encryptBase64为值，默认1小时过期
        String stateKey = STATE_PREFIX + stateEncode;
        redisTemplate.opsForValue().set(stateKey, params.toJSONString(), stateExpiredHours, TimeUnit.HOURS);

        String appId;
        if ("semi-managed".equalsIgnoreCase(type)) {
            appId = sheinConfig.getSemiManagedAppId();
        } else if ("full-managed".equalsIgnoreCase(type)) {
            appId = sheinConfig.getFullManagedAppId();
        }  else if ("self-operated".equalsIgnoreCase(type)) {
            appId = sheinConfig.getSelfOperatedAppId();
        } else {
            throw new BadRequestException("type参数错误");
        }

        String authUrl = String.format("%s?appid=%s&redirectUrl=%s&state=%s",
                sheinConfig.getLoginUrl(), appId, Base64.encode(sheinConfig.getAuthFinishUrl()), stateEncode);
        ResponseVo<String> responseVo = ResponseVo.ofSuccess(authUrl);
        responseVo.setId(MdcUtil.getRequestId());
        return responseVo;
    }


    public void authCallback(String tempToken, String state, HttpServletResponse response) throws Exception {
        // 判断redis中是否有stateKey
        String stateKey = STATE_PREFIX + state;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(stateKey))) {
            log.error("找不到state，授权失败！{} ", stateKey);
//            DingDingNotifyUtil.sendMsg("【Walmart】找不到state，授权失败！" + state, illegalStateAlertToken);
            //重定向到授权失败页面
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
            return;
        }
        String stateValue;
        try {
            stateValue = (String) redisTemplate.opsForValue().get(stateKey);
            log.info("获取的state：{} ", stateValue);
        } catch (Exception e) {
            log.info("获取state异常！{} ", stateKey);
//            DingDingNotifyUtil.sendMsg("【Walmart】获取state异常，授权失败！" + state, illegalStateAlertToken);
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
            return;
        }
        JSONObject params = JSONObject.parseObject(stateValue);
        String authState = params.getString("state");
        String redirectUrl = params.getString("redirectUrl");
        String callbackUrl = params.getString("callbackUrl");
        String type = params.getString("type");
        String appId;
        String apiUrl;
        String appSecret;
        if ("semi-managed".equalsIgnoreCase(type)) {
            apiUrl = sheinConfig.getSemiManagedApiUrl();
            appId = sheinConfig.getSemiManagedAppId();
            appSecret = sheinConfig.getSemiManagedAppSecret();
        } else if ("full-managed".equalsIgnoreCase(type)) {
            apiUrl = sheinConfig.getFullManagedApiUrl();
            appId = sheinConfig.getFullManagedAppId();
            appSecret = sheinConfig.getFullManagedAppSecret();
        }  else if ("self-operated".equalsIgnoreCase(type)) {
            apiUrl = sheinConfig.getSelfOperatedApiUrl();
            appId = sheinConfig.getSelfOperatedAppId();
            appSecret = sheinConfig.getSelfOperatedAppSecret();
        } else {
            throw new BadRequestException("type参数错误");
        }

        OkHttpClient httpClient = new OkHttpClient().newBuilder().readTimeout(okhttpReadTimeout, TimeUnit.SECONDS).build();

        String requestParamsStr = new JSONObject() {
            {
                put("tempToken", tempToken);
            }
        }.toString();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), requestParamsStr);
        String tokenReqUrl = apiUrl + "/open-api/auth/get-by-token";
        Request request = this.createAuthHeader((new Request.Builder()).url(tokenReqUrl).method("POST", requestBody),
                appId, appSecret);
        Call call = httpClient.newCall(request);
        Response res = call.execute();
        if (!res.isSuccessful()) {
            log.error("获取token失败！");
            //重定向到授权失败页面
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
            return;
        }
        String responseStr = res.body().string();
        AuthResponse authResponse = JSON.parseObject(responseStr, AuthResponse.class);
        if (!authResponse.isSuccess()) {
            log.error("获取token失败！{} ", responseStr);
            //重定向到授权失败页面
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
            return;
        }
        String secretKey = authResponse.getInfo().getSecretKey();
        String decryptSecretKey = AESTools.decrypt(secretKey, appSecret);
        String openKeyId = authResponse.getInfo().getOpenKeyId();

        SheinSellerAuth sheinSellerAuth = sheinSellerAuthRepository.findFirstByOpenKeyId(openKeyId).orElse(new SheinSellerAuth());
        sheinSellerAuth.setOpenKeyId(openKeyId);
        sheinSellerAuth.setEncryptSecretKey(secretKey);
        sheinSellerAuth.setAppId(appId);
        if (StringUtils.isNotEmpty(callbackUrl)) {
            sheinSellerAuth.setCallbackUrl(callbackUrl);
        }
//        sheinSellerAuth.setState(stateValue);
        sheinSellerAuthRepository.save(sheinSellerAuth);
        this.cacheSecretKey(appId, type, openKeyId, decryptSecretKey, sheinSellerAuth.getCallbackUrl());
        // 发送参数到外部接口
        try {
            String authStateEncode = URLEncoder.encode(authState, "utf-8");
            String urlParams = "openKeyId=" + openKeyId
                    + "&state=" + authStateEncode;
            if (redirectUrl.contains("?")) {
                redirectUrl += "&" + urlParams;
            } else {
                redirectUrl += "?" + urlParams;
            }
            response.sendRedirect(redirectUrl);
        } catch (Exception ex) {
            log.error(String.format("调用外部接口失败， 接口地址： %s", stateValue), ex);
            //重定向到授权失败页面
            response.sendRedirect(sheinConfig.getAuthIndex() + "/static/shein/auth/failure.html");
        }
    }

    private void cacheSecretKey(String appId, String type, String openKeyId, String decryptSecretKey, String callbackUrl) {
        String openKeyRedisKey = SHEIN_OPEN_KEY_PREFIX + type + ":" + openKeyId;
        JSONObject params = new JSONObject();
        params.put("appId", appId);
        params.put("secretKey", decryptSecretKey);
        params.put("callbackUrl", callbackUrl);
        log.info("缓存openKeyId:[{}]解密后的secretKey", openKeyId);
        redisTemplate.opsForValue().set(openKeyRedisKey, params.toJSONString(), 60 * 60 * 24 * 7, TimeUnit.SECONDS);
    }

    public String getCallbackUrl(String appId, String type, String openKeyId) {
        String openKeyRedisKey = SHEIN_OPEN_KEY_PREFIX + type + ":" + openKeyId;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(openKeyRedisKey))) {
            SheinSellerAuth sheinSellerAuth = sheinSellerAuthRepository.findFirstByOpenKeyId(openKeyId)
                    .orElseThrow(() -> new BadRequestException("openKeyId not found"));
            String decryptSecretKey;
            if (sheinSellerAuth.getAppId() == null) {
                decryptSecretKey = sheinSellerAuth.getEncryptSecretKey();
            } else {
                String appSecret = this.getAppSecretByType(type);
                decryptSecretKey = AESTools.decrypt(sheinSellerAuth.getEncryptSecretKey(), appSecret);
            }
            JSONObject params = new JSONObject();
            params.put("appId", appId);
            params.put("secretKey", decryptSecretKey);
            params.put("callbackUrl", sheinSellerAuth.getCallbackUrl());
            log.info("缓存openKeyId:[{}]解密后的secretKey", openKeyId);
            redisTemplate.opsForValue().set(openKeyRedisKey, params.toJSONString(), 60 * 60 * 24 * 7, TimeUnit.SECONDS);
            return sheinSellerAuth.getCallbackUrl();
        }
        String openKeyValue = (String) redisTemplate.opsForValue().get(openKeyRedisKey);
        JSONObject params = JSONObject.parseObject(openKeyValue);
        return params.getString("callbackUrl");
    }

    private String getAppSecretByType(String type) {
        String appSecret;
        if ("semi-managed".equalsIgnoreCase(type)) {
            appSecret = sheinConfig.getSemiManagedAppSecret();
        } else if ("full-managed".equalsIgnoreCase(type)) {
            appSecret = sheinConfig.getFullManagedAppSecret();
        }  else if ("self-operated".equalsIgnoreCase(type)) {
            appSecret = sheinConfig.getSelfOperatedAppSecret();
        } else {
            throw new BadRequestException("type参数错误");
        }
        return appSecret;
    }


    /**
     * 创建授权请求头，/open-api/auth/get-by-token，用tempToken交换openKeyId和secretKey
     * @param requestBuilder
     * @return
     */
    public Request createAuthHeader(Request.Builder requestBuilder, String appId, String appSecret) {
        requestBuilder.addHeader("Content-Type", "application/json;charset=UTF-8");
        requestBuilder.addHeader("x-lt-appid", appId);
        String timestamp = String.valueOf(System.currentTimeMillis());
        requestBuilder.addHeader("x-lt-timestamp", timestamp); // 时间戳（5分钟内有效）
        String sign = null;
        try {
            sign = generateSheinSignature(appId, appSecret,
                    "/open-api/auth/get-by-token", timestamp, RandomStringUtils.randomAlphanumeric(5));
        } catch (Exception e) {
            log.error("generateSheinSignature error", e);
        }
        requestBuilder.addHeader("x-lt-signature", sign);
        // 支持返回的多语言报错信息，例如：language:en
        // 默认返回中文多语言信息  支持传参：  英语:en  法语: fr  西班牙语:es  德语:de  中文简体:zh-cn  泰语:th  巴西葡语:pt-br 日语:ja
//        requestBuilder.addHeader("language", "");
        return requestBuilder.build();
    }


    /**
     * 生成签名
     * @param openKeyId 店铺授权的openKeyId
     * @param secretKey 店铺授权的解密secretKey
     * @param path  请求接口路径
     * @param timestamp 请求时间戳
     * @param randomKey 5位随机字符串
     * @return
     * @throws Exception
     */
    public static String generateSheinSignature(String openKeyId, String secretKey, String path,
                                                String timestamp, String randomKey) throws Exception {
        // 步骤一：组装签名数据VALUE
        String value = openKeyId + "&" + timestamp + "&" + path;
//        System.out.println("步骤一 - 签名数据VALUE: " + value);

        // 步骤二：组装签名密钥KEY
        String key = secretKey + randomKey;
//        System.out.println("步骤二 - 签名密钥KEY: " + key);

        // 步骤三：HMAC-SHA256计算并转换为十六进制
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hmacResult = mac.doFinal(value.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexStringBuilder = new StringBuilder();
        for (byte b : hmacResult) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexStringBuilder.append('0');
            }
            hexStringBuilder.append(hex);
        }
        String hexSignature = hexStringBuilder.toString();
//        System.out.println("步骤三 - HMAC-SHA256结果(HEX): " + hexSignature);

        // 步骤四：Base64编码
        String base64Signature = java.util.Base64.getEncoder().encodeToString(
                hexSignature.getBytes(StandardCharsets.UTF_8));
//        System.out.println("步骤四 - Base64编码结果: " + base64Signature);

        // 步骤五：拼接RandomKey
        String finalSignature = randomKey + base64Signature;
//        System.out.println("步骤五 - 最终签名: " + finalSignature);

        return finalSignature;
    }

}
