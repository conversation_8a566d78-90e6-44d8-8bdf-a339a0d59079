package com.fzzixun.shein.auth.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.fzzixun.shein.auth.dto.SheinIpDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SheinWhiteIpManager {

    private static final int PROXY_TYPE = 5;

    @Getter
    private static final Map<String, SheinIpDTO> whiteIpMap = new ConcurrentHashMap<>();

    private static final SpApiCompanyPropertyListener LISTENER;
    private static DataProperty<SheinIpDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SpApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
    }

    public static void register2Property(DataProperty<SheinIpDTO> property) {
        synchronized (LISTENER) {
            log.info("[SheinWhiteIpManager] Registering new property to shein ip manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SpApiCompanyPropertyListener implements PropertyListener<SheinIpDTO> {

        private String getKey(SheinIpDTO value) {
            return value.getIp();
        }

        @Override
        public synchronized void configUpdate(SheinIpDTO oldValue, SheinIpDTO value) {
            // 更新数据
            log.info("[SheinWhiteIpManager] received: {}", value);
            if (oldValue.getType() == PROXY_TYPE) {
                whiteIpMap.remove(this.getKey(oldValue));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
            if (value.getType() == PROXY_TYPE) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configLoad(SheinIpDTO value) {
            // 加载数据
            log.info("[SheinWhiteIpManager] loaded: {}", value);
            if (value.getType() == PROXY_TYPE) {
                whiteIpMap.put(this.getKey(value), value);
                log.info("新增数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }

        @Override
        public void configDelete(SheinIpDTO value) {
            // 删除数据
            log.info("[SheinWhiteIpManager] deleted: {}", value);
            if (value.getType() == PROXY_TYPE) {
                whiteIpMap.remove(this.getKey(value));
                log.info("移除数据, 当前ip白名单列表:{}", JSON.toJSONString(whiteIpMap));
            }
        }
    }

}
