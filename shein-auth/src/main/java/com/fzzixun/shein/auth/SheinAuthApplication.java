package com.fzzixun.shein.auth;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.fzzixun.proxy.common.ProxyCommonConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@ServletComponentScan
@EnableApolloConfig
@EnableAsync
@SpringBootApplication(scanBasePackageClasses = {SheinAuthApplication.class, ProxyCommonConfig.class})
public class SheinAuthApplication {
    public static void main(String[] args) {
        SpringApplication.run(SheinAuthApplication.class, args);
    }
}

