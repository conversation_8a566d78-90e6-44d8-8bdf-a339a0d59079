package com.fzzixun.shein.auth.manager;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.framework.datasource.apollo.property.DataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.DynamicDataProperty;
import com.fzzixun.appstore.framework.datasource.apollo.property.PropertyListener;
import com.fzzixun.shein.auth.dto.ProxyCompanyDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ProxyCompanyManager {

    @Getter
    private static final Map<Long, ProxyCompanyDTO> companyMap = new ConcurrentHashMap<>();

    private static final SpApiCompanyPropertyListener LISTENER;
    private static DataProperty<ProxyCompanyDTO> currentProperty = new DynamicDataProperty<>();

    static {
        LISTENER = new SpApiCompanyPropertyListener();
        currentProperty.addListener(LISTENER);
    }

    public static void register2Property(DataProperty<ProxyCompanyDTO> property) {
        synchronized (LISTENER) {
            log.info("[SpApiCompanyManager] Registering new property to sp-api company manager");
            currentProperty.removeListener(LISTENER);
            property.addListener(LISTENER);
            currentProperty = property;
        }
    }

    private static class SpApiCompanyPropertyListener implements PropertyListener<ProxyCompanyDTO> {

        private Long getKey(ProxyCompanyDTO value) {
            return value.getCompanyId();
        }

        @Override
        public synchronized void configUpdate(ProxyCompanyDTO oldValue, ProxyCompanyDTO value) {
            // 更新数据
            log.info("[SpApiCompanyManager] received: {}", value);
            companyMap.put(this.getKey(value), value);
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }

        @Override
        public void configLoad(ProxyCompanyDTO value) {
            // 加载数据
            log.info("[SpApiCompanyManager] loaded: {}", value);
            companyMap.put(this.getKey(value), value);
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }

        @Override
        public void configDelete(ProxyCompanyDTO value) {
            // 删除数据
            log.info("[SpApiCompanyManager] deleted: {}", value);
            companyMap.remove(this.getKey(value));
            log.info("当前公司列表:{}", JSON.toJSONString(companyMap));
        }
    }

}
