package com.fzzixun.shein.auth.facade.enums;


import lombok.Getter;

@Getter
public enum ManageType {
    /**
     * 半托管
     */
    SemiManaged("semi-managed"),
    /**
     * 全托管
     */
    FullManaged("full-managed"),
    /**
     * 自运营
     */
    SelfOperated("self-operated");

    private final String type;

    ManageType(String type) {
        this.type = type;
    }

    public static ManageType getByType(String type) {
        for (ManageType manageType : ManageType.values()) {
            if (manageType.getType().equals(type)) {
                return manageType;
            }
        }
        return null;
    }
}
