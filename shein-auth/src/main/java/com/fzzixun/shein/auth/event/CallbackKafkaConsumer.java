package com.fzzixun.shein.auth.event;

import com.alibaba.fastjson.JSON;
import com.fzzixun.appstore.isvNotify.common.po.NotifyWithUrlPo;
import com.fzzixun.appstore.isvNotify.common.service.NotifyMicroService;
import com.fzzixun.proxy.common.common.Constant;
import com.fzzixun.shein.auth.config.ParammConfig;
import com.fzzixun.shein.auth.dto.CallbackMsgDTO;
import com.fzzixun.shein.auth.service.AuthService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class CallbackKafkaConsumer {

    private final ParammConfig paramConfig;
    private final NotifyMicroService notifyMicroService;
    private final AuthService authService;

    @KafkaListener(topics = "#{'${kafka.listener_topics}'.split(',')}", groupId = "${shein.groupid}")
    public void notifyListen(List<ConsumerRecord<?, String>> records, Consumer<?, ?> consumer) {
        if (Boolean.TRUE.equals(paramConfig.getLogDebug())) {
            log.info("record value: {}", records.size());
        }
        try {
            for (ConsumerRecord<?, String> record : records) {
                if (Boolean.TRUE.equals(paramConfig.getLogDebug())) {
                    log.info("record partition={} offset={}", record.partition(), record.offset());
                }
                CallbackMsgDTO callbackMsgDTO = JSON.parseObject(record.value(), CallbackMsgDTO.class);
                String type = callbackMsgDTO.getType();
                String openKeyId = callbackMsgDTO.getOpenKeyId();
                String appId = callbackMsgDTO.getAppId();
                String callbackUrl = authService.getCallbackUrl(appId, type, openKeyId);
                if (StringUtils.isEmpty(callbackUrl)) {
                    log.error("未配置回调地址, openKeyId:{}", openKeyId);
                    continue;
                }
                sendCallback("shein_" + type + "_" + openKeyId, callbackUrl, record.value(), Constant.CALLBACK_MESSAGE_TYPE_SHEIN_EVENT);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            consumer.commitAsync();
        }
    }

    public void sendCallback(String appKey, String callbackUrl, String data, Integer messageType) {
        NotifyWithUrlPo notifyPo = new NotifyWithUrlPo();
        notifyPo.setAppKey(appKey);
        notifyPo.setCompanyId(0L);
        notifyPo.setData(data);
        notifyPo.setMessageType(messageType);
        notifyPo.setCallbackUrl(callbackUrl);
        if (StringUtils.isEmpty(notifyPo.getCallbackUrl())) {
            log.error("未配置回调地址, appKey:{}", appKey);
            return;
        }
        try {
            log.info("推送通知, appKey:{}, {}", appKey, JSON.toJSONString(notifyPo));
            notifyMicroService.sentNotifyWithCallbackUrl(notifyPo);
        } catch (FeignException.BadRequest badRequest) {
            log.error("推送通知失败, appKey:{}, {}", appKey, badRequest.getMessage());
        } catch (Exception e) {
            log.error("推送通知异常, appKey:{},", appKey, e);
        }
    }

}
