package com.fzzixun.shein.auth.domain.repository;

import com.fzzixun.shein.auth.domain.entity.SheinSellerAuth;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;


public interface SheinSellerAuthRepository extends JpaRepository<SheinSellerAuth, Long>,
        JpaSpecificationExecutor<SheinSellerAuth> {

    Optional<SheinSellerAuth> findFirstByOpenKeyId(String openKeyId);

}