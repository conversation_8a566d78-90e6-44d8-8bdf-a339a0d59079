package com.fzzixun.shein.auth.controller;

import com.alibaba.fastjson.JSON;
import com.fzzixun.proxy.common.infrastructure.annotation.WebLog;
import com.fzzixun.shein.auth.config.ParammConfig;
import com.fzzixun.shein.auth.config.SheinConfig;
import com.fzzixun.shein.auth.dto.CallbackMsgDTO;
import com.fzzixun.shein.auth.facade.enums.ManageType;
import com.fzzixun.shein.auth.producer.KafkaOpt;
import com.fzzixun.shein.auth.utils.AESTools;
import com.fzzixun.shein.auth.utils.ApiSignUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;

@Slf4j
@WebLog
@RequestMapping("/rest/v1/callback")
@RestController
@Api(tags = "事件推送")
@RequiredArgsConstructor
public class CallbackController {

    private final SheinConfig sheinConfig;
    private final KafkaOpt kafkaOpt;
    private final ParammConfig parammConfig;
    /**
     * 供应商openKey，用于识别erp用户
     */
    public static final String X_LT_OPENKEY_ID = "x-lt-openKeyId";
    /**
     * 时间戳请求头
     */
    public static final String X_LT_TIMESTAMP = "x-lt-timestamp";
    /**
     * 签名请求头
     */
    public static final String X_LT_SIGNATURE = "x-lt-signature";
    /**
     * 签名的appid请求头
     */
    public static final String X_LT_KEY_EVENT_APP_ID = "x-lt-appid";
    /**
     * 事件code请求头
     */
    public static final String X_LT_KEY_EVENT_CODE = "x-lt-eventcode";

    @PostMapping("/receive")
    @ApiOperation("回调接收-半托管")
    public boolean receiveSemiManaged(HttpServletRequest request, @RequestParam("eventData") String eventData) {
       return this.receiveCallback(request, eventData, ManageType.SemiManaged.getType(), sheinConfig.getSemiManagedAppSecret());
    }
    @PostMapping("/receive/fullManaged")
    @ApiOperation("回调接收-全托管")
    public boolean receiveFullManaged(HttpServletRequest request, @RequestParam("eventData") String eventData) {
        return this.receiveCallback(request, eventData, ManageType.FullManaged.getType(), sheinConfig.getFullManagedAppSecret());
    }

    @PostMapping("/receive/selfOperated")
    @ApiOperation("回调接收-自运营")
    public boolean receiveSelfOperated(HttpServletRequest request, @RequestParam("eventData") String eventData) {
        return this.receiveCallback(request, eventData, ManageType.SelfOperated.getType(), sheinConfig.getSelfOperatedAppSecret());
    }

    private boolean receiveCallback(HttpServletRequest request, String eventData, String type, String appSecret) {
        if (parammConfig.getLogDebug()) {
            Enumeration<String> headerNames = request.getHeaderNames();
            HashMap<String, String> headerMap = new HashMap<>();
            while (headerNames.hasMoreElements()) {
                String next = headerNames.nextElement();
                headerMap.put(next, request.getHeader(next));
            }
            log.info("收到openAPI-callback请求,header:{}", headerMap);
        }

        boolean verifySign = verifySignForHttp(request, appSecret);
        log.info("收到openAPI-callback请求，密文message--{},签名结果-{}", eventData, verifySign);
        String decryptData = AESTools.decrypt(eventData, appSecret);
        log.info("收到openAPI-callback请求，明文message--{}", decryptData);
        String appId = request.getHeader(X_LT_KEY_EVENT_APP_ID);
        String openKeyId = request.getHeader(X_LT_OPENKEY_ID);
        String eventCode = request.getHeader(X_LT_KEY_EVENT_CODE);
        CallbackMsgDTO callbackMsgDTO = new CallbackMsgDTO(appId, type, openKeyId, eventCode, decryptData);
        kafkaOpt.send(JSON.toJSONString(callbackMsgDTO));
        return verifySign;
    }

    public static boolean verifySignForHttp(HttpServletRequest request, String secretKey) {
        // 获取请求头
        String openKey = null;
        if (request.getHeader(X_LT_KEY_EVENT_APP_ID) != null) {
            openKey = request.getHeader(X_LT_KEY_EVENT_APP_ID);
        } else {
            openKey = request.getHeader(X_LT_OPENKEY_ID);
        }
        String timestamp = request.getHeader(X_LT_TIMESTAMP);
        String signature = request.getHeader(X_LT_SIGNATURE);
        String requestPath = "/shein-auth" + request.getRequestURI();
        return ApiSignUtil.verifySign(signature, openKey, secretKey, requestPath, timestamp);
    }
}
